import { Injectable } from '@nestjs/common';
import { StructuredTool, ToolSchemaBase } from '@langchain/core/tools';
import { z } from 'zod';
import OpenAI from 'openai';

export interface WebSearchOptions {
  search_context_size?: 'low' | 'medium' | 'high';
  user_location?: {
    type: 'approximate';
    approximate: {
      country?: string; // Two-letter ISO country code (e.g., "US", "GB")
      city?: string; // Free text string (e.g., "Minneapolis")
      region?: string; // Free text string (e.g., "Minnesota")
      timezone?: string; // IANA timezone (e.g., "America/Chicago")
    };
  };
}

export interface WebSearchResponse {
  content: string;
  annotations: Array<{
    type: 'url_citation';
    url_citation: {
      end_index: number;
      start_index: number;
      title: string;
      url: string;
    };
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export interface WebSearchError {
  keyId: number;
  provider: string;
  error: string;
  statusCode?: number;
  retryAfter?: number;
  attemptNumber: number;
}

@Injectable()
export class WebSearchTool extends StructuredTool {
  name: string;
  description: string;
  schema: ToolSchemaBase;
  openai: OpenAI;

  constructor() {
    super();
    this.name = 'web_search_and_cite';
    this.description =
      'Perform web search using OpenAI search-enabled models with real-time information and optional geographic refinement. Use this tool to find current information, news, facts, or answers that require up-to-date web data. Results include citations from reliable sources. When presenting data from this tool, please include the sources in the answer.';
    this.schema = z.object({
      query: z
        .string()
        .min(1, 'Search query cannot be empty')
        .max(1000, 'Search query too long (max 1000 characters)')
        .describe('Search query to perform web search'),
      searchContext: z
        .enum(['low', 'medium', 'high'])
        .optional()
        .describe('Search context size for web search results (optional)'),
      userLocation: z
        .object({
          country: z
            .string()
            .length(2)
            .optional()
            .describe('Two-letter ISO country code (e.g., "US", "GB")'),
          city: z
            .string()
            .optional()
            .describe('City name for localized search (e.g., "Minneapolis")'),
          region: z
            .string()
            .optional()
            .describe(
              'Region/state name for localized search (e.g., "Minnesota")',
            ),
          timezone: z
            .string()
            .optional()
            .describe(
              'IANA timezone for localized search (e.g., "America/Chicago")',
            ),
        })
        .optional()
        .describe('User location for geographically refined search results'),
    });
    this.openai = new OpenAI({
      maxRetries: 5,
    });
  }

  async _call(input: any, config: any) {
    const { query, searchContext, userLocation } = input;
    const options: WebSearchOptions = {};

    if (searchContext) {
      options.search_context_size = searchContext;
    }

    if (userLocation) {
      options.user_location = {
        type: 'approximate',
        approximate: {
          ...(userLocation.country && { country: userLocation.country }),
          ...(userLocation.city && { city: userLocation.city }),
          ...(userLocation.region && { region: userLocation.region }),
          ...(userLocation.timezone && { timezone: userLocation.timezone }),
        },
      };
    }
    const result = await this.callWebSearchApi(query, options);
    let response = [result.content];
    if (result.annotations && result.annotations.length > 0) {
      response.push('Sources:');
      result.annotations.forEach((annotation, index) => {
        if (annotation.type === 'url_citation') {
          response.push(
            `${index + 1}. ${annotation.url_citation.title}: ${annotation.url_citation.url}`,
          );
        }
      });
    }
    return response.join('\n\n');
  }

  private async callWebSearchApi(
    query: string,
    options: WebSearchOptions,
  ): Promise<WebSearchResponse> {
    const requestOptions: any = {
      model: 'gpt-4o-mini-search-preview',
      messages: [
        {
          role: 'user',
          content: query,
        },
      ],
    };

    // Add web search options if provided
    if (options) {
      requestOptions.web_search_options = {};

      if (options.search_context_size) {
        requestOptions.web_search_options.search_context_size =
          options.search_context_size;
      }

      if (options.user_location) {
        requestOptions.web_search_options.user_location = options.user_location;
      }
    } else {
      // Default empty web_search_options to enable web search
      requestOptions.web_search_options = {};
    }
    const completion =
      await this.openai.chat.completions.create(requestOptions);

    const choice = completion.choices[0];
    if (!choice) {
      throw new Error('No response choice returned from OpenAI API');
    }

    return {
      content: choice.message.content || '',
      annotations: choice.message.annotations || [],
      usage: {
        prompt_tokens: completion.usage?.prompt_tokens || 0,
        completion_tokens: completion.usage?.completion_tokens || 0,
        total_tokens: completion.usage?.total_tokens || 0,
      },
    };
  }
}
