import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In, IsNull } from 'typeorm';
import { ExternalConversationMessage } from '../entities/external-conversation-message.entity';
import { MessageRole, Platform } from '../../../shared/enums';
import {
  AttachmentType,
  KnowledgeFileWithMessageId,
  MediaDataWithMessageId,
  ReplyToMessageData,
  ReplyToMessagesContext,
} from '../../../shared/interfaces';

/**
 * Message Operations Service for External Platforms
 *
 * Handles database operations for ExternalConversationMessage entities.
 * Manages external platform conversations and message processing workflows.
 *
 * Key Features:
 * - External conversation message management
 * - Unprocessed message fetching for any external platform
 * - Reply-to message context building
 * - Message processing status tracking
 * - Platform-agnostic message handling
 */
@Injectable()
export class ExternalMessageOperationsService {
  private readonly logger = new Logger(ExternalMessageOperationsService.name);

  constructor(
    @InjectRepository(ExternalConversationMessage)
    private readonly messageRepository: Repository<ExternalConversationMessage>,
  ) {}

  /**
   * Fetch unprocessed user messages for external platform
   * Gets messages from external conversation that haven't been processed yet
   *
   * @param threadId - Thread ID (maps to externalCustomerPlatformDataId)
   * @param platform - Platform to filter messages (WEBSITE, ZALO, etc.)
   * @returns Array of unprocessed user messages
   */
  async fetchUnprocessedUserMessages(
    threadId: string,
    platform: Platform,
  ): Promise<ExternalConversationMessage[]> {
    this.logger.debug(
      `Fetching unprocessed user messages for thread: ${threadId}`,
    );

    try {
      const messages = await this.messageRepository.find({
        where: {
          externalCustomerPlatformDataId: threadId,
          role: MessageRole.USER,
          platform: platform,
          processed: false,
          deletedAt: IsNull(),
        },
        order: {
          createdAt: 'ASC',
        },
      });

      this.logger.debug(
        `Found ${messages.length} unprocessed user messages for thread: ${threadId}`,
        {
          threadId,
          messageCount: messages.length,
          messageIds: messages.map((m) => m.id),
        },
      );

      return messages;
    } catch (error) {
      this.logger.error(
        `Failed to fetch unprocessed user messages for thread: ${threadId}`,
        {
          threadId,
          error: error.message,
          stack: error.stack,
        },
      );
      throw error;
    }
  }

  /**
   * Fetch reply-to messages by their IDs
   * Gets the original messages that users are replying to
   *
   * @param replyToMessageIds - Array of message IDs being replied to
   * @param platform - Platform to filter messages (WEBSITE, ZALO, etc.)
   * @returns Array of reply-to messages
   */
  async fetchReplyToMessages(
    replyToMessageIds: string[],
    platform: Platform,
  ): Promise<ExternalConversationMessage[]> {
    if (!replyToMessageIds || replyToMessageIds.length === 0) {
      this.logger.debug('No reply-to message IDs provided');
      return [];
    }

    this.logger.debug(`Fetching reply-to messages`, {
      replyToMessageIds,
      count: replyToMessageIds.length,
    });

    try {
      const messages = await this.messageRepository.find({
        where: {
          id: In(replyToMessageIds),
          platform: platform,
          deletedAt: IsNull(),
        },
        order: {
          createdAt: 'ASC',
        },
      });

      this.logger.debug(`Found ${messages.length} reply-to messages`, {
        requestedIds: replyToMessageIds,
        foundCount: messages.length,
        foundIds: messages.map((m) => m.id),
      });

      return messages;
    } catch (error) {
      this.logger.error(`Failed to fetch reply-to messages`, {
        replyToMessageIds,
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  /**
   * Build reply-to messages context for external platform
   * Creates context data for messages being replied to
   *
   * @param replyToMessages - Array of messages being replied to
   * @param replyToSpecificImages - Images specific to reply-to messages
   * @param replyToSpecificKnowledgeFiles - Knowledge files specific to reply-to messages
   * @returns Reply-to messages context
   */
  async buildReplyToMessagesContext(param: {
    replyToMessages: ExternalConversationMessage[];
    replyToSpecificImages: MediaDataWithMessageId[];
    replyToSpecificKnowledgeFiles: KnowledgeFileWithMessageId[];
  }): Promise<ReplyToMessagesContext> {
    const {
      replyToMessages,
      replyToSpecificImages,
      replyToSpecificKnowledgeFiles,
    } = param;
    this.logger.debug(`Building reply-to messages context`, {
      messageCount: replyToMessages.length,
      hasImages: !!replyToSpecificImages?.length,
    });

    const replyToMessagesContext: ReplyToMessagesContext = [];

    for (const replyToMessage of replyToMessages) {
      const replyToMessageData: ReplyToMessageData = {
        text: replyToMessage.text,
        attachments: {
          images: replyToSpecificImages.map((media) => {
            return {
              id: media.id,
              name: media.name,
              description: media.description,
              tags: media.tags,
              storageKey: media.storageKey,
              type: AttachmentType.IMAGE,
            };
          }),
          knowledgeFiles: replyToSpecificKnowledgeFiles.map((kf) => {
            return {
              id: kf.id,
              name: kf.name,
              fileId: kf.fileId,
              type: AttachmentType.KNOWLEDGE_FILE,
            };
          }),
        },
      };
      replyToMessagesContext.push(replyToMessageData);
    }

    this.logger.debug(`Built reply-to context`, {
      contextCount: replyToMessagesContext.length,
      totalImageAttachments: replyToMessagesContext.reduce(
        (sum, ctx) => sum + (ctx.attachments.images?.length || 0),
        0,
      ),
    });

    return replyToMessagesContext;
  }

  /**
   * Mark messages as processed after successful execution
   * Updates the processed flag to prevent reprocessing
   *
   * @param messageIds - Array of message IDs to mark as processed
   * @param threadId - Thread ID for logging context
   * @param platform - Platform to filter messages (WEBSITE, ZALO, etc.)
   */
  async markMessagesAsProcessed(
    messageIds: string[],
    threadId: string,
    platform: Platform,
  ): Promise<void> {
    if (!messageIds || messageIds.length === 0) {
      this.logger.debug('No message IDs provided for marking as processed');
      return;
    }

    this.logger.debug(`Marking ${messageIds.length} messages as processed`, {
      threadId,
      messageIds,
    });

    try {
      const updateResult = await this.messageRepository.update(
        {
          id: In(messageIds),
          platform: platform,
        },
        {
          processed: true,
          updatedAt: Date.now().toString(),
        },
      );

      this.logger.debug(`Successfully marked messages as processed`, {
        threadId,
        messageIds,
        affectedRows: updateResult.affected,
      });
    } catch (error) {
      this.logger.error(`Failed to mark messages as processed`, {
        threadId,
        messageIds,
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  /**
   * Update message text with validation for external platform
   * Used for finalizing accumulated text from executor agent
   *
   * @param messageId - Message ID to update
   * @param text - New text content
   * @param platform - Platform to filter messages (WEBSITE, ZALO, etc.)
   * @param context - Context information for logging
   * @returns Promise<boolean> - Success/failure status
   */
  async updateMessageTextWithValidation(
    messageId: string,
    text: string,
    platform: Platform,
    context: {
      runId: string;
      agentId: string;
      operation: string;
    },
  ): Promise<boolean> {
    this.logger.debug(`Updating message text with validation`, {
      messageId,
      textLength: text.length,
      context,
    });

    try {
      // Validate message exists and belongs to the specified platform
      const existingMessage = await this.messageRepository.findOne({
        where: {
          id: messageId,
          platform: platform,
        },
        select: ['id', 'text', 'role'],
      });

      if (!existingMessage) {
        this.logger.warn(`Message not found for update`, {
          messageId,
          context,
        });
        return false;
      }

      // Update the message text
      const updateResult = await this.messageRepository.update(
        {
          id: messageId,
          platform: platform,
        },
        {
          text: text,
          updatedAt: Date.now().toString(),
        },
      );

      if (updateResult.affected === 0) {
        this.logger.warn(`No rows affected when updating message`, {
          messageId,
          context,
        });
        return false;
      }

      this.logger.debug(`Successfully updated message text`, {
        messageId,
        textLength: text.length,
        textPreview: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
        context,
        affectedRows: updateResult.affected,
      });

      return true;
    } catch (error) {
      this.logger.error(`Failed to update message text`, {
        messageId,
        textLength: text.length,
        context,
        error: (error as Error).message,
        stack: (error as Error).stack,
      });
      return false; // Don't throw - let caller handle the failure
    }
  }
}
