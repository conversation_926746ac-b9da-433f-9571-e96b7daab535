import { Injectable, Logger } from '@nestjs/common';
import { fromPromise } from 'xstate';
import { WorkflowContext, NodeExecutionContext, DetailedNodeExecutionResult } from '../types';
import { DependencyResolverService } from '../services/dependency-resolver.service';
import { ConditionalConnectionService } from '../services/conditional-connection.service';
import { WorkflowStateManagerService } from '../services/workflow-state-manager.service';
import { NodeExecutorFactory } from '../executors/node-executor.factory';
import { AgentNodeDetectorService } from '../services/agent-node-detector.service';
import { LangGraphIntegrationService } from '../services/langgraph-integration.service';
import { WorkflowRepository } from '../../../repositories/workflow.repository';
import { NodeRepository } from '../../../repositories/node.repository';
import { ConnectionRepository } from '../../../repositories/connection.repository';
import { ExecutionRepository } from '../../../repositories/execution.repository';

/**
 * XState machine services implementation
 */
@Injectable()
export class MachineServicesProvider {
  private readonly logger = new Logger(MachineServicesProvider.name);

  constructor(
    private readonly dependencyResolver: DependencyResolverService,
    private readonly conditionalConnectionService: ConditionalConnectionService,
    private readonly stateManager: WorkflowStateManagerService,
    private readonly executorFactory: NodeExecutorFactory,
    private readonly agentDetector: AgentNodeDetectorService,
    private readonly langGraphIntegration: LangGraphIntegrationService,
    private readonly workflowRepository: WorkflowRepository,
    private readonly nodeRepository: NodeRepository,
    private readonly connectionRepository: ConnectionRepository,
    private readonly executionRepository: ExecutionRepository,
  ) {}

  /**
   * Get all machine services
   */
  getMachineServices() {
    return {
      // Workflow machine services
      loadWorkflowService: this.createLoadWorkflowService(),
      validateWorkflowService: this.createValidateWorkflowService(),
      monitorExecutionService: this.createMonitorExecutionService(),
      cleanupWorkflowService: this.createCleanupWorkflowService(),

      // Node execution machine services
      prepareExecutionService: this.createPrepareExecutionService(),
      validateNodeService: this.createValidateNodeService(),
      executeNodeService: this.createExecuteNodeService(),
      cleanupExecutionService: this.createCleanupExecutionService(),
    };
  }

  /**
   * Load workflow service
   */
  private createLoadWorkflowService() {
    return fromPromise(async ({ input }: { input: { workflowId: string; executionId: string; options?: { enableConditionalRouting?: boolean } } }) => {
      try {
        this.logger.debug(`Loading workflow: ${input.workflowId}`);

        // Load workflow definition
        const workflow = await this.workflowRepository.findById(input.workflowId);
        if (!workflow) {
          throw new Error(`Workflow not found: ${input.workflowId}`);
        }

        // Load nodes
        const nodes = await this.nodeRepository.findByWorkflowId(input.workflowId);
        if (nodes.length === 0) {
          throw new Error(`No nodes found for workflow: ${input.workflowId}`);
        }

        // Load connections
        const connections = await this.connectionRepository.findByWorkflowId(input.workflowId);

        // Analyze dependencies
        const dependencyAnalysis = await this.dependencyResolver.analyzeDependencies(nodes, connections);

        if (dependencyAnalysis.hasCircularDependencies) {
          throw new Error(`Circular dependencies detected in workflow: ${input.workflowId}`);
        }

        // Analyze conditional dependencies
        const conditionalAnalysis = await this.dependencyResolver.analyzeConditionalDependencies(nodes, connections);

        // Create node execution states
        const nodeStates = new Map();
        for (const node of nodes) {
          nodeStates.set(node.id, {
            id: node.id,
            status: 'pending',
            inputData: null,
            outputData: null,
            retryCount: 0,
            node,
          });
        }

        // Initialize conditional routing data
        const enableConditionalRouting = input.options?.enableConditionalRouting !== false;

        return {
          nodes: nodeStates,
          connections,
          dependencyGraph: dependencyAnalysis.graph,
          readyNodes: dependencyAnalysis.graph.rootNodes,
          waitingNodes: [],
          workflowSettings: workflow.settings,
          // Enhanced conditional routing support
          conditionalGraph: enableConditionalRouting ? conditionalAnalysis.conditionalGraph : undefined,
          enhancedConnections: enableConditionalRouting ? conditionalAnalysis.enhancedConnections : undefined,
          activeConnections: enableConditionalRouting ?
            conditionalAnalysis.enhancedConnections.filter(conn => !conn.isConditional) :
            undefined,
          completedNodes: new Map(),
          ifNodeResults: new Map(),
          switchNodeResults: new Map(),
        };

      } catch (error) {
        this.logger.error(`Failed to load workflow ${input.workflowId}:`, error);
        throw error;
      }
    });
  }

  /**
   * Validate workflow service
   */
  private createValidateWorkflowService() {
    return fromPromise(async ({ input }: { input: WorkflowContext }) => {
      try {
        this.logger.debug(`Validating workflow: ${input.workflowId}`);

        const errors: string[] = [];
        const warnings: string[] = [];

        // Validate basic structure
        if (!input.nodes || input.nodes.size === 0) {
          errors.push('Workflow must have at least one node');
        }

        if (!input.dependencyGraph) {
          errors.push('Workflow must have dependency graph');
        }

        // Validate each node
        for (const [nodeId, nodeState] of input.nodes.entries()) {
          if (!nodeState.node) {
            errors.push(`Node ${nodeId} missing node definition`);
            continue;
          }

          // Check if node has executor
          const hasExecutor = this.executorFactory.isSupported(nodeState.node.nodeDefinitionId || '');
          if (!hasExecutor) {
            errors.push(`No executor found for node ${nodeId} (${nodeState.node.nodeDefinitionId})`);
          }

          // Validate agent nodes
          if (nodeState.node.agentId) {
            const agentValidation = await this.agentDetector.detectAgentNode(nodeState.node);
            if (!agentValidation.validation.isValid) {
              errors.push(...agentValidation.validation.errors.map(err => `Node ${nodeId}: ${err}`));
            }
            warnings.push(...agentValidation.validation.warnings.map(warn => `Node ${nodeId}: ${warn}`));
          }
        }

        // Validate dependency graph
        const graphValidation = this.dependencyResolver.validateDependencyGraph(input.dependencyGraph);
        if (!graphValidation.isValid) {
          errors.push(...graphValidation.errors);
        }
        warnings.push(...graphValidation.warnings);

        return {
          isValid: errors.length === 0,
          errors,
          warnings,
        };

      } catch (error) {
        this.logger.error(`Failed to validate workflow ${input.workflowId}:`, error);
        throw error;
      }
    });
  }

  /**
   * Monitor execution service
   */
  private createMonitorExecutionService() {
    return fromPromise(async ({ input }: { input: WorkflowContext }) => {
      try {
        this.logger.debug(`Monitoring execution: ${input.executionId}`);

        // Create checkpoint periodically
        const checkpointInterval = setInterval(async () => {
          try {
            await this.stateManager.createAutoCheckpoint(
              input,
              {}, // Machine state would be passed here
              'periodic_checkpoint'
            );
          } catch (error) {
            this.logger.warn('Failed to create periodic checkpoint:', error);
          }
        }, 30000); // Every 30 seconds

        // Monitor for timeout
        const timeout = input.options?.timeout || 3600000; // 1 hour default
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => {
            clearInterval(checkpointInterval);
            reject(new Error('Workflow execution timeout'));
          }, timeout);
        });

        // Return cleanup function
        return {
          cleanup: () => {
            clearInterval(checkpointInterval);
          },
        };

      } catch (error) {
        this.logger.error(`Failed to monitor execution ${input.executionId}:`, error);
        throw error;
      }
    });
  }

  /**
   * Cleanup workflow service
   */
  private createCleanupWorkflowService() {
    return fromPromise(async ({ input }: { input: WorkflowContext }) => {
      try {
        this.logger.debug(`Cleaning up workflow: ${input.executionId}`);

        // Save final execution state
        await this.executionRepository.updateExecutionStatus(
          input.executionId,
          input.metadata.completedNodes === input.metadata.totalNodes ? 'completed' : 'failed'
        );

        // Cleanup old checkpoints
        await this.stateManager.cleanupOldSnapshots(input.executionId);

        // Clear any temporary data
        // Implementation specific cleanup

        return { success: true };

      } catch (error) {
        this.logger.error(`Failed to cleanup workflow ${input.executionId}:`, error);
        throw error;
      }
    });
  }

  /**
   * Prepare execution service
   */
  private createPrepareExecutionService() {
    return fromPromise(async ({ input }: { 
      input: { 
        nodeContext: NodeExecutionContext; 
        config?: any; 
      } 
    }) => {
      try {
        const { nodeContext, config } = input;
        this.logger.debug(`Preparing execution for node: ${nodeContext.node.id}`);

        // Prepare input data from previous nodes
        const inputData = this.prepareNodeInputData(nodeContext);

        // Detect if this is an agent node
        const isAgentNode = await this.agentDetector.detectAgentNode(nodeContext.node);

        // Prepare execution context with extended properties
        const preparedContext: NodeExecutionContext & {
          isAgentNode?: boolean;
          agentConfig?: any;
        } = {
          ...nodeContext,
          inputData,
          isAgentNode: isAgentNode.isAgentNode,
          agentConfig: isAgentNode.agentConfig,
        };

        return {
          nodeContext: preparedContext,
          config: config || {},
        };

      } catch (error) {
        this.logger.error(`Failed to prepare execution for node ${input.nodeContext.node.id}:`, error);
        throw error;
      }
    });
  }

  /**
   * Validate node service
   */
  private createValidateNodeService() {
    return fromPromise(async ({ input }: { 
      input: { 
        nodeContext: NodeExecutionContext; 
        config?: any; 
      } 
    }) => {
      try {
        const { nodeContext } = input;
        this.logger.debug(`Validating node: ${nodeContext.node.id}`);

        const errors: string[] = [];
        const warnings: string[] = [];

        // Get appropriate executor
        const executor = this.executorFactory.createExecutor(
          nodeContext.node.nodeDefinitionId || ''
        );

        if (!executor) {
          errors.push(`No executor found for node type: ${nodeContext.node.nodeDefinitionId}`);
        } else {
          // Validate using executor
          const validation = await executor.validateInput({
            node: nodeContext.node,
            nodeDefinition: nodeContext.nodeDefinition,
            inputData: nodeContext.inputData,
            previousOutputs: nodeContext.previousOutputs,
            executionId: nodeContext.executionId,
            workflowId: nodeContext.workflowId,
            userId: nodeContext.userId,
            triggerData: nodeContext.triggerData,
            headers: nodeContext.headers || {},
            options: nodeContext.options,
          });

          if (!validation.isValid) {
            errors.push(...validation.errors.map(err => err.message));
          }
          warnings.push(...validation.warnings.map(warn => warn.message));
        }

        return {
          isValid: errors.length === 0,
          errors,
          warnings,
        };

      } catch (error) {
        this.logger.error(`Failed to validate node ${input.nodeContext.node.id}:`, error);
        throw error;
      }
    });
  }

  /**
   * Execute node service
   */
  private createExecuteNodeService() {
    return fromPromise(async ({ input }: { 
      input: { 
        nodeContext: NodeExecutionContext; 
        config?: any; 
      } 
    }) => {
      try {
        const { nodeContext, config } = input;
        this.logger.debug(`Executing node: ${nodeContext.node.id}`);

        let result: DetailedNodeExecutionResult;

        // Check if this is an agent node
        const extendedContext = nodeContext as NodeExecutionContext & { isAgentNode?: boolean };
        if (extendedContext.isAgentNode && nodeContext.node.agentId) {
          // Execute through LangGraph integration
          result = await this.langGraphIntegration.executeAgentNode(nodeContext);
        } else {
          // Execute through regular executor
          const executor = this.executorFactory.createExecutor(
            nodeContext.node.nodeDefinitionId || ''
          );

          if (!executor) {
            throw new Error(`No executor found for node type: ${nodeContext.node.nodeDefinitionId}`);
          }

          result = await executor.execute({
            node: nodeContext.node,
            nodeDefinition: nodeContext.nodeDefinition,
            inputData: nodeContext.inputData,
            previousOutputs: nodeContext.previousOutputs,
            executionId: nodeContext.executionId,
            workflowId: nodeContext.workflowId,
            userId: nodeContext.userId,
            triggerData: nodeContext.triggerData,
            headers: nodeContext.headers || {},
            options: nodeContext.options,
          }, config);
        }

        return result;

      } catch (error) {
        this.logger.error(`Failed to execute node ${input.nodeContext.node.id}:`, error);
        
        return {
          success: false,
          error,
          shouldRetry: await this.shouldRetryNodeExecution(error, input.nodeContext),
          metadata: {
            executionTime: 0,
            logs: [`Node execution failed: ${error.message}`],
          },
        } as DetailedNodeExecutionResult;
      }
    });
  }

  /**
   * Cleanup execution service
   */
  private createCleanupExecutionService() {
    return fromPromise(async ({ input }: { input: any }) => {
      try {
        this.logger.debug(`Cleaning up node execution for input:`, input);

        // Cleanup any temporary resources
        // Implementation specific cleanup

        return { success: true };

      } catch (error) {
        this.logger.error(`Failed to cleanup node execution:`, error);
        throw error;
      }
    });
  }

  // Helper methods

  private prepareNodeInputData(context: NodeExecutionContext): any {
    // Combine trigger data with previous node outputs
    const inputData = {
      ...context.triggerData,
    };

    // Add outputs from previous nodes
    for (const [nodeId, output] of context.previousOutputs.entries()) {
      inputData[`node_${nodeId}`] = output;
    }

    return inputData;
  }

  private async shouldRetryNodeExecution(error: any, context: NodeExecutionContext): Promise<boolean> {
    // Check if the executor supports retry for this error
    const executor = this.executorFactory.createExecutor(
      context.node.nodeDefinitionId || ''
    );

    if (!executor) {
      return false;
    }

    // Use executor's retry logic if available
    // This would be implemented in the base executor
    this.logger.debug(`Checking retry for error: ${error?.message || 'Unknown error'}`);
    return false; // Default to no retry
  }
}
