import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ExternalConversationMessage } from '../../../domains/external/entities/external-conversation-message.entity';
import { ZaloJobData } from '../interfaces/zalo-info.interface';
import { StreamEvent as ExternalStreamEvent } from '../../../shared/interfaces/stream-event.interface';
import {
  ExclusiveTags,
  MessageRole,
  Platform,
  StreamEventType,
} from '../../../shared/enums';
import { StreamEvent } from '@langchain/core/tracers/log_stream';
import { ZaloConsultationService } from 'src/shared/services/zalo';
import { EncryptionService } from 'src/shared/services/encryption/encryption.service';
import { env } from 'src/config';
import { backOff } from 'exponential-backoff';
import { ExternalMessageOperationsService } from '../../../domains/external/services';
import { TokenUsageCollector } from '../../../shared/utils';

/**
 * Zalo Event Processor Service
 *
 * Handles dynamic message creation during PlannerExecutorGraph execution.
 * Transforms LangGraph events into Zalo-specific operations instead of Redis streaming.
 *
 * Key Features:
 * - Zalo user conversation support (ExternalConversationMessage)
 * - Executor agent message creation (planner is internal)
 * - Event transformation from LangGraph to Zalo operations
 * - No Redis streaming - uses events for Zalo API callbacks
 * - Integration with Zalo OA messaging APIs
 * - No tool call interrupts (PlannerExecutorGraph doesn't need approvals)
 */
@Injectable()
export class ZaloEventProcessorService {
  private readonly logger = new Logger(ZaloEventProcessorService.name);

  /**
   * Function map for LangGraph event handlers
   * Different from website - focuses on Zalo API operations instead of Redis streaming
   */
  private readonly eventHandlers = new Map<
    string,
    (
      langGraphEvent: StreamEvent,
      jobData: ZaloJobData,
      streamKey: string,
      currentMessageId: string | null,
    ) => Promise<{ newMessageId?: string | null; messageUpdated?: boolean }>
  >([
    ['on_chat_model_start', this.handleChatModelStart.bind(this)],
    ['on_chat_model_stream', this.handleChatModelStream.bind(this)],
    ['on_chat_model_end', this.handleChatModelEnd.bind(this)],
    ['on_tool_start', this.handleToolStart.bind(this)],
    ['on_tool_end', this.handleToolEnd.bind(this)],
    // Run lifecycle events
    [StreamEventType.RUN_STARTED, this.handleRunStarted.bind(this)],
    [StreamEventType.RUN_COMPLETE, this.handleRunComplete.bind(this)],
    [StreamEventType.RUN_CANCELLED, this.handleRunCancelled.bind(this)],
    [StreamEventType.RUN_ERROR, this.handleRunError.bind(this)],
  ]);

  constructor(
    @InjectRepository(ExternalConversationMessage)
    private readonly messageRepository: Repository<ExternalConversationMessage>,
    private readonly consultantService: ZaloConsultationService,
    private readonly encryptionService: EncryptionService,
    private readonly messageOperations: ExternalMessageOperationsService,
  ) {}

  /**
   * Process LangGraph events and transform them into Zalo operations
   * Handles message creation dynamically during streaming for Zalo users
   *
   * @param langGraphEvent - Raw LangGraph event
   * @param jobData - Zalo job data with user context
   * @param streamKey - Stream key (not used for Redis but kept for compatibility)
   * @param currentMessageId - Current message ID (null if no active message)
   * @returns Object with newMessageId if a new message was created
   */
  async processLangGraphEvent(
    langGraphEvent: StreamEvent | Record<string, unknown>,
    jobData: ZaloJobData,
    streamKey: string,
    currentMessageId: string | null,
  ): Promise<{ newMessageId?: string | null; messageUpdated?: boolean }> {
    const eventType = (langGraphEvent as Record<string, unknown>)
      .event as string;

    this.logger.debug(`Processing LangGraph event: ${eventType}`, {
      eventType,
      currentMessageId,
      runId: jobData.runId,
      threadId: jobData.threadId,
      zaloUserId: jobData.humanInfo.zaloUser.id,
      oaId: jobData.humanInfo.zaloInfo.oaId,
    });

    try {
      // Use function map for cleaner event handling
      const handler = this.eventHandlers.get(eventType);

      if (handler) {
        return await handler(
          langGraphEvent as StreamEvent,
          jobData,
          streamKey,
          currentMessageId,
        );
      } else {
        this.logger.debug(`Unhandled LangGraph event type: ${eventType}`);
        return {};
      }
    } catch (error) {
      this.logger.error(`Error processing LangGraph event: ${eventType}`, {
        error: error.message,
        stack: error.stack,
        runId: jobData.runId,
        threadId: jobData.threadId,
      });
      return {};
    }
  }

  /**
   * Handle chat model start event - create new message for executor agent
   */
  private async handleChatModelStart(
    langGraphEvent: StreamEvent,
    jobData: ZaloJobData,
    streamKey: string,
    currentMessageId: string | null,
  ): Promise<{ newMessageId?: string | null; messageUpdated?: boolean }> {
    this.logger.debug('Handling chat model start', {
      runId: jobData.runId,
      currentMessageId,
    });

    // TODO: implement proper logic
    // Only create messages for executor agent (main agent), not planner
    const isExecutorAgent = this.isExecutorAgent(langGraphEvent, jobData);
    if (!isExecutorAgent) {
      return {};
    }

    // Create new message for executor agent only
    const newMessageId = await this.createNewMessage(jobData);

    this.logger.debug('Created new Zalo message for executor agent', {
      messageId: newMessageId,
      agentId: jobData.mainAgentId,
      threadId: jobData.threadId,
    });

    return { newMessageId };
  }

  /**
   * Handle chat model stream event - accumulate content in existing message
   * Even though Zalo doesn't stream to client, we need to accumulate text for final message update
   */
  private async handleChatModelStream(
    langGraphEvent: StreamEvent,
    jobData: ZaloJobData,
    streamKey: string,
    currentMessageId: string | null,
  ): Promise<{ newMessageId?: string | null; messageUpdated?: boolean }> {
    if (!currentMessageId) {
      return {};
    }
    // zalo doesn't stream to client, and we don't need to accumulate text

    return { messageUpdated: false }; // Don't clear accumulated text yet
  }

  /**
   * Handle chat model end event - finalize message content
   */
  private async handleChatModelEnd(
    langGraphEvent: StreamEvent,
    jobData: ZaloJobData,
    streamKey: string,
    currentMessageId: string | null,
  ): Promise<{ newMessageId?: string | null; messageUpdated?: boolean }> {
    if (!currentMessageId) {
      return {};
    }
    if (!this.isExecutorAgent(langGraphEvent, jobData)) {
      this.logger.debug(
        'Chat model end event is from planner agent - skipping message finalization',
      );
      return {};
    }

    this.logger.debug('Finalizing Zalo message content', {
      messageId: currentMessageId,
      runId: jobData.runId,
    });

    // Get final content from LangGraph event
    const finalContent =
      (langGraphEvent.data as any).output?.content ||
      (langGraphEvent.data as any).output?.text ||
      '';

    if (!finalContent) {
      this.logger.warn(
        `No final content provided for message ${currentMessageId} in run ${jobData.runId}`,
      );
      return { newMessageId: null, messageUpdated: false };
    }
    // Update message in database with final content using external message operations
    const updateSuccess =
      await this.messageOperations.updateMessageTextWithValidation(
        currentMessageId,
        finalContent,
        Platform.ZALO,
        {
          runId: jobData.runId,
          agentId: jobData.mainAgentId,
          operation: 'finalize_message',
        },
      );

    if (!updateSuccess) {
      this.logger.warn(
        `Failed to update message ${currentMessageId} with final content`,
        {
          runId: jobData.runId,
          messageId: currentMessageId,
          contentLength: finalContent.length,
        },
      );
      return {};
    }

    // Send message to Zalo OA after successful database update
    try {
      // Decrypt to get the access token
      const { accessToken, refreshToken } = this.encryptionService.decrypt<{
        accessToken: string;
        refreshToken: string;
      }>(
        jobData.humanInfo.zaloInfo.secretKey,
        env.keyPairEncryption.KEY_PAIR_PRIVATE_KEY,
        jobData.humanInfo.zaloInfo.encryptedConfig,
      );

      // Send to Zalo with retry logic
      await backOff(
        async () => {
          await this.consultantService.sendConsultationTextMessage(
            accessToken,
            jobData.humanInfo.zaloUser.zaloUserId,
            finalContent,
          );
        },
        {
          maxDelay: 10000, // 10 seconds max delay
          numOfAttempts: 5, // Retry up to 5 times
          startingDelay: 1000, // Start with 1 second delay
          retry: (error) => {
            this.logger.warn(
              `Retrying Zalo message send due to error: ${error.message}`,
              {
                runId: jobData.runId,
                messageId: currentMessageId,
              },
            );
            return true; // Always retry on error
          },
        },
      );

      this.logger.debug(
        `Successfully sent message to Zalo and updated database for message ${currentMessageId}`,
        {
          runId: jobData.runId,
          messageId: currentMessageId,
          contentLength: finalContent.length,
          zaloUserId: jobData.humanInfo.zaloUser.id,
        },
      );
    } catch (error) {
      this.logger.error(
        `Failed to send message to Zalo for message ${currentMessageId}:`,
        {
          runId: jobData.runId,
          messageId: currentMessageId,
          error: (error as Error).message,
          stack: (error as Error).stack,
        },
      );
      // Don't throw - Zalo sending failure shouldn't crash the run
      // Message is already saved in database
    }

    return { newMessageId: null, messageUpdated: true };
  }

  /**
   * Handle tool start event - log tool execution for Zalo context
   */
  private async handleToolStart(
    langGraphEvent: StreamEvent,
    jobData: ZaloJobData,
    streamKey: string,
    currentMessageId: string | null,
  ): Promise<{ newMessageId?: string | null; messageUpdated?: boolean }> {
    const toolName = (langGraphEvent.data as any)?.input?.tool || 'unknown';
    this.logger.debug('Tool execution started in Zalo context', {
      toolName,
      runId: jobData.runId,
      oaId: jobData.humanInfo.zaloInfo.oaId,
    });

    // Could trigger Zalo-specific tool operations here
    return {};
  }

  /**
   * Handle tool end event - process tool results for Zalo
   */
  private async handleToolEnd(
    langGraphEvent: StreamEvent,
    jobData: ZaloJobData,
    streamKey: string,
    currentMessageId: string | null,
  ): Promise<{ newMessageId?: string | null; messageUpdated?: boolean }> {
    const toolName = (langGraphEvent.data as any)?.output?.tool || 'unknown';
    this.logger.debug('Tool execution completed in Zalo context', {
      toolName,
      runId: jobData.runId,
      oaId: jobData.humanInfo.zaloInfo.oaId,
    });

    // Could process tool results for Zalo API integration here
    return {};
  }

  /**
   * Handle run started event - Zalo-specific processing start
   */
  private async handleRunStarted(
    langGraphEvent: StreamEvent,
    jobData: ZaloJobData,
    streamKey: string,
    currentMessageId: string | null,
  ): Promise<{ newMessageId?: string | null; messageUpdated?: boolean }> {
    this.logger.log(`Zalo AI processing started for run: ${jobData.runId}`, {
      oaId: jobData.humanInfo.zaloInfo.oaId,
      zaloUserId: jobData.humanInfo.zaloUser.id,
    });

    // Could trigger Zalo OA status updates here
    return {};
  }

  /**
   * Handle run complete event - Zalo-specific processing completion
   */
  private async handleRunComplete(
    langGraphEvent: StreamEvent,
    jobData: ZaloJobData,
    streamKey: string,
    currentMessageId: string | null,
  ): Promise<{ newMessageId?: string | null; messageUpdated?: boolean }> {
    const totalCost = (langGraphEvent as any).totalCost || 0;
    this.logger.log(`Zalo AI processing completed for run: ${jobData.runId}`, {
      totalCost,
      oaId: jobData.humanInfo.zaloInfo.oaId,
      zaloUserId: jobData.humanInfo.zaloUser.id,
    });

    // Could trigger Zalo OA success notifications here
    return {};
  }

  /**
   * Handle run cancelled event - Zalo-specific cancellation
   */
  private async handleRunCancelled(
    langGraphEvent: StreamEvent,
    jobData: ZaloJobData,
    streamKey: string,
    currentMessageId: string | null,
  ): Promise<{ newMessageId?: string | null; messageUpdated?: boolean }> {
    const reason = (langGraphEvent as any).reason || 'unknown';
    this.logger.log(`Zalo AI processing cancelled for run: ${jobData.runId}`, {
      reason,
      oaId: jobData.humanInfo.zaloInfo.oaId,
    });

    // Could trigger Zalo OA cancellation notifications here
    return {};
  }

  /**
   * Handle run error event - Zalo-specific error handling
   */
  private async handleRunError(
    langGraphEvent: StreamEvent,
    jobData: ZaloJobData,
    streamKey: string,
    currentMessageId: string | null,
  ): Promise<{ newMessageId?: string | null; messageUpdated?: boolean }> {
    const error = (langGraphEvent as any).error || {};
    this.logger.error(`Zalo AI processing error for run: ${jobData.runId}`, {
      errorType: error.type,
      errorMessage: error.message,
      oaId: jobData.humanInfo.zaloInfo.oaId,
    });

    // Could trigger Zalo OA error notifications here
    return {};
  }

  private isExecutorAgent(
    langGraphEvent: StreamEvent,
    jobData: ZaloJobData,
  ): boolean {
    const metadata = langGraphEvent.metadata;

    // 1. A valid event must have metadata.
    if (!metadata) {
      this.logger.debug(
        `Event metadata is missing for job: ${jobData.runId}, event type: ${langGraphEvent.event}`,
        `Event metadata is missing for job: ${jobData.runId}, event type: ${langGraphEvent.event}`,
        `Event metadata is missing for job: ${jobData.runId}, event type: ${langGraphEvent.event}`,
        `Event metadata is missing for job: ${jobData.runId}, event type: ${langGraphEvent.event}`,
        `Event metadata is missing for job: ${jobData.runId}, event type: ${langGraphEvent.event}`,
        `Event metadata is missing for job: ${jobData.runId}, event type: ${langGraphEvent.event}`,
        `Event metadata is missing for job: ${jobData.runId}, event type: ${langGraphEvent.event}`,
        `Event metadata is missing for job: ${jobData.runId}, event type: ${langGraphEvent.event}`,
        `Event metadata is missing for job: ${jobData.runId}, event type: ${langGraphEvent.event}`,
      );
      return false;
    }

    // 2. A valid executor event must come from the 'executor' graph
    //    AND its agent ID must match the job's main agent ID.
    const isExecutor =
      metadata.invoked_graph === 'executor' &&
      metadata.agent_id === jobData.mainAgentId;

    if (isExecutor) {
      this.logger.debug(
        `Event is from executor agent: ${metadata.agent_id}, event type: ${langGraphEvent.event}`,
        `Event is from executor agent: ${metadata.agent_id}, event type: ${langGraphEvent.event}`,
        `Event is from executor agent: ${metadata.agent_id}, event type: ${langGraphEvent.event}`,
        `Event is from executor agent: ${metadata.agent_id}, event type: ${langGraphEvent.event}`,
        `Event is from executor agent: ${metadata.agent_id}, event type: ${langGraphEvent.event}`,
        `Event is from executor agent: ${metadata.agent_id}, event type: ${langGraphEvent.event}`,
        `Event is from executor agent: ${metadata.agent_id}, event type: ${langGraphEvent.event}`,
        `Event is from executor agent: ${metadata.agent_id}, event type: ${langGraphEvent.event}`,
      );
    }

    return isExecutor;
  }

  /**
   * Create a new message in the database
   * Uses the threadId as the externalCustomerPlatformDataId
   */
  private async createNewMessage(jobData: ZaloJobData): Promise<string> {
    const message = this.messageRepository.create({
      text: '',
      externalCustomerPlatformDataId: jobData.threadId, // Use threadId, not externalPlatformId
      role: MessageRole.ASSISTANT,
      platform: Platform.ZALO,
      createdAt: `${Date.now()}`,
      processed: true,
      agentId: jobData.mainAgentId,
    });

    const savedMessage = await this.messageRepository.save(message);
    const messageId = savedMessage.id;

    this.logger.debug(
      `Created new assistant message: ${messageId} for run: ${jobData.runId} in thread: ${jobData.threadId} for Zalo user: ${jobData.humanInfo.zaloUser.id}`,
    );
    return messageId;
  }
}
