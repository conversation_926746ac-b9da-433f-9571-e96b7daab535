import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { DataSourceEnum } from '../../enums';

export enum OwnerType {
  USER = 'USER',
  ADMIN = 'ADMIN',
}

export enum MediaStatus {
  DRAFT = 'DRAFT',
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  DELETED = 'DELETED',
}

export enum MediaType {
  IMAGE = 'IMAGE',
  VIDEO = 'VIDEO',
  AUDIO = 'AUDIO',
}
/**
 * Bảng lưu thông tin tài nguyên media
 */
@Entity('media_data')
@Index(['storageKey'], { unique: true })
@Index(['mediaId'], { unique: true, where: '"media_id" IS NOT NULL' })
export class MediaData {
  /**
   * Mã định danh media
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Tên media
   */
  @Column({ type: 'varchar', length: 255, nullable: false })
  name: string;

  /**
   * <PERSON>ô tả về tài nguyên media
   */
  @Column({ type: 'text', nullable: true })
  description: string;

  /**
   * Kích thước media (byte).
   * Stored as a string to safely handle large numbers beyond JavaScript's Number.MAX_SAFE_INTEGER.
   */
  @Column({ type: 'bigint', nullable: false })
  size: string;

  /**
   * Các thẻ phân loại media (dạng JSONB)
   */
  @Column({ type: 'jsonb', nullable: true })
  tags: Record<string, any>;

  /**
   * Khóa định danh trên hệ thống lưu trữ
   */
  @Column({
    type: 'varchar',
    length: 512,
    nullable: false,
    name: 'storage_key',
  })
  storageKey: string;

  /**
   * Mã người sở hữu media (FK tới users)
   */
  @Column({ type: 'integer', nullable: false, name: 'owned_by' })
  ownedBy: number;

  /**
   * 'USER' 'ADMIN'
   */
  @Column({
    type: 'varchar',
    length: 20,
    nullable: true,
    name: 'owner_type',
  })
  ownerType: OwnerType;

  /**
   * The type of media.
   */
  @Column({
    type: 'enum',
    enum: MediaType,
    default: MediaType.IMAGE,
    nullable: false,
    name: 'media_type',
  })
  mediaType: MediaType;

  /**
   * Mã media phụ trợ dùng cho liên kết ngoài hoặc hệ thống phụ trợ
   */
  @Column({ type: 'varchar', length: 100, nullable: true, name: 'media_id' })
  mediaId: string;

  /**
   * The status of the media.
   */
  @Column({
    type: 'enum',
    enum: MediaStatus,
    default: MediaStatus.DRAFT,
    nullable: false,
  })
  status: MediaStatus;

  /**
   * Thời điểm tạo bản ghi (unix timestamp)
   */
  @CreateDateColumn({
    type: 'bigint',
    name: 'created_at',
    transformer: {
      to: (value: Date) => value,
      from: (value: string) => new Date(parseInt(value, 10)),
    },
  })
  createdAt: Date;

  /**
   * Thời điểm cập nhật bản ghi (unix timestamp)
   */
  @UpdateDateColumn({
    type: 'bigint',
    name: 'updated_at',
    transformer: {
      to: (value: Date) => value,
      from: (value: string) => new Date(parseInt(value, 10)),
    },
  })
  updatedAt: Date;

  /**
   * Nguồn dữ liệu: INTERNAL - từ trong hệ thống, EXTERNAL - từ bên ngoài
   */
  @Column({
    name: 'data_source',
    enum: DataSourceEnum,
    type: 'enum',
    default: DataSourceEnum.INTERNAL,
    comment:
      'Nguồn dữ liệu: INTERNAL - từ trong hệ thống, EXTERNAL - từ bên ngoài',
  })
  dataSource: DataSourceEnum;
}
