import { Modu<PERSON> } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { BullModule } from '@nestjs/bullmq';
import { QueueName } from '../../queue/queue-name.enum';
import { CalendarProcessor } from './calendar.processor';
import { CalendarService } from './calendar.service';

/**
 * Module xử lý Calendar trong worker
 * <PERSON><PERSON><PERSON> trách nhiệm xử lý các calendar queue jobs
 */
@Module({
  imports: [
    // HTTP module for API calls to backend
    HttpModule.register({
      timeout: 30000, // 30 seconds default timeout
      maxRedirects: 5,
    }),

    // Config module for environment variables
    ConfigModule,

    // BullMQ queue for calendar
    BullModule.registerQueue({
      name: QueueName.CALENDAR,
    }),
  ],
  providers: [
    CalendarProcessor,
    CalendarService,
  ],
  exports: [
    CalendarService,
  ],
})
export class CalendarModule {}
