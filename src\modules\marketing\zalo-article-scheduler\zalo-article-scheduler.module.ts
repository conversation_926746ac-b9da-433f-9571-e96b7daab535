import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ZaloArticleSchedulerProcessor } from './zalo-article-scheduler.processor';
import { ZaloArticleSchedulerService } from './zalo-article-scheduler.service';
import { ZaloModule } from '../../../shared/services/zalo/zalo.module';

@Module({
  imports: [
    ConfigModule,
    ZaloModule,
  ],
  providers: [
    ZaloArticleSchedulerProcessor,
    ZaloArticleSchedulerService,
  ],
  exports: [
    ZaloArticleSchedulerService,
  ],
})
export class ZaloArticleSchedulerModule {}
