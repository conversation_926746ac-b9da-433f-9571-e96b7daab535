import { Injectable, Logger } from '@nestjs/common';
import {
  JsonContextOptions,
  NodeOutputData,
  WorkflowContext,
  WorkflowExecutionMetadata
} from '../interfaces/workflow-context.interface';
import { WorkflowContextService } from './workflow-context.service';

/**
 * ✅ SIMPLIFIED WORKFLOW CONTEXT BUILDER
 * Service để xây dựng và quản lý SimplifiedWorkflowContext
 */
@Injectable()
export class WorkflowContextBuilderService {
  private readonly logger = new Logger(WorkflowContextBuilderService.name);
  private context: WorkflowContext;

  constructor(
    private readonly contextService: WorkflowContextService
  ) { }

  /**
   * Initialize builder với metadata
   */
  initialize(
    executionId: string,
    workflowId: string,
    userId: number,
    triggerType: 'webhook' | 'schedule' | 'manual'
  ): this {
    this.context = {
      json: {},
      metadata: {
        executionId,
        workflowId,
        userId,
        startTime: Date.now(),
        triggerType,
        totalNodes: 0,
        completedNodes: 0,
        failedNodes: 0,
        status: 'running'
      }
    };

    this.logger.log(`Initialized workflow context builder`, {
      executionId,
      workflowId,
      userId,
      triggerType
    });

    return this;
  }

  /**
   * Thêm node output vào context
   */
  addNodeOutput(
    nodeName: string,
    outputData: NodeOutputData,
    options: JsonContextOptions = {}
  ): this {
    if (!this.context) {
      throw new Error('Context builder not initialized. Call initialize() first.');
    }

    try {
      this.context.json = this.contextService.addNodeToJsonContext(
        this.context.json,
        nodeName,
        outputData,
        options
      );
      this.context.metadata.completedNodes++;

      this.logger.debug(`Added node output to context`, {
        nodeName,
        completedNodes: this.context.metadata.completedNodes
      });

      return this;
    } catch (error) {
      this.context.metadata.failedNodes++;
      this.logger.error(`Failed to add node output`, {
        nodeName,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Thêm nhiều node outputs
   */
  addMultipleNodeOutputs(
    nodes: Record<string, Record<string, any>>,
    options: JsonContextOptions = {}
  ): this {
    if (!this.context) {
      throw new Error('Context builder not initialized. Call initialize() first.');
    }

    const result = this.contextService.addMultipleNodesToJsonContext(
      this.context.json,
      nodes,
      options
    );

    this.context.json = result.updatedJson;

    // Update metadata based on results
    result.results.forEach(r => {
      if (r.success) {
        this.context.metadata.completedNodes++;
      } else {
        this.context.metadata.failedNodes++;
      }
    });

    this.logger.debug(`Added multiple node outputs to context`, {
      totalNodes: Object.keys(nodes).length,
      successCount: result.results.filter(r => r.success).length,
      failureCount: result.results.filter(r => !r.success).length
    });

    return this;
  }

  /**
   * Update metadata
   */
  updateMetadata(updates: Partial<WorkflowExecutionMetadata>): this {
    if (!this.context) {
      throw new Error('Context builder not initialized. Call initialize() first.');
    }

    this.context.metadata = { ...this.context.metadata, ...updates };

    this.logger.debug(`Updated context metadata`, {
      updates: Object.keys(updates)
    });

    return this;
  }

  /**
   * Mark node as failed
   */
  markNodeFailed(nodeName: string, error?: any): this {
    if (!this.context) {
      throw new Error('Context builder not initialized. Call initialize() first.');
    }

    this.context.metadata.failedNodes++;

    if (error) {
      this.context.json[`${nodeName}_error`] = {
        error: error.message || String(error),
        timestamp: Date.now(),
        nodeName
      };
    }

    this.logger.warn(`Marked node as failed`, {
      nodeName,
      failedNodes: this.context.metadata.failedNodes,
      hasError: !!error
    });

    return this;
  }

  /**
   * Complete execution
   */
  complete(): this {
    if (!this.context) {
      throw new Error('Context builder not initialized. Call initialize() first.');
    }

    this.context.metadata.endTime = Date.now();
    this.context.metadata.executionDuration =
      this.context.metadata.endTime - this.context.metadata.startTime;
    this.context.metadata.status =
      this.context.metadata.failedNodes > 0 ? 'failed' : 'completed';

    this.logger.log(`Completed workflow execution`, {
      executionId: this.context.metadata.executionId,
      status: this.context.metadata.status,
      duration: this.context.metadata.executionDuration,
      completedNodes: this.context.metadata.completedNodes,
      failedNodes: this.context.metadata.failedNodes
    });

    return this;
  }

  /**
   * Get value từ context
   */
  getValue<T = any>(path: string, options: JsonContextOptions = {}): T | undefined {
    if (!this.context) {
      throw new Error('Context builder not initialized. Call initialize() first.');
    }

    return this.contextService.getValueFromJsonContext(this.context.json, path, options) as T;
  }

  /**
   * Check if path exists
   */
  hasPath(path: string, options: JsonContextOptions = {}): boolean {
    if (!this.context) {
      throw new Error('Context builder not initialized. Call initialize() first.');
    }

    return this.contextService.hasPathInJsonContext(this.context.json, path, options);
  }

  /**
   * Get all available paths
   */
  getAllPaths(options: JsonContextOptions = {}): string[] {
    if (!this.context) {
      throw new Error('Context builder not initialized. Call initialize() first.');
    }

    return this.contextService.getAllPathsFromJsonContext(this.context.json, options);
  }

  /**
   * Get built context
   */
  build(): WorkflowContext {
    if (!this.context) {
      throw new Error('Context builder not initialized. Call initialize() first.');
    }

    return JSON.parse(JSON.stringify(this.context)); // Deep clone
  }

  /**
   * Get context for template processing
   */
  getTemplateContext(): Record<string, any> {
    if (!this.context) {
      throw new Error('Context builder not initialized. Call initialize() first.');
    }

    return {
      json: this.context.json,
      metadata: this.context.metadata
    };
  }

  /**
   * Clone builder
   */
  clone(): WorkflowContextBuilderService {
    if (!this.context) {
      throw new Error('Context builder not initialized. Call initialize() first.');
    }

    const cloned = new WorkflowContextBuilderService(this.contextService);
    cloned.context = JSON.parse(JSON.stringify(this.context));
    return cloned;
  }

  /**
   * Reset context (keep metadata structure)
   */
  reset(): this {
    if (!this.context) {
      throw new Error('Context builder not initialized. Call initialize() first.');
    }

    this.context.json = {};
    this.context.metadata.completedNodes = 0;
    this.context.metadata.failedNodes = 0;
    this.context.metadata.status = 'running';
    delete this.context.metadata.endTime;
    delete this.context.metadata.executionDuration;

    this.logger.debug(`Reset context builder`, {
      executionId: this.context.metadata.executionId
    });

    return this;
  }

  /**
   * Get current context (read-only)
   */
  getCurrentContext(): Readonly<WorkflowContext> {
    if (!this.context) {
      throw new Error('Context builder not initialized. Call initialize() first.');
    }

    return this.context;
  }
}
