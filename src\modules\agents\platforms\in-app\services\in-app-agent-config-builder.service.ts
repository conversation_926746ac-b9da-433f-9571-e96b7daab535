import { Injectable, Logger } from '@nestjs/common';
import { AgentConfig } from '../../../shared/interfaces/agent-config.interface';
import { AuthContext } from '../../../shared/interfaces';
import { InAppPlatformStrategy } from './in-app-platform.strategy';
import { AgentConfigBuilderService } from '../../../shared/services/agent-config-builder.service';

/**
 * InApp Agent Config Builder Service (Facade)
 *
 * Provides a platform-specific interface for building agent configurations
 * while delegating the actual work to the generic AgentConfigBuilderService.
 * Maintains backward compatibility with existing in-app code.
 */
@Injectable()
export class InAppAgentConfigBuilderService {
  private readonly logger = new Logger(InAppAgentConfigBuilderService.name);

  constructor(
    private readonly baseService: AgentConfigBuilderService,
    private readonly platformStrategy: InAppPlatformStrategy,
  ) {}

  /**
   * Build agent configurations with 4-query maximum strategy
   *
   * @param agentIds Array of agent IDs to build configurations for
   * @param promptBuilderMap Map of agent IDs to prompt builder functions
   * @param userId Optional user ID for integration selection logic
   * @param jwt Optional JWT token for MCP client building
   * @returns Map of agent IDs to complete AgentConfigInterface objects
   */
  async buildAgentConfigs(param: {
    agentIds: Array<{id: string, prompt?: string}>;
    promptBuilderMap: Record<string, Array<() => string | Promise<string>>>;
    userId?: number;
    jwt?: string;
  }): Promise<Record<string, AgentConfig>> {
    const { agentIds, promptBuilderMap, userId, jwt } = param;
    
    this.logger.debug(`Building agent configs for ${agentIds.length} agents (in-app platform)`, {
      agentIds: agentIds.map(agent => agent.id),
      userId,
    });

    // Convert in-app specific parameters to generic format
    const authContext: AuthContext = { 
      jwt, 
      userId 
    };

    // Delegate to the generic base service
    return this.baseService.buildAgentConfigs({
      agentIds,
      promptBuilderMap,
      platformStrategy: this.platformStrategy,
      authContext,
    });
  }

}
