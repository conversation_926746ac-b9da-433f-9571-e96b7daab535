import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger, Injectable } from '@nestjs/common';
import { Job } from 'bullmq';
import { QueueName, ZaloArticleTrackingJobName } from '../../../queue/queue-name.enum';
import { ZaloArticleTrackingJobData } from '../../../queue/queue.types';
import { ZaloArticleTrackingService } from './zalo-article-tracking.service';

/**
 * Processor xử lý queue tracking bài viết Zalo
 */
@Injectable()
@Processor(QueueName.ZALO_ARTICLE_TRACKING, {
  concurrency: 5, // Xử lý tối đa 5 job đồng thời
  stalledInterval: 30 * 1000, // 30 giây
  maxStalledCount: 1,
})
export class ZaloArticleTrackingProcessor extends WorkerHost {
  private readonly logger = new Logger(ZaloArticleTrackingProcessor.name);

  constructor(
    private readonly zaloArticleTrackingService: ZaloArticleTrackingService,
  ) {
    super();
  }

  /**
   * <PERSON><PERSON> lý job từ queue
   * @param job Job chứa dữ liệu tracking bài viết
   */
  async process(job: Job<ZaloArticleTrackingJobData, any, string>): Promise<void> {
    this.logger.log(
      `Bắt đầu xử lý job article tracking: ${job.id} - Token: ${job.data.token.substring(0, 20)}...`,
    );

    try {
      switch (job.name) {
        case ZaloArticleTrackingJobName.CHECK_ARTICLE_STATUS:
          await this.processCheckArticleStatus(job);
          break;
        
        default:
          this.logger.warn(`Job name không được hỗ trợ: ${job.name}`);
          throw new Error(`Job name không được hỗ trợ: ${job.name}`);
      }

      this.logger.log(`Đã xử lý thành công job article tracking: ${job.id}`);
    } catch (error) {
      this.logger.error(
        `Lỗi khi xử lý job article tracking: ${job.id} - ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Xử lý job kiểm tra trạng thái bài viết
   * @param job Job chứa dữ liệu tracking
   */
  private async processCheckArticleStatus(job: Job<ZaloArticleTrackingJobData>): Promise<void> {
    const { token, accessToken, userId, integrationId, localArticleId, checkCount = 0 } = job.data;

    this.logger.debug(`Checking article status - Token: ${token.substring(0, 20)}..., Check count: ${checkCount}`);

    // Cập nhật progress
    await job.updateProgress(10);

    try {
      // Gọi service để check article status và cập nhật database
      const result = await this.zaloArticleTrackingService.checkAndUpdateArticleStatus(
        token,
        accessToken,
        userId,
        integrationId,
        localArticleId
      );

      await job.updateProgress(50);

      this.logger.debug(`Article status result:`, JSON.stringify(result, null, 2));

      // Nếu bài viết đã có article_id (success), không cần check thêm
      if (result.status === 'success' && result.article_id) {
        this.logger.log(`Article tracking completed successfully - Article ID: ${result.article_id}`);
        await job.updateProgress(100);
        return;
      }

      // Nếu bài viết failed, không cần check thêm
      if (result.status === 'failed') {
        this.logger.warn(`Article processing failed - Token: ${token.substring(0, 20)}...`);
        await job.updateProgress(100);
        return;
      }

      // Nếu vẫn đang processing và chưa quá số lần check tối đa
      const maxCheckCount = 20; // Tối đa 20 lần check (khoảng 10-15 phút)
      if (result.status === 'processing' && checkCount < maxCheckCount) {
        this.logger.debug(`Article still processing, scheduling next check - Count: ${checkCount + 1}/${maxCheckCount}`);
        
        // Tạo job mới để check lại sau một khoảng thời gian
        const nextCheckData: ZaloArticleTrackingJobData = {
          ...job.data,
          checkCount: checkCount + 1,
          delayMs: Math.min(10000 + (checkCount * 2000), 30000), // Tăng delay dần từ 10s đến 30s
        };

        // Thêm job mới vào queue để check lại
        await this.zaloArticleTrackingService.scheduleNextCheck(nextCheckData);
        
        await job.updateProgress(80);
        this.logger.debug(`Scheduled next check for article tracking - Token: ${token.substring(0, 20)}...`);
      } else {
        // Đã check quá số lần tối đa hoặc có lỗi khác
        this.logger.warn(`Article tracking reached max attempts or unknown status - Token: ${token.substring(0, 20)}..., Status: ${result.status}`);
        await job.updateProgress(100);
      }

    } catch (error) {
      this.logger.error(
        `Error checking article status - Token: ${token.substring(0, 20)}..., Error: ${error.message}`,
        error.stack,
      );

      // Nếu có lỗi và chưa quá số lần check tối đa, thử lại
      const maxCheckCount = 20;
      if (checkCount < maxCheckCount) {
        const nextCheckData: ZaloArticleTrackingJobData = {
          ...job.data,
          checkCount: checkCount + 1,
          delayMs: Math.min(15000 + (checkCount * 3000), 60000), // Delay lâu hơn khi có lỗi
        };

        await this.zaloArticleTrackingService.scheduleNextCheck(nextCheckData);
        this.logger.debug(`Scheduled retry for article tracking after error - Token: ${token.substring(0, 20)}...`);
      }

      throw error;
    }
  }
}
