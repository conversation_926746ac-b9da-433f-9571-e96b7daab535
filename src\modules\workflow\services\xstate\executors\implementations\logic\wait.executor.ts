import { Injectable, Logger } from '@nestjs/common';
import { BaseNodeExecutor } from '../../base/base-node.executor';
import { 
  ExecutorContext, 
  ValidationResult,
} from '../../base/node-executor.interface';
import { DetailedNodeExecutionResult, NodeExecutionConfig } from '../../../types';
import { NodeGroupEnum } from '../../../../../enums/node-group.enum';
import { ENodeType } from '../../../../../interfaces/node-manager.interface';
import {
  IWaitParameters,
  IWaitInput,
  IWaitOutput,
  validateWaitParameters
} from '../../../../../interfaces';
import { ConditionEvaluatorService } from '../../shared/condition-evaluator.service';
import { ValidationUtils } from '../../shared/validation.utils';

/**
 * Executor for WAIT node type
 * Handles delays and conditional waiting
 */
@Injectable()
export class WaitExecutor extends BaseNodeExecutor {
  readonly nodeGroup = NodeGroupEnum.LOGIC;
  readonly supportedNodeTypes = [ENodeType.WAIT];
  readonly executorName = 'WaitExecutor';
  readonly version = '1.0.0';

  constructor(
    private readonly conditionEvaluatorService: ConditionEvaluatorService
  ) {
    super();
  }

  /**
   * Execute WAIT node
   */
  protected async executeNode(
    context: ExecutorContext,
    config: NodeExecutionConfig
  ): Promise<DetailedNodeExecutionResult> {
    const startTime = Date.now();

    // Parse and validate parameters
    const params = context.node.parameters as IWaitParameters;
    const input = context.inputData as IWaitInput;

    try {
      
      this.logger.debug(`Executing WAIT: ${params.wait_type}`);

      let waitResult: any;

      switch (params.wait_type) {
        case 'duration':
          waitResult = await this.executeDurationWait(params, input);
          break;

        case 'until_time':
          waitResult = await this.executeUntilTimeWait(params, input);
          break;

        default:
          throw new Error(`Unsupported wait type: ${params.wait_type}`);
      }
      
      const executionTime = Date.now() - startTime;
      
      const output: IWaitOutput = {
        data: input.data,
        wait_metadata: {
          wait_type: params.wait_type,
          planned_wait_time: waitResult.plannedWaitTime,
          actual_wait_time: waitResult.actualWaitTime,
          target_time: waitResult.targetTime || Date.now() + waitResult.plannedWaitTime,
          start_time: waitResult.startTime,
          completed_at: waitResult.endTime,
          status: waitResult.status || 'completed',
          status_reason: waitResult.statusReason,
          timed_out: waitResult.timeoutReached || false,
          cancelled: waitResult.cancelled || false,
          progress_snapshots: waitResult.progressSnapshots || []
        }
      };
      
      return {
        nodeType: 'WAIT',
        success: true,
        outputData: output,
        metadata: {
          executionTime,
          waitType: params.wait_type,
          actualWaitTime: waitResult.actualWaitTime,
          plannedWaitTime: waitResult.plannedWaitTime,
          timeoutReached: waitResult.timeoutReached || false,
          customMetrics: {
            waitType: params.wait_type,
            actualWaitTime: waitResult.actualWaitTime,
            plannedWaitTime: waitResult.plannedWaitTime,
            timeoutReached: waitResult.timeoutReached || false,
            status: waitResult.status || 'completed',
          },
          logs: [
            `WAIT ${params.wait_type} completed`,
            `Planned wait time: ${waitResult.plannedWaitTime}ms`,
            `Actual wait time: ${waitResult.actualWaitTime}ms`,
            `Status: ${waitResult.status || 'completed'}`,
            `Total execution time: ${executionTime}ms`,
          ],
        },
      };
      
    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      return {
        nodeType: 'WAIT',
        success: false,
        error,
        metadata: {
          executionTime,
          waitType: params.wait_type,
          actualWaitTime: 0,
          plannedWaitTime: 0,
          timeoutReached: false,
          logs: [
            `WAIT execution failed: ${error.message}`,
            `Execution time: ${executionTime}ms`,
          ],
        },
      };
    }
  }

  /**
   * Execute duration wait
   */
  private async executeDurationWait(params: IWaitParameters, input: IWaitInput): Promise<any> {
    if (!params.duration_config) {
      throw new Error('Duration configuration is required for duration wait');
    }

    const config = params.duration_config;
    const startTime = Date.now();
    let duration: number;

    // Calculate duration based on type
    if (config.type === 'static') {
      duration = config.value || 1000;
    } else if (config.type === 'dynamic') {
      if (!config.field) {
        throw new Error('Field is required for dynamic duration');
      }

      const fieldValue = this.getFieldValue(input.data, config.field);
      duration = Number(fieldValue);

      if (isNaN(duration) || duration < 0) {
        duration = config.default_value || 1000;
      }
    } else {
      throw new Error(`Unsupported duration type: ${config.type}`);
    }

    // Convert to milliseconds based on unit
    duration = this.convertToMilliseconds(duration, config.unit);

    // Apply min/max constraints
    if (config.min_duration && duration < config.min_duration) {
      duration = config.min_duration;
    }
    if (config.max_duration && duration > config.max_duration) {
      duration = config.max_duration;
    }

    // Apply global max wait time
    if (params.max_wait_time && duration > params.max_wait_time) {
      switch (params.timeout_behavior) {
        case 'error':
          throw new Error(`Duration ${duration}ms exceeds max wait time ${params.max_wait_time}ms`);
        case 'truncate':
          duration = params.max_wait_time;
          break;
        case 'skip':
          return {
            plannedWaitTime: duration,
            actualWaitTime: 0,
            status: 'skipped',
            statusReason: 'Duration exceeds max wait time',
            startTime,
            endTime: Date.now(),
            targetTime: startTime + duration
          };
      }
    }

    // Execute wait
    await this.sleep(duration);
    const endTime = Date.now();

    return {
      plannedWaitTime: duration,
      actualWaitTime: endTime - startTime,
      status: 'completed',
      startTime,
      endTime,
      targetTime: startTime + duration
    };
  }

  /**
   * Execute until time wait
   */
  private async executeUntilTimeWait(params: IWaitParameters, input: IWaitInput): Promise<any> {
    if (!params.until_config) {
      throw new Error('Until configuration is required for until_time wait');
    }

    const config = params.until_config;
    const startTime = Date.now();
    let targetTime: number;

    // Calculate target time based on type
    if (config.type === 'static') {
      if (!config.datetime) {
        throw new Error('Datetime is required for static until time');
      }

      // Parse based on format
      if (config.format === 'unix_timestamp') {
        targetTime = Number(config.datetime);
      } else {
        targetTime = new Date(config.datetime).getTime();
      }

      if (isNaN(targetTime)) {
        throw new Error(`Invalid timestamp: ${config.datetime}`);
      }
    } else if (config.type === 'dynamic') {
      if (!config.field) {
        throw new Error('Field is required for dynamic until time');
      }

      const fieldValue = this.getFieldValue(input.data, config.field);

      // Parse based on format
      if (config.format === 'unix_timestamp') {
        targetTime = Number(fieldValue);
      } else {
        targetTime = new Date(fieldValue).getTime();
      }

      if (isNaN(targetTime)) {
        targetTime = config.default_datetime ? new Date(config.default_datetime).getTime() : Date.now();
      }
    } else {
      throw new Error(`Unsupported until time type: ${config.type}`);
    }

    // Check if target time is in the past
    if (targetTime <= startTime) {
      return {
        plannedWaitTime: 0,
        actualWaitTime: 0,
        status: 'completed',
        statusReason: 'Target time is in the past',
        startTime,
        endTime: Date.now(),
        targetTime
      };
    }

    const waitDuration = targetTime - startTime;

    // Apply max wait time constraint
    if (params.max_wait_time && waitDuration > params.max_wait_time) {
      switch (params.timeout_behavior) {
        case 'error':
          throw new Error(`Wait duration ${waitDuration}ms exceeds max wait time ${params.max_wait_time}ms`);
        case 'truncate':
          const truncatedWait = params.max_wait_time;
          await this.sleep(truncatedWait);
          return {
            plannedWaitTime: waitDuration,
            actualWaitTime: truncatedWait,
            status: 'timeout',
            statusReason: 'Max wait time exceeded',
            startTime,
            endTime: Date.now(),
            targetTime,
            timeoutReached: true
          };
        case 'skip':
          return {
            plannedWaitTime: waitDuration,
            actualWaitTime: 0,
            status: 'skipped',
            statusReason: 'Wait duration exceeds max wait time',
            startTime,
            endTime: Date.now(),
            targetTime
          };
      }
    }

    // Execute wait
    await this.sleep(waitDuration);
    const endTime = Date.now();

    return {
      plannedWaitTime: waitDuration,
      actualWaitTime: endTime - startTime,
      status: 'completed',
      startTime,
      endTime,
      targetTime
    };
  }



  /**
   * Validate WAIT node input
   */
  protected async validateNodeSpecificInput(
    context: ExecutorContext,
    result: ValidationResult
  ): Promise<void> {
    const params = context.node.parameters as IWaitParameters;
    
    // Use existing validation function from interface
    const interfaceValidation = validateWaitParameters(params);
    if (!interfaceValidation.isValid) {
      for (const error of interfaceValidation.errors) {
        ValidationUtils.addError(
          result,
          'INTERFACE_VALIDATION_ERROR',
          error,
          'parameters'
        );
      }
    }

    // Additional custom validations
    this.validateWaitSpecific(params, result);
  }

  /**
   * WAIT specific validations
   */
  private validateWaitSpecific(
    params: IWaitParameters,
    result: ValidationResult
  ): void {
    // Validate wait type specific configurations
    switch (params.wait_type) {
      case 'duration':
        this.validateDurationConfig(params, result);
        break;

      case 'until_time':
        this.validateUntilTimeConfig(params, result);
        break;
    }

    // Validate timeout settings
    if (params.max_wait_time) {
      ValidationUtils.validateNumberRange(
        result,
        params.max_wait_time,
        'Max wait time',
        1000,
        86400000, // 24 hours max
        'max_wait_time'
      );
    }

    if (params.progress_interval) {
      ValidationUtils.validateNumberRange(
        result,
        params.progress_interval,
        'Progress interval',
        100,
        60000, // 1 minute max
        'progress_interval'
      );
    }
  }

  private validateDurationConfig(params: IWaitParameters, result: ValidationResult): void {
    if (!params.duration_config) {
      ValidationUtils.addError(
        result,
        'MISSING_DURATION_CONFIG',
        'Duration configuration is required for duration wait',
        'duration_config'
      );
      return;
    }

    const config = params.duration_config;

    // Validate based on duration type
    if (config.type === 'static') {
      if (config.value === undefined) {
        ValidationUtils.addError(
          result,
          'MISSING_DURATION_VALUE',
          'Duration value is required for static duration',
          'duration_config.value'
        );
      } else {
        ValidationUtils.validateNumberRange(
          result,
          config.value,
          'Duration value',
          0,
          86400000, // 24 hours max
          'duration_config.value'
        );
      }
    } else if (config.type === 'dynamic') {
      if (!config.field) {
        ValidationUtils.addError(
          result,
          'MISSING_DURATION_FIELD',
          'Field is required for dynamic duration',
          'duration_config.field'
        );
      } else {
        ValidationUtils.validateFieldPath(
          result,
          config.field,
          'Duration field',
          'duration_config.field'
        );
      }
    }

    // Validate constraints
    if (config.min_duration !== undefined && config.max_duration !== undefined) {
      if (config.min_duration > config.max_duration) {
        ValidationUtils.addError(
          result,
          'INVALID_DURATION_RANGE',
          'Minimum duration cannot be greater than maximum duration',
          'duration_config'
        );
      }
    }
  }

  private validateUntilTimeConfig(params: IWaitParameters, result: ValidationResult): void {
    if (!params.until_config) {
      ValidationUtils.addError(
        result,
        'MISSING_UNTIL_CONFIG',
        'Until configuration is required for until_time wait',
        'until_config'
      );
      return;
    }

    const config = params.until_config;

    // Validate based on until type
    if (config.type === 'static') {
      if (!config.datetime) {
        ValidationUtils.addError(
          result,
          'MISSING_DATETIME',
          'Datetime is required for static until time',
          'until_config.datetime'
        );
      } else {
        // Validate datetime format
        const testTime = new Date(config.datetime).getTime();
        if (isNaN(testTime)) {
          ValidationUtils.addError(
            result,
            'INVALID_DATETIME',
            'Invalid datetime format',
            'until_config.datetime',
            config.datetime
          );
        }
      }
    } else if (config.type === 'dynamic') {
      if (!config.field) {
        ValidationUtils.addError(
          result,
          'MISSING_DATETIME_FIELD',
          'Field is required for dynamic datetime',
          'until_config.field'
        );
      } else {
        ValidationUtils.validateFieldPath(
          result,
          config.field,
          'Datetime field',
          'until_config.field'
        );
      }
    }
  }

  /**
   * Convert duration to milliseconds based on unit
   */
  private convertToMilliseconds(value: number, unit?: string): number {
    if (!unit || unit === 'milliseconds') {
      return value;
    }

    switch (unit) {
      case 'seconds':
        return value * 1000;
      case 'minutes':
        return value * 60 * 1000;
      case 'hours':
        return value * 60 * 60 * 1000;
      case 'days':
        return value * 24 * 60 * 60 * 1000;
      default:
        this.logger.warn(`Unknown time unit: ${unit}, using milliseconds`);
        return value;
    }
  }

  /**
   * Get field value using dot notation
   */
  private getFieldValue(obj: Record<string, any>, fieldPath: string): any {
    try {
      return fieldPath.split('.').reduce((current, key) => {
        if (current === null || current === undefined) {
          return undefined;
        }
        return current[key];
      }, obj);
    } catch (error) {
      return undefined;
    }
  }

  /**
   * Sleep for specified milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
