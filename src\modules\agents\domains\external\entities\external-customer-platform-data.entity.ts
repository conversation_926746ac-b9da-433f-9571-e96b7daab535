import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { UserConvertCustomer } from './user-convert-customer.entity';
import { Platform } from '../../../shared/enums';

@Entity('external_customer_platform_data')
export class ExternalCustomerPlatformData {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id', type: 'bigint', nullable: false })
  userId: number;

  @ManyToOne(() => UserConvertCustomer)
  @JoinColumn({ name: 'user_convert_customer_id' })
  userConvertCustomer: UserConvertCustomer;

  @Column({
    type: 'enum',
    enum: Platform,
    nullable: false,
  })
  platform: Platform;

  @Column({ type: 'jsonb', nullable: false })
  data: any;
}
