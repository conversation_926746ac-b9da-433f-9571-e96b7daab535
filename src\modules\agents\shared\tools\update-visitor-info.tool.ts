import { CallbackManagerForToolRun } from '@langchain/core/callbacks/manager';
import { StructuredTool, ToolRunnableConfig } from '@langchain/core/tools';
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { z } from 'zod';
import { UserConvertCustomer } from '../../domains/external/entities';
import { WebsitePlannerExecutorConfig } from '../../platforms/website/graph-configs/website-planner-executor-config.interface';

@Injectable()
export class UpdateVisitorInfoTool extends StructuredTool {
  private readonly logger = new Logger(UpdateVisitorInfoTool.name);
  name: string;
  description: string;
  schema: any;
  
  constructor(
    @InjectRepository(UserConvertCustomer)
    private readonly userConvertCustomerRepository: Repository<UserConvertCustomer>,
  ) {
    super();
    this.name = 'update_visitor_info';
    this.description = `
- A tool to update visitor information fields
- All fields are optional - only provided fields will be updated
- Available fields: name, email, countryCode, phone, platform, timezone, metadata`;

    this.schema = z.object({
      name: z.string().optional().describe('Customer name'),
      email: z.array(z.string().email()).optional().describe('Email addresses'),
      countryCode: z.number().optional().describe('Country code'),
      phone: z.string().optional().describe('Phone number (without country code)'),
      metadata: z.record(z.any()).optional().describe('Custom fields as key-value pairs'),
    });
  }

  protected async _call(
    arg: z.infer<typeof this.schema>,
    runManager?: CallbackManagerForToolRun,
    parentConfig?: ToolRunnableConfig<WebsitePlannerExecutorConfig>,
  ): Promise<string> {
    const visitorId = parentConfig?.configurable?.currentUser?.visitor?.id;
    
    if (!visitorId) {
      return 'Error: Missing visitor ID in execution context. Do not retry upon this message';
    }

    // Filter out undefined values to only update provided fields
    const updateData: Partial<UserConvertCustomer> = {};
    
    if (arg.name !== undefined) updateData.name = arg.name;
    if (arg.email !== undefined) updateData.email = arg.email;
    if (arg.countryCode !== undefined) updateData.countryCode = arg.countryCode;
    if (arg.phone !== undefined) updateData.phone = arg.phone;
    if (arg.platform !== undefined) updateData.platform = arg.platform;
    if (arg.timezone !== undefined) updateData.timezone = arg.timezone;
    if (arg.metadata !== undefined) updateData.metadata = arg.metadata;

    // Check if there's anything to update
    if (Object.keys(updateData).length === 0) {
      return 'No fields provided for update. Please specify at least one field to update.';
    }

    const queryRunner = this.userConvertCustomerRepository.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Check if visitor exists
      const visitor = await queryRunner.manager.findOne(UserConvertCustomer, {
        where: { id: visitorId },
      });

      if (!visitor) {
        throw new Error('Visitor not found');
      }

      // Update the visitor
      await queryRunner.manager.save(UserConvertCustomer, {
        ...visitor,
        ...updateData,
        updatedAt: Date.now(),
      });

      await queryRunner.commitTransaction();
      
      const updatedFields = Object.keys(updateData).join(', ');
      return `Successfully updated visitor information. Updated fields: ${updatedFields}`;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(
        `Error updating visitor info for visitor ${visitorId}`,
        error,
      );
      return `Error: Failed to update visitor information. Reason: ${error.message}`;
    } finally {
      await queryRunner.release();
    }
  }
}