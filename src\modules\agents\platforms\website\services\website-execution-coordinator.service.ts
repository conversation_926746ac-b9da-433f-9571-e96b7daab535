import { Injectable, Logger } from '@nestjs/common';
import { AgentConfig, LangChainInput } from '../../../shared/interfaces';
import { TokenUsageCollector } from '../../../shared/utils';
import { WebsiteJobData } from '../interfaces/website-job-data.interface';
import { WebsitePlannerExecutorConfig } from '../graph-configs/website-planner-executor-config.interface';
import { ExecutionInput } from '../../../shared/interfaces';

@Injectable()
export class WebsiteExecutionCoordinatorService {
  private readonly logger = new Logger(WebsiteExecutionCoordinatorService.name);

  /**
   * Build GraphConfigurable object for PlannerExecutorGraph execution
   * Combines planner config, executor config, and execution parameters
   */
  buildPlannerExecutorGraphConfigurable(param: {
    jobData: WebsiteJobData;
    plannerAgent: AgentConfig | undefined;
    executorAgent: AgentConfig;
    platformThreadId: string;
  }): WebsitePlannerExecutorConfig {
    const { jobData, plannerAgent, executorAgent, platformThreadId } = param;
    
    this.logger.debug('Building PlannerExecutor GraphConfigurable object', {
      visitorId: jobData.humanInfo.websiteVisitor.id,
      websiteOwnerId: jobData.humanInfo.websiteOwner.userId,
      hasPlannerAgent: !!plannerAgent,
      executorAgentId: executorAgent.id,
    });

    const graphConfigurable: WebsitePlannerExecutorConfig = {
      plannerAgent,
      executorAgent,
      thread_id: platformThreadId,
      platform: jobData.platform,
      currentUser: {
        visitor: jobData.humanInfo.websiteVisitor,
        owner: jobData.humanInfo.websiteOwner,
        info: jobData.humanInfo.websiteInfo,
      },
    };

    this.logger.debug('PlannerExecutor GraphConfigurable built successfully', {
      threadId: graphConfigurable.thread_id,
      platform: graphConfigurable.platform,
      hasPlannerAgent: !!graphConfigurable.plannerAgent,
      executorAgentId: graphConfigurable.executorAgent.id,
    });

    return graphConfigurable;
  }

  /**
   * Initialize token usage collector for billing
   * Creates collector with both planner and executor agent mappings
   */
  initializeTokenCollector(
    jobData: WebsiteJobData,
    plannerAgent: AgentConfig | undefined,
    executorAgent: AgentConfig,
  ): TokenUsageCollector {
    this.logger.debug('Initializing token usage collector', {
      visitorId: jobData.humanInfo.websiteVisitor.id,
      websiteOwnerId: jobData.humanInfo.websiteOwner.userId,
      hasPlannerAgent: !!plannerAgent,
      executorAgentId: executorAgent.id,
    });

    // Build agent map for token tracking
    const agentMap: Record<string, AgentConfig> = {
      [executorAgent.id]: executorAgent,
    };

    // Add planner agent if present
    if (plannerAgent) {
      agentMap[plannerAgent.id] = plannerAgent;
    }

    // Website platform: business owner pays for tokens (websiteOwnerId)
    const tokenCollector = new TokenUsageCollector(agentMap, {
      userId: jobData.humanInfo.websiteOwner.userId,
    });

    this.logger.debug('Token usage collector initialized', {
      agentCount: Object.keys(agentMap).length,
      websiteOwnerId: jobData.humanInfo.websiteOwner.userId,
      agentIds: Object.keys(agentMap),
    });

    return tokenCollector;
  }

  /**
   * Build execution parameters for PlannerExecutorGraph
   * Creates tags and context information for execution tracking
   */
  buildExecutionParameters(
    jobData: WebsiteJobData,
    plannerAgent: AgentConfig | undefined,
    executorAgent: AgentConfig,
  ): { tags: string[]; userContext: string; executorAgentTag: string; plannerAgentTag?: string } {
    this.logger.debug('Building execution parameters', {
      runId: jobData.runId,
      platform: jobData.platform,
      visitorId: jobData.humanInfo.websiteVisitor.id,
      websiteOwnerId: jobData.humanInfo.websiteOwner.userId,
    });

    // Context shows website owner (who pays) and visitor (who interacts)
    const userContext = `website_owner:${jobData.humanInfo.websiteOwner.userId}`;

    // Required agent tags for token usage collection
    const executorAgentTag = `agent:${executorAgent.id}:${executorAgent.type}`;
    const plannerAgentTag = plannerAgent 
      ? `agent:${plannerAgent.id}:${plannerAgent.type}` 
      : undefined;

    const tags = [
      userContext,
      `visitor:${jobData.humanInfo.websiteVisitor.id}`,
      `run:${jobData.runId}`,
      `platform:${jobData.platform}`,
      executorAgentTag,
    ];

    // Add planner tag if present
    if (plannerAgentTag) {
      tags.push(plannerAgentTag);
    }

    this.logger.debug('Execution parameters built', {
      userContext,
      executorAgentTag,
      plannerAgentTag,
      tagCount: tags.length,
    });

    return { tags, userContext, executorAgentTag, plannerAgentTag };
  }

  /**
   * Setup LangGraph execution with streaming and token collection
   * Coordinates all execution components and prepares for streaming
   */
  async setupLangGraphExecution(
    jobData: WebsiteJobData,
    langGraphInput: LangChainInput,
    graphConfigurable: WebsitePlannerExecutorConfig,
  ): Promise<{
    tokenUsageCollector: TokenUsageCollector;
    input: any;
    tags: string[];
    abortController: AbortController;
  }> {
    this.logger.debug('Setting up LangGraph execution', {
      runId: jobData.runId,
      threadId: jobData.threadId,
      hasMessages: !!langGraphInput.messages,
      messageCount: langGraphInput.messages?.length || 0,
    });

    // Initialize token usage collector for billing
    const tokenUsageCollector = this.initializeTokenCollector(
      jobData,
      graphConfigurable.plannerAgent,
      graphConfigurable.executorAgent,
    );

    // Build LangGraph input from messages
    const input = { messages: langGraphInput.messages };

    // Build execution parameters for PlannerExecutorGraph
    const { tags } = this.buildExecutionParameters(
      jobData,
      graphConfigurable.plannerAgent,
      graphConfigurable.executorAgent,
    );

    const abortController = new AbortController();

    this.logger.debug('LangGraph execution setup complete', {
      runId: jobData.runId,
      threadId: jobData.threadId,
      messageCount: langGraphInput.messages?.length || 0,
      tagCount: tags.length,
      hasPlannerAgent: !!graphConfigurable.plannerAgent,
    });

    return {
      tokenUsageCollector,
      input,
      tags,
      abortController,
    };
  }
}