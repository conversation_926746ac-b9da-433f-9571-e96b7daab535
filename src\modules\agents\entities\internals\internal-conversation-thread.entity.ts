import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Internal Conversation Threads entity
 * Supports dual ownership (user OR employee) for internal conversations
 */
@Entity('internal_conversation_threads')
export class InternalConversationThread {
  /**
   * UUID primary key for the conversation thread
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Title/name of the conversation
   */
  @Column({ type: 'varchar', length: 255, nullable: false })
  title: string;

  /**
   * ID of the user who owns this thread (nullable for employee-owned threads)
   */
  @Column({ name: 'user_id', type: 'integer', nullable: true })
  userId?: number;

  /**
   * ID of the employee who owns this thread (nullable for user-owned threads)
   */
  @Column({ name: 'employee_id', type: 'integer', nullable: true })
  employeeId?: number;

  /**
   * Creation timestamp in milliseconds
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    nullable: false,
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  createdAt: string;

  /**
   * Last updated timestamp in milliseconds
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    nullable: false,
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  updatedAt: string;

  /**
   * Soft delete timestamp in milliseconds (null if not deleted)
   */
  @Column({
    name: 'deleted_at',
    type: 'bigint',
    nullable: true,
  })
  deletedAt?: string | null;

  // Note: Database constraint ensures only one owner:
  // CHECK ((user_id IS NOT NULL AND employee_id IS NULL) OR (user_id IS NULL AND employee_id IS NOT NULL))
}
