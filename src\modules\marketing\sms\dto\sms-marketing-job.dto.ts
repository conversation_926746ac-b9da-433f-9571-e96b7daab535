import { SmsCampaignType } from '../constants';
import { SenderTypeEnum } from '../../enums/sender-type.enum';

/**
 * Interface cho cấu hình SMS integration (từ app)
 */
export interface SmsIntegrationConfig {
  id: string;
  integrationName: string;
  typeId: number;
  metadata?: Record<string, any>;
  provider?: string; // Legacy field for backward compatibility
  providerName?: string; // Primary provider field
  endpoint?: string;
  additionalSettings?: Record<string, any>;
}

/**
 * DTO cho recipient trong SMS marketing job
 */
export interface SmsRecipientDto {
  /**
   * Số điện thoại người nhận
   */
  phone: string;

  /**
   * ID của audience (optional, từ app)
   */
  audienceId?: number;

  /**
   * Custom fields của audience (optional, từ app)
   */
  customFields?: Record<string, any>;
}

/**
 * DTO cho job SMS marketing trong queue
 */
export interface SmsMarketingJobDto {
  /**
   * ID của campaign
   */
  campaignId: number;

  /**
   * Nội dung SMS đã được xử lý sẵn từ app
   */
  content: string;

  /**
   * Danh sách người nhận
   */
  recipients: SmsRecipientDto[];

  /**
   * ID của SMS server configuration (deprecated - use smsServerConfig or smsIntegrationConfig)
   */
  smsServerId?: number;

  /**
   * SMS server configuration (pre-calculated from app) - legacy format
   */
  smsServerConfig?: Record<string, any>;

  /**
   * SMS integration configuration (new format from app)
   */
  smsIntegrationConfig?: SmsIntegrationConfig;



  /**
   * Loại chiến dịch SMS
   */
  campaignType: SmsCampaignType;

  /**
   * Tên campaign cho FPT SMS (dùng cho brandname SMS)
   */
  campaignName?: string;

  /**
   * Timestamp tạo job
   */
  createdAt: number;

  /**
   * Thời gian lên lịch gửi (Unix timestamp)
   * Bắt buộc đối với ADS campaign
   */
  scheduledAt?: number;

  /**
   * Flag để phân biệt campaign admin và user
   */
  isAdminCampaign?: boolean;

  /**
   * Provider-specific options (Twilio, generic providers)
   */
  providerOptions?: {
    provider?: string;
    [key: string]: any;
  };

  /**
   * Loại người gửi SMS (USER hoặc EMPLOYEE)
   */
  senderType?: SenderTypeEnum;

  /**
   * ID của người gửi SMS (user_id hoặc employee_id)
   */
  senderId?: number;

  /**
   * ID của user sở hữu campaign (từ app)
   */
  userId?: number;

  /**
   * ID của template SMS (từ app)
   */
  templateId?: number | null;

  /**
   * Template variables (từ app)
   */
  templateVariables?: Record<string, any>;
}
