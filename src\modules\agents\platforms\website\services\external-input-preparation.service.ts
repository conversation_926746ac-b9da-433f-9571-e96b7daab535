import { Injectable, Logger } from '@nestjs/common';
import { HumanMessage } from '@langchain/core/messages';
import { v7 } from 'uuid';
import { ExternalConversationMessage } from '../../../entities/externals/external-conversation-message.entity';
import { ExternalAttachmentProcessingService } from './external-attachment-processing.service';
import {
  KnowledgeFileWithMessageId,
  LangChainInput,
  MediaDataWithMessageId,
} from 'src/modules/agents/interfaces';

/**
 * Input Preparation Service for Website Platform
 *
 * Handles input preparation and formatting for LangGraph execution.
 * Converts external conversation messages and attachments into proper LangGraph input format.
 *
 * Now supports both image and knowledge file attachments (same as in-app platform)
 * Note: Website platform doesn't use tool call decisions (that's in-app only)
 */
@Injectable()
export class ExternalInputPreparationService {
  private readonly logger = new Logger(ExternalInputPreparationService.name);

  constructor(
    private readonly attachmentProcessing: ExternalAttachmentProcessingService,
  ) {}

  /**
   * Build LangChain input from external conversation messages
   * Converts external messages and attachments to Lang<PERSON>hain format for planner-executor graph
   * Now supports both images and knowledge files
   *
   * @param param - Input parameters with external messages and attachments
   * @returns LangChainInput with processed messages
   */
  async buildLangChainInput(param: {
    userMessages: ExternalConversationMessage[];
    messageSpecificImages: MediaDataWithMessageId[];
    messageSpecificKnowledgeFiles: KnowledgeFileWithMessageId[];
  }): Promise<LangChainInput> {
    const {
      userMessages,
      messageSpecificImages,
      messageSpecificKnowledgeFiles,
    } = param;

    this.logger.debug('Building LangChain input for website platform', {
      messageCount: userMessages.length,
      imageCount: messageSpecificImages.length,
      knowledgeFileCount: messageSpecificKnowledgeFiles.length,
    });

    try {
      // Build text messages (similar to in-app flow)
      const textMessages = userMessages
        .filter((msg) => msg.text)
        .map((msg) => msg.text)
        .join('\n\n');

      const content = [
        {
          type: 'text',
          text: textMessages,
        },
      ];

      const humanMessageText = new HumanMessage({
        id: v7(),
        content,
      });

      // Convert images to LangChain format (similar to in-app flow)
      const humanMessageImage =
        await this.attachmentProcessing.convertImagesToLangChain(
          messageSpecificImages,
        );

      const result = {
        messages: [humanMessageText, ...(humanMessageImage || [])],
      };

      this.logger.debug('Successfully built LangChain input', {
        totalMessages: result.messages.length,
        textMessages: 1,
        imageMessages: humanMessageImage?.length || 0,
        knowledgeFileCount: messageSpecificKnowledgeFiles.length,
      });

      return result;
    } catch (error) {
      this.logger.error('Failed to build LangChain input', {
        messageCount: userMessages.length,
        imageCount: messageSpecificImages.length,
        knowledgeFileCount: messageSpecificKnowledgeFiles.length,
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }
}
