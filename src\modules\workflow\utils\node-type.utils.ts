import { ENodeType } from '../interfaces/node-manager.interface';

/**
 * Utility functions để chuyển đổi giữa string và ENodeType
 */
export class NodeTypeUtils {
  
  /**
   * Chuyển đổi từ string sang ENodeType
   * @param nodeTypeString - String representation của node type
   * @returns ENodeType enum value hoặc undefined nếu không tìm thấy
   */
  static stringToNodeType(nodeTypeString: string): ENodeType | undefined {
    // Normalize input string (trim và lowercase)
    const normalizedString = nodeTypeString?.trim().toLowerCase();
    
    if (!normalizedString) {
      return undefined;
    }

    // Tìm kiếm trong tất cả values của ENodeType enum
    const nodeTypeValues = Object.values(ENodeType) as string[];
    const foundNodeType = nodeTypeValues.find(
      value => value.toLowerCase() === normalizedString
    );

    return foundNodeType as ENodeType | undefined;
  }

  /**
   * Chuy<PERSON><PERSON> đổ<PERSON> từ ENodeType sang string
   * @param nodeType - ENodeType enum value
   * @returns String representation của node type
   */
  static nodeTypeToString(nodeType: ENodeType): string {
    return nodeType as string;
  }

  /**
   * Kiểm tra xem một string có phải là valid ENodeType không
   * @param nodeTypeString - String cần kiểm tra
   * @returns true nếu string là valid ENodeType
   */
  static isValidNodeType(nodeTypeString: string): boolean {
    return this.stringToNodeType(nodeTypeString) !== undefined;
  }

  /**
   * Lấy tất cả node types dưới dạng string array
   * @returns Array của tất cả node type strings
   */
  static getAllNodeTypeStrings(): string[] {
    return Object.values(ENodeType) as string[];
  }

  /**
   * Lấy tất cả node types dưới dạng ENodeType array
   * @returns Array của tất cả ENodeType enum values
   */
  static getAllNodeTypes(): ENodeType[] {
    return Object.values(ENodeType);
  }

  /**
   * Chuyển đổi từ string sang ENodeType với fallback value
   * @param nodeTypeString - String representation của node type
   * @param fallback - Fallback value nếu conversion thất bại
   * @returns ENodeType enum value hoặc fallback value
   */
  static stringToNodeTypeWithFallback(
    nodeTypeString: string, 
    fallback: ENodeType
  ): ENodeType {
    const result = this.stringToNodeType(nodeTypeString);
    return result !== undefined ? result : fallback;
  }

  /**
   * Chuyển đổi array of strings sang array of ENodeTypes
   * @param nodeTypeStrings - Array of string representations
   * @returns Array of ENodeType enum values (filtered out invalid ones)
   */
  static stringArrayToNodeTypeArray(nodeTypeStrings: string[]): ENodeType[] {
    return nodeTypeStrings
      .map(str => this.stringToNodeType(str))
      .filter((nodeType): nodeType is ENodeType => nodeType !== undefined);
  }

  /**
   * Chuyển đổi array of ENodeTypes sang array of strings
   * @param nodeTypes - Array of ENodeType enum values
   * @returns Array of string representations
   */
  static nodeTypeArrayToStringArray(nodeTypes: ENodeType[]): string[] {
    return nodeTypes.map(nodeType => this.nodeTypeToString(nodeType));
  }

  /**
   * Tìm kiếm node types theo pattern (case-insensitive)
   * @param pattern - Pattern để tìm kiếm (có thể là partial match)
   * @returns Array of matching ENodeType enum values
   */
  static searchNodeTypes(pattern: string): ENodeType[] {
    const normalizedPattern = pattern.trim().toLowerCase();
    
    if (!normalizedPattern) {
      return [];
    }

    return this.getAllNodeTypes().filter(nodeType => {
      const nodeTypeString = this.nodeTypeToString(nodeType).toLowerCase();
      return nodeTypeString.includes(normalizedPattern);
    });
  }

  /**
   * Lấy display name cho node type (convert kebab-case to Title Case)
   * @param nodeType - ENodeType enum value
   * @returns Display name string
   */
  static getDisplayName(nodeType: ENodeType): string {
    const nodeTypeString = this.nodeTypeToString(nodeType);
    
    // Convert kebab-case to Title Case
    return nodeTypeString
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  /**
   * Lấy display names cho array of node types
   * @param nodeTypes - Array of ENodeType enum values
   * @returns Array of display name strings
   */
  static getDisplayNames(nodeTypes: ENodeType[]): string[] {
    return nodeTypes.map(nodeType => this.getDisplayName(nodeType));
  }

  /**
   * Group node types theo category (dựa trên prefix)
   * @returns Object với key là category và value là array of node types
   */
  static groupNodeTypesByCategory(): Record<string, ENodeType[]> {
    const groups: Record<string, ENodeType[]> = {};
    
    this.getAllNodeTypes().forEach(nodeType => {
      const nodeTypeString = this.nodeTypeToString(nodeType);
      
      // Determine category based on node type string
      let category = 'Other';
      
      if (nodeTypeString.startsWith('http')) {
        category = 'HTTP';
      } else if (nodeTypeString.includes('condition') || 
                 nodeTypeString.includes('switch') || 
                 nodeTypeString.includes('loop') ||
                 nodeTypeString.includes('filter') ||
                 nodeTypeString.includes('merge') ||
                 nodeTypeString.includes('wait')) {
        category = 'Logic';
      } else if (nodeTypeString.includes('webhook')) {
        category = 'Trigger';
      } else if (nodeTypeString.includes('edit') || 
                 nodeTypeString.includes('transform')) {
        category = 'Transform';
      }
      
      if (!groups[category]) {
        groups[category] = [];
      }
      
      groups[category].push(nodeType);
    });
    
    return groups;
  }

  /**
   * Validate và normalize node type string
   * @param nodeTypeString - String cần validate
   * @returns Normalized string hoặc null nếu invalid
   */
  static validateAndNormalize(nodeTypeString: string): string | null {
    const nodeType = this.stringToNodeType(nodeTypeString);
    return nodeType ? this.nodeTypeToString(nodeType) : null;
  }

  /**
   * Create mapping object từ string to ENodeType
   * @returns Object mapping string keys to ENodeType values
   */
  static createStringToNodeTypeMap(): Record<string, ENodeType> {
    const map: Record<string, ENodeType> = {};
    
    this.getAllNodeTypes().forEach(nodeType => {
      const nodeTypeString = this.nodeTypeToString(nodeType);
      map[nodeTypeString] = nodeType;
      map[nodeTypeString.toLowerCase()] = nodeType;
    });
    
    return map;
  }

  /**
   * Create mapping object từ ENodeType to string
   * @returns Object mapping ENodeType keys to string values
   */
  static createNodeTypeToStringMap(): Record<ENodeType, string> {
    const map = {} as Record<ENodeType, string>;
    
    this.getAllNodeTypes().forEach(nodeType => {
      map[nodeType] = this.nodeTypeToString(nodeType);
    });
    
    return map;
  }
}
