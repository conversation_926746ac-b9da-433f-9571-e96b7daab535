import { Injectable, Logger } from '@nestjs/common';
import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { z } from 'zod';
import { ZaloJobData } from './interfaces/zalo-info.interface';
import { GraphJobName, QueueName } from '../../../../queue';
import { RunStatusService } from '../../../../shared/services/run-status.service';
import { 
  ExternalMessageOperationsService,
  ExternalInputPreparationService,
  ExternalAttachmentProcessingService,
} from '../../domains/external/services';
import { ZaloAgentConfigurationService } from './services/zalo-agent-configuration.service';
import { ZaloExecutionCoordinatorService } from './services/zalo-execution-coordinator.service';
import { ZaloEventProcessorService } from './services/zalo-event-processor.service';
import { ZaloPostProcessingService } from './services/zalo-post-processing.service';
import { PlannerExecutorGraph } from '../../shared/graphs/planner-executor.graph';
import {
  ReplyToMessagesContext,
  ExecutionInput,
  ThreadMediaAttachments,
  ThreadKnowledgeFileAttachments,
  LangChainInput,
} from '../../shared/interfaces';
import { UserAbortError } from '../../shared/errors';
import { ZaloPlannerExecutorConfig } from './graph-configs/zalo-planner-executor-config.interface';
import { StreamEventType, Platform } from '../../shared/enums';
import { TokenUsageCollector } from '../../shared/utils';

@Injectable()
@Processor(QueueName.ZALO_AI)
export class ZaloAiProcessor extends WorkerHost {
  private readonly logger = new Logger(ZaloAiProcessor.name);

  constructor(
    private readonly runStatusService: RunStatusService,
    private readonly messageOperations: ExternalMessageOperationsService,
    private readonly inputPreparation: ExternalInputPreparationService,
    private readonly attachmentProcessing: ExternalAttachmentProcessingService,
    private readonly zaloAgentConfiguration: ZaloAgentConfigurationService,
    private readonly zaloExecutionCoordinator: ZaloExecutionCoordinatorService,
    private readonly zaloEventProcessor: ZaloEventProcessorService,
    private readonly zaloPostProcessing: ZaloPostProcessingService,
    private readonly plannerExecutorGraph: PlannerExecutorGraph,
  ) {
    super();
  }

  /**
   * Main job processing method
   * Only handles trigger operations - cancellation via Redis/progress monitoring
   * Job name = graph type
   */
  async process(job: Job<ZaloJobData, any, GraphJobName>): Promise<void> {
    const jobData = job.data;
    this.logger.log(`Emitting RUN_STARTED event for run `, JSON.stringify(jobData));
    const jobName: GraphJobName = job.name;
    switch (jobName) {
      case GraphJobName.PLANNER_EXECUTOR:
        await this.handlePlannerExecutor(jobData);
        break;
      case GraphJobName.SUPERVISOR_WORKERS:
        this.logger.warn(
          'Supervisor workers not implemented for Zalo platform',
        );
        break;
      default:
        const message = `Unknown job name: ${jobName}`;
        this.logger.error(message);
        throw new Error(message);
    }
  }

  /**
   * Handle planner-executor job - 7-phase execution workflow
   *
   * Phase 1: Job Reception & Validation ✅
   * Phase 2: Run Initialization & Message Processing ✅
   * Phase 3: Input & Context Preparation ✅
   * Phase 4: Agent Configuration & System Prompting ✅
   * Phase 5: LangGraph Execution Setup ✅
   * Phase 6: LangGraph Streaming Execution ✅
   * Phase 7: Completion & Cleanup ✅
   *
   * Planner-executor pattern for Zalo chat interactions
   */
  private async handlePlannerExecutor(jobData: ZaloJobData): Promise<void> {
    this.logger.log(
      `Triggering run: ${jobData.runId} for thread: ${jobData.threadId}`,
    );

    const { keys } = jobData;
    if (!keys) {
      this.logger.error('Job data keys are missing');
      throw new Error('Job data keys are required for processing');
    }

    // ✅ Phase 1: Job Reception & Validation
    this.validateJobDataKeys(jobData);
    const platformThreadId = keys.platformThreadId;

    this.logger.debug('Phase 1 completed: Job Reception & Validation', {
      runId: jobData.runId,
      threadId: jobData.threadId,
      platformThreadId,
      runStatusKey: keys.runStatusKey,
      oaId: jobData.humanInfo.zaloInfo.oaId,
    });

    try {
      // ✅ Phase 2: Run Initialization & Message Processing
      this.logger.debug(
        'Phase 2: Starting Run Initialization & Message Processing',
      );

      // 2.1 Create and activate run with context
      const activated = await this.runStatusService.activateRun(
        platformThreadId,
        jobData.runId,
        {
          agentId: jobData.mainAgentId,
          userId: jobData.humanInfo.zaloUser.id,
          platform: jobData.platform,
          workerAgentCount: 0, // Planner-executor has no workers
        },
      );

      if (!activated) {
        this.logger.error(
          'Failed to activate run - may have been cancelled or invalid',
          {
            threadId: jobData.threadId,
            runId: jobData.runId,
          },
        );
        return;
      }

      // 2.2 Get UNPROCESSED USER messages from database (only new messages)
      const userMessages =
        await this.messageOperations.fetchUnprocessedUserMessages(
          jobData.threadId,
          Platform.ZALO,
        );

      if (userMessages.length === 0) {
        this.logger.warn(
          `No USER messages found for thread ${jobData.threadId}`,
        );
        return;
      }

      this.logger.debug(
        `Found ${userMessages.length} user messages for thread ${jobData.threadId}`,
      );

      // 2.3 Build reply-to message context
      const replyToMessageIds = userMessages
        .filter((msg) => !!msg.replyingToMessageId)
        .map((msg) => msg.replyingToMessageId)
        .filter((id): id is string => id !== undefined);

      const replyToMessages =
        await this.messageOperations.fetchReplyToMessages(
          replyToMessageIds,
          Platform.ZALO,
        );

      this.logger.debug(
        'Phase 2 completed: Run Initialization & Message Processing',
        {
          threadId: jobData.threadId,
          userMessageCount: userMessages.length,
          replyToMessageCount: replyToMessageIds.length,
        },
      );

      // ✅ Phase 3: Input & Context Preparation
      this.logger.debug('Phase 3: Starting Input & Context Preparation', {
        threadId: jobData.threadId,
        userMessageCount: userMessages.length,
        replyToMessageCount: replyToMessageIds.length,
      });

      // Declare variables upfront for consistency
      let input: ExecutionInput;
      let replyToMessagesContext: ReplyToMessagesContext = [];
      let threadMediaAttachments: ThreadMediaAttachments = [];
      let threadKnowledgeFileAttachments: ThreadKnowledgeFileAttachments = [];

      // 3.1 Process thread attachments (fetch ALL images and knowledge files for system prompts)
      const threadAttachmentResult =
        await this.attachmentProcessing.processThreadAttachments(
          jobData.threadId,
        );
      const threadAttachmentsWithMessages =
        threadAttachmentResult.threadAttachmentsWithMessages;
      threadMediaAttachments = threadAttachmentResult.threadMediaAttachments;
      threadKnowledgeFileAttachments =
        threadAttachmentResult.threadKnowledgeFileAttachments;

      // 3.2 Process message-specific attachments (in-memory filtering)
      const {
        messageSpecificImages,
        replyToSpecificImages,
        messageSpecificKnowledgeFiles,
        replyToSpecificKnowledgeFiles,
      } = await this.attachmentProcessing.processMessageAttachments(
        userMessages,
        replyToMessageIds,
        threadAttachmentsWithMessages,
      );

      // 3.3 Build reply context with attachment data
      replyToMessagesContext =
        await this.messageOperations.buildReplyToMessagesContext({
          replyToMessages,
          replyToSpecificImages,
          replyToSpecificKnowledgeFiles,
        });

      // 3.4 Build final LangChain input with messages + attachments
      input = await this.inputPreparation.buildLangChainInput({
        userMessages,
        messageSpecificImages,
        messageSpecificKnowledgeFiles,
      });

      this.logger.debug('Phase 3 completed: Input & Context Preparation', {
        threadId: jobData.threadId,
        threadMediaAttachmentCount: Object.keys(threadMediaAttachments).length,
        threadKnowledgeFileAttachmentCount: Object.keys(
          threadKnowledgeFileAttachments,
        ).length,
        messageSpecificImageCount: messageSpecificImages.length,
        replyToSpecificImageCount: replyToSpecificImages.length,
        inputMessageCount: input.messages.length,
      });

      // ✅ Check for cancellation before proceeding
      const cancelled =
        await this.runStatusService.isRunCancelled(platformThreadId);
      if (cancelled) {
        this.logger.warn(
          `Run ${jobData.runId} for thread ${jobData.threadId} was cancelled before processing`,
        );
        throw new UserAbortError(jobData.threadId, 'Run was cancelled');
      }

      // ✅ Phase 4: Agent Configuration & System Prompting
      this.logger.debug('Phase 4: Building agent configurations...');

      const { plannerAgent, executorAgent } =
        await this.zaloAgentConfiguration.buildPlannerExecutorConfiguration({
          zaloUser: jobData.humanInfo.zaloUser,
          zaloOwner: jobData.humanInfo.zaloOwner,
          zaloInfo: jobData.humanInfo.zaloInfo,
          threadMediaAttachments,
          threadKnowledgeFileAttachments,
          plannerAgentId: jobData.plannerAgentId,
          executorAgentId: jobData.mainAgentId, // mainAgentId is the executor
        });

      this.logger.debug('Phase 4 completed: Agent configurations built', {
        threadId: jobData.threadId,
        zaloUserId: jobData.humanInfo.zaloUser.id,
        zaloOwnerId: jobData.humanInfo.zaloOwner.userId,
        oaId: jobData.humanInfo.zaloInfo.oaId,
        plannerAgentConfigured: !!plannerAgent,
        executorAgentConfigured: !!executorAgent,
        plannerAgentId: jobData.plannerAgentId,
        executorAgentId: jobData.mainAgentId,
      });

      // ✅ Check for cancellation before proceeding to execution
      const cancelledAfterConfig =
        await this.runStatusService.isRunCancelled(platformThreadId);
      if (cancelledAfterConfig) {
        this.logger.warn(
          `Run ${jobData.runId} for thread ${jobData.threadId} was cancelled after agent configuration`,
        );
        throw new UserAbortError(
          jobData.threadId,
          'Run was cancelled after agent configuration',
        );
      }

      // ✅ Phase 5: LangGraph Execution Setup
      this.logger.debug('Phase 5: Starting LangGraph Execution Setup');

      // Build graph configurable object for PlannerExecutorGraph
      const plannerExecutorGraphConfig: ZaloPlannerExecutorConfig =
        this.zaloExecutionCoordinator.buildPlannerExecutorGraphConfigurable({
          jobData,
          plannerAgent,
          executorAgent,
          platformThreadId: jobData.keys.platformThreadId,
        });

      // Setup execution components: token collector, input, tags, abort controller
      const {
        tokenUsageCollector,
        input: graphInput,
        tags,
        abortController,
      } = await this.zaloExecutionCoordinator.setupLangGraphExecution(
        jobData,
        input,
        plannerExecutorGraphConfig,
      );

      this.logger.debug('Phase 5 completed: LangGraph Execution Setup', {
        threadId: jobData.threadId,
        runId: jobData.runId,
        hasPlannerAgent: !!plannerAgent,
        executorAgentId: executorAgent.id,
        tagCount: tags.length,
        inputMessageCount: graphInput.messages?.length || 0,
      });

      // ✅ Phase 6: LangGraph Streaming Execution
      this.logger.debug('Phase 6: Starting LangGraph Streaming Execution');

      await this.executeLangGraph({
        jobData,
        langGraphInput: input,
        graphConfigurable: plannerExecutorGraphConfig,
        streamKey: jobData.keys.streamKey,
        tokenUsageCollector,
        tags,
        abortController,
      });

      this.logger.debug('Phase 6 completed: LangGraph Streaming Execution', {
        threadId: jobData.threadId,
        runId: jobData.runId,
      });

      // ✅ Phase 7: Completion & Cleanup
      this.logger.debug('Phase 7: Starting Completion & Cleanup');

      // Mark messages as processed after successful execution
      const messageIds = userMessages.map((msg) => msg.id);
      await this.messageOperations.markMessagesAsProcessed(
        messageIds,
        jobData.threadId,
        Platform.ZALO,
      );

      this.logger.debug('Phase 7 completed: Messages marked as processed', {
        threadId: jobData.threadId,
        processedMessageCount: messageIds.length,
      });
    } catch (error) {
      this.logger.error(`Error processing run ${jobData.runId}:`, error);

      // Classify error type for appropriate handling
      const errorType = this.analyzeError(error);

      if (errorType === 'user_abort') {
        this.logger.log(
          `User aborted processing for thread: ${jobData.threadId}`,
        );
        await this.zaloEventProcessor.processLangGraphEvent(
          { event: StreamEventType.RUN_CANCELLED, reason: 'user_abort' },
          jobData,
          jobData.keys.streamKey,
          null,
        );
        // User pressed abort button - run already marked as cancelled
        return;
      }

      // Mark run as failed for non-abort errors
      await this.runStatusService.failRun(
        jobData.keys.platformThreadId,
        jobData.runId,
        (error as Error).message || 'Unknown error',
        (error as Error).stack,
      );

      // Publish RUN_ERROR event
      await this.zaloEventProcessor.processLangGraphEvent(
        { event: StreamEventType.RUN_ERROR, runId: jobData.runId },
        jobData,
        jobData.keys.streamKey,
        null,
      );

      // Real error - let it bubble up for BullMQ error handling
      throw error;
    } finally {
      // Basic cleanup logging - run status is managed in try/catch blocks
      this.logger.debug('Finished handlePlannerExecutor', {
        threadId: jobData.threadId,
        runId: jobData.runId,
      });
    }
  }

  /**
   * Execute PlannerExecutorGraph with streaming, cancellation monitoring, and token usage tracking
   * Includes Zalo OA owner billing after completion
   */
  private async executeLangGraph(param: {
    jobData: ZaloJobData;
    langGraphInput: LangChainInput;
    graphConfigurable: ZaloPlannerExecutorConfig;
    streamKey: string;
    tokenUsageCollector: TokenUsageCollector;
    tags: string[];
    abortController: AbortController;
  }): Promise<void> {
    const {
      jobData,
      langGraphInput,
      graphConfigurable,
      streamKey,
      tokenUsageCollector,
      tags,
      abortController,
    } = param;

    // Emit RunStarted event
    await this.zaloEventProcessor.processLangGraphEvent(
      { event: StreamEventType.RUN_STARTED, runId: jobData.runId },
      jobData,
      streamKey,
      null,
    );

    

    // Build LangGraph input from messages
    const input = { messages: langGraphInput.messages };

    let currentMessageId: string | null = null;

    try {
      // Execute PlannerExecutorGraph with streaming and token usage collection
      const stream = this.plannerExecutorGraph.executeStream({
        input,
        config: graphConfigurable,
        tags,
        callbacks: [tokenUsageCollector],
        signal: abortController.signal,
      });

      for await (const event of stream) {
        const metadata = event.metadata || {};
        this.logger.debug(
          `---------------------------------
          Event is from executor agent: ${metadata.agent_id}, event type: ${event.event}
          ${JSON.stringify(metadata, null, 2)}
          ---------------------------------`
        );
        // Check for cancellation using unified status
        const cancelled = await this.runStatusService.isRunCancelled(
          jobData.keys.platformThreadId,
        );
        if (cancelled) {
          this.logger.log(
            `Thread ${jobData.threadId} cancelled via RunStatusService`,
            {
              threadId: jobData.threadId,
              runId: jobData.runId,
            },
          );

          // Abort the LangGraph execution
          abortController.abort();
        }

        // Process each LangGraph event
        const result = await this.zaloEventProcessor.processLangGraphEvent(
          event,
          jobData,
          streamKey,
          currentMessageId,
        );
        if (result.newMessageId !== undefined) {
          currentMessageId = result.newMessageId;
        }
        if (result.messageUpdated) {
          tokenUsageCollector.clearAccumulatedText(jobData.mainAgentId);
        }
      }

      this.logger.log(
        `Completed LangGraph execution for run: ${jobData.runId}`,
      );

      // Emit RunComplete event on successful completion
      const usageSummary = tokenUsageCollector.getUsageSummary();
      await this.zaloEventProcessor.processLangGraphEvent(
        {
          event: StreamEventType.RUN_COMPLETE,
          totalCost: usageSummary.totalCost,
        },
        jobData,
        streamKey,
        null,
      );
    } catch (error) {
      // Don't emit RUN_ERROR for cancellation errors - they already emitted RUN_CANCELLED
      if ((error as Error).message !== 'Aborted') {
        this.logger.error(
          `LangGraph execution failed for run ${jobData.runId}:`,
          error,
        );

        // Emit RunError event only for real errors
        await this.zaloEventProcessor.processLangGraphEvent(
          {
            event: StreamEventType.RUN_ERROR,
            error: {
              type: (error as Error).constructor.name || 'Error',
              message: (error as Error).message || 'Unknown error',
              code: (error as Record<string, unknown>).code as string,
              stack: (error as Error).stack,
            },
          },
          jobData,
          streamKey,
          null,
        );
      } else {
        // LangGraph abort - handle gracefully without trying to determine reason
        this.logger.debug('Job was cancelled/aborted', {
          runId: jobData.runId,
          threadId: jobData.threadId,
        });

        // Always emit RUN_CANCELLED for any abort
        await this.zaloEventProcessor.processLangGraphEvent(
          { event: StreamEventType.RUN_CANCELLED, reason: 'aborted' },
          jobData,
          streamKey,
          null,
        );

        // Throw a generic cancellation error
        throw new UserAbortError(jobData.threadId, 'Job was cancelled');
      }
      throw error;
    } finally {
      // Post-stream operations - always execute regardless of success/failure
      await this.zaloPostProcessing.performPostStreamOperations(
        jobData,
        tokenUsageCollector,
        currentMessageId,
      );
    }
  }

  /**
   * Analyze error type to determine handling strategy
   * Uses custom error types for proper classification between user aborts and real errors
   */
  private analyzeError(error: unknown): 'real_error' | 'user_abort' {
    // Check custom error types
    if (error instanceof UserAbortError) {
      return 'user_abort';
    }

    // Real error - not a user cancellation
    return 'real_error';
  }

  /**
   * Validates Zalo-specific Redis key patterns
   * Validates both format AND components match jobData threadId/runId
   * 🔒 SECURITY: Ensures worker receives valid keys before processing
   */
  private validateJobDataKeys(jobData: ZaloJobData): void {
    const { threadId, runId } = jobData;

    const JobDataKeysSchema = z.object({
      keys: z.object({
        platformThreadId: z.literal(`zalo:${threadId}`),
        runStatusKey: z.literal(`run_status:zalo:${threadId}`),
        streamKey: z.literal(`zalo:agent_stream:${threadId}:${runId}`),
      }),
    });

    JobDataKeysSchema.parse(jobData);

    this.logger.debug('Job data keys validation passed', {
      threadId,
      runId,
      runStatusKey: jobData.keys.runStatusKey,
      zaloUserId: jobData.humanInfo.zaloUser.id,
      oaId: jobData.humanInfo.zaloInfo.oaId,
    });
  }
}