import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { QueueName, WebhookJobName } from '../../queue';
import { UserAddonUsage } from '../../shared/entities/user-addon-usage.entity';
import { Addon } from '../../shared/entities/addon.entity';
import { AddonType } from '../../shared/enums/addon-type.enum';

/**
 * Interface cho data của job trừ dung lượng
 */
interface UsageDeductionJobData {
  userId: number;
  addonType: string;
  usageAmount: number;
  eventId: string;
  eventType: string;
  metadata?: Record<string, any>;
  timestamp: number;
}

/**
 * Processor xử lý trừ dung lượng user addon usage
 * <PERSON><PERSON><PERSON><PERSON> trigger từ webhook events qua queue system
 */
@Injectable()
@Processor(QueueName.WEBHOOK)
export class UsageDeductionProcessor extends WorkerHost {
  private readonly logger = new Logger(UsageDeductionProcessor.name);

  constructor(
    @InjectRepository(UserAddonUsage)
    private readonly userAddonUsageRepository: Repository<UserAddonUsage>,

    @InjectRepository(Addon)
    private readonly addonRepository: Repository<Addon>,
  ) {
    super();
    this.logger.log('🚀 UsageDeductionProcessor initialized and ready to process jobs');
  }

  /**
   * Xử lý job trừ dung lượng user addon usage và analytics
   */
  async process(job: Job<UsageDeductionJobData | any>): Promise<void> {
    // Chỉ xử lý job DEDUCT_USER_ADDON_USAGE và PROCESS_ANALYTICS
    if (job.name !== WebhookJobName.DEDUCT_USER_ADDON_USAGE &&
        job.name !== WebhookJobName.PROCESS_ANALYTICS) {
      this.logger.debug(`⏭️ Skipping non-usage job: ${job.name} (ID: ${job.id})`);
      return;
    }

    this.logger.log(`🔍 Received job: ${job.name} (ID: ${job.id})`);
    this.logger.log(`🔍 Job data: ${JSON.stringify(job.data)}`);

    // Xử lý job DEDUCT_USER_ADDON_USAGE
    if (job.name === WebhookJobName.DEDUCT_USER_ADDON_USAGE) {
      await this.handleUsageDeduction(job);
      return;
    }

    // Xử lý job PROCESS_ANALYTICS
    if (job.name === WebhookJobName.PROCESS_ANALYTICS) {
      await this.handleAnalytics(job);
      return;
    }
  }

  private async handleUsageDeduction(job: Job<UsageDeductionJobData>): Promise<void> {
    const { userId, addonType, usageAmount, eventId, eventType, metadata } = job.data;
    const startTime = Date.now();

    try {
      this.logger.log(
        `🔄 Processing usage deduction: User ${userId}, Addon ${addonType}, Amount ${usageAmount} ` +
        `(Event: ${eventType}, Job: ${job.id})`
      );

      // 1. Validate input data
      await this.validateJobData(job.data);

      // 2. Tìm addon theo type
      const addon = await this.findAddonByType(addonType);
      if (!addon) {
        throw new Error(`Addon not found for type: ${addonType}`);
      }

      // 3. Tìm user addon usage active
      const userAddonUsage = await this.findActiveUserAddonUsage(userId, addon.id);
      if (!userAddonUsage) {
        throw new Error(
          `No active addon usage found for user ${userId}, addon ${addonType}`
        );
      }

      // 4. Kiểm tra còn dung lượng không
      if (userAddonUsage.remainingValue < usageAmount) {
        throw new Error(
          `Insufficient usage remaining: ${userAddonUsage.remainingValue} < ${usageAmount} ` +
          `for user ${userId}, addon ${addonType}`
        );
      }

      // 5. Trừ dung lượng
      await this.deductUsage(userAddonUsage.id, usageAmount);

      // 6. Log thành công
      const processingTime = Date.now() - startTime;
      this.logger.log(
        `✅ Usage deducted successfully: User ${userId}, Addon ${addonType}, ` +
        `Amount ${usageAmount}, Remaining: ${userAddonUsage.remainingValue - usageAmount} ` +
        `(${processingTime}ms, Event: ${eventType})`
      );

      // 7. Log analytics nếu cần
      if (metadata?.enableAnalytics !== false) {
        await this.logUsageAnalytics({
          userId,
          addonType,
          usageAmount,
          eventType,
          eventId,
          processingTime,
          remainingAfter: userAddonUsage.remainingValue - usageAmount,
        });
      }

    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.logger.error(
        `❌ Failed to deduct usage: User ${userId}, Addon ${addonType}, ` +
        `Amount ${usageAmount}, Error: ${error.message} (${processingTime}ms)`
      );

      // Re-throw để Bull retry
      throw error;
    }
  }

  /**
   * Validate job data
   */
  private async validateJobData(data: UsageDeductionJobData): Promise<void> {
    const { userId, addonType, usageAmount } = data;

    if (!userId || userId <= 0) {
      throw new Error(`Invalid userId: ${userId}`);
    }

    if (!addonType || typeof addonType !== 'string') {
      throw new Error(`Invalid addonType: ${addonType}`);
    }

    if (!usageAmount || usageAmount <= 0) {
      throw new Error(`Invalid usageAmount: ${usageAmount}`);
    }

    // Validate addon type exists in enum
    const validAddonTypes = Object.values(AddonType);
    if (!validAddonTypes.includes(addonType as AddonType)) {
      throw new Error(`Invalid addon type: ${addonType}. Valid types: ${validAddonTypes.join(', ')}`);
    }
  }

  /**
   * Tìm addon theo type
   */
  private async findAddonByType(addonType: string): Promise<Addon | null> {
    return this.addonRepository.findOne({
      where: { 
        type: addonType as AddonType,
        isActive: true 
      }
    });
  }

  /**
   * Tìm user addon usage đang active
   */
  private async findActiveUserAddonUsage(
    userId: number,
    addonId: number
  ): Promise<UserAddonUsage | null> {
    const now = Date.now();

    return this.userAddonUsageRepository
      .createQueryBuilder('usage')
      .where('usage.userId = :userId', { userId })
      .andWhere('usage.addonId = :addonId', { addonId })
      .andWhere('usage.status = :status', { status: 'ACTIVE' })
      .andWhere('usage.usagePeriodStart <= :now', { now })
      .andWhere('usage.usagePeriodEnd >= :now', { now })
      .andWhere('usage.remainingValue > 0')
      .orderBy('usage.usagePeriodEnd', 'ASC') // Ưu tiên addon sắp hết hạn
      .getOne();
  }

  /**
   * Trừ dung lượng
   */
  private async deductUsage(usageId: number, amount: number): Promise<void> {
    const result = await this.userAddonUsageRepository
      .createQueryBuilder()
      .update(UserAddonUsage)
      .set({
        currentUsage: () => 'COALESCE(current_usage, 0) + :amount',
        remainingValue: () => 'remaining_value - :amount',
        lastUpdatedAt: Date.now()
      })
      .where('id = :usageId', { usageId })
      .andWhere('remaining_value >= :amount', { amount })
      .setParameters({ amount })
      .execute();

    if (result.affected === 0) {
      throw new Error(
        `Failed to deduct usage: No rows affected. ` +
        `UsageId: ${usageId}, Amount: ${amount}`
      );
    }
  }

  /**
   * Xử lý job analytics
   */
  private async handleAnalytics(job: Job<any>): Promise<void> {
    const startTime = Date.now();

    try {
      this.logger.log(`📊 Processing analytics job: ${job.id}`);

      const { event, timestamp } = job.data;

      // Log analytics data
      this.logger.debug(
        `📊 Analytics data: Event=${event?.event}, ` +
        `User=${event?.user?.id}, Timestamp=${timestamp}`
      );

      // TODO: Implement actual analytics processing
      // - Store analytics data in database
      // - Send to analytics service
      // - Update metrics/counters

      const processingTime = Date.now() - startTime;
      this.logger.log(
        `✅ Analytics processed successfully: Job ${job.id} (${processingTime}ms)`
      );

    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.logger.error(
        `❌ Failed to process analytics: Job ${job.id}, Error: ${error.message} (${processingTime}ms)`
      );

      // Don't throw error to avoid job retry for analytics
      // Analytics failures shouldn't block the system
    }
  }

  /**
   * Log analytics cho usage deduction
   */
  private async logUsageAnalytics(data: {
    userId: number;
    addonType: string;
    usageAmount: number;
    eventType: string;
    eventId: string;
    processingTime: number;
    remainingAfter: number;
  }): Promise<void> {
    try {
      // TODO: Implement analytics logging
      // Có thể gửi đến analytics service hoặc log vào database
      this.logger.debug(
        `📊 Usage analytics: ${JSON.stringify(data)}`
      );
    } catch (error) {
      this.logger.warn(`Failed to log usage analytics: ${error.message}`);
      // Không throw error để không ảnh hưởng đến main process
    }
  }
}
