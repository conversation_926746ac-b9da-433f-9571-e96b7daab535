import { Injectable, Logger } from '@nestjs/common';
import { Platform, ModelFeature, InputModality } from '../../../shared/enums';
import { AgentConfig } from '../../../shared/interfaces/agent-config.interface';
import { McpConfiguration } from '../../../shared/interfaces/mcp-config.interface';
import { McpClientService } from '../../../shared/services';
import { WebSearchTool } from '../../../shared/tools/web-search.tool';
import { AgentMemoryTool } from '../../../shared/tools/agent-memory.tool';
import { ImageLoaderTool } from '../../../shared/tools/image-loader.tool';
import { AuthContext, PlatformStrategy } from '../../../shared/interfaces';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { Integration, UserConverts } from '../../../shared/entities';
import { UserConvertCustomerMemoryTool } from '../../../shared/tools/manage-visitor-memory.tool';
import { AgentKnowledgeRAGTool } from '../../../shared/tools/agent-knowledge-rag.tool';
import { AgentProductRAGTool } from '../../../shared/tools/agent-product-rag.tool';
import { AgentMediaRAGTool } from '../../../shared/tools/agent-media-rag.tool';
import { InjectRepository } from '@nestjs/typeorm';
import { UserConvertCustomer } from 'src/modules/agents/domains/external/entities';
import { tool } from '@langchain/core/tools';
import { UpdateVisitorInfoTool } from 'src/modules/agents/shared/tools/update-visitor-info.tool';

@Injectable()
export class ZaloPlatformStrategy implements PlatformStrategy {
  private readonly logger = new Logger(ZaloPlatformStrategy.name);
  readonly platform = Platform.ZALO;

  constructor(
    private readonly mcpClientService: McpClientService,
    private readonly webSearchService: WebSearchTool,
    private readonly agentMemoryService: AgentMemoryTool,
    private readonly imageLoaderTool: ImageLoaderTool,
    private readonly websiteUserConvertCustomerMemoryTool: UserConvertCustomerMemoryTool,
    private readonly agentKnowledgeRAGTool: AgentKnowledgeRAGTool,
    private readonly agentMediaRAGTool: AgentMediaRAGTool,
    private readonly agentProductRAGTool: AgentProductRAGTool,
    private readonly updateVisitorInfoTool: UpdateVisitorInfoTool,
    @InjectRepository(UserConvertCustomer)
    private readonly userConvertCustomerRepository: Repository<UserConvertCustomer>,
    @InjectRepository(UserConverts)
    private readonly userConvertsRepository: Repository<UserConverts>,
  ) {}

  async buildMcpClient(
    mcpConfig: Record<string, McpConfiguration> | null,
    authContext: AuthContext,
  ): Promise<any> {
    // Zalo users don't have JWT, but we can still build MCP clients
    // using the Zalo OA owner's configuration
    return mcpConfig
      ? await this.mcpClientService.buildMcpClients(
          mcpConfig,
          undefined,
          this.platform,
          authContext,
        )
      : null;
  }

  getToolsForAgent(agentConfig: AgentConfig, authContext: AuthContext): any[] {
    const result = agentConfig.model.features.includes(ModelFeature.TOOL_CALL)
      ? [
          this.webSearchService,
          this.agentMemoryService,
          // Note: Zalo platform doesn't include user-specific RAG tools
          // since Zalo users don't have personal knowledge/media/products
          agentConfig.model.inputModalities.includes(InputModality.IMAGE)
            ? this.imageLoaderTool
            : undefined,
          this.websiteUserConvertCustomerMemoryTool,
          this.agentKnowledgeRAGTool,
          this.agentMediaRAGTool,
          this.agentProductRAGTool,
          this.updateVisitorInfoTool,
        ].filter(Boolean)
      : [];
    this.logger.debug(
      `getToolsForAgent: ${agentConfig.id} - ${result.length} tools available (Zalo platform)`,
    );
    return result
      .concat(...this.getConversionTool(agentConfig, authContext))
      .filter(Boolean);
  }

  getConversionTool(
    agentConfig: AgentConfig,
    authContext: AuthContext,
  ): any | undefined {
    const { zaloUserId, zaloOwnerId, zaloData } = authContext;
    const schema = agentConfig.specializedConfig?.convert || {
      type: 'object',
      required: ['conversion_detail'],
      properties: {
        conversion_detail: {
          type: 'object',
          anyOf: [
            { required: ['customer_email'] },
            { required: ['customer_phone'] },
          ],
          properties: {
            customer_email: {
              type: 'string',
              description: 'Email của khách hàng',
            },
            customer_phone: {
              type: 'string',
              description: 'Số điện thoại của khách hàng',
            },
          },
          additionalProperties: false,
        },
      },
      additionalProperties: false,
    };

    if (!schema) {
      this.logger.debug(
        `No conversion tool configured for agent ${agentConfig.id} on website platform`,
      );
      return undefined;
    }

    const createConversion = async (args: any) => {
      const { conversion_detail, ...rest } = args;
      const userConvertCustomer =
        await this.userConvertCustomerRepository.findOneBy({
          id: zaloUserId,
          userId: zaloOwnerId, // Use Zalo OA owner's ID
        });
      if (!userConvertCustomer) {
        throw new Error(`UserConvertCustomer with ID ${zaloUserId} not found`);
      }
      if (conversion_detail.customer_email) {
        const existingEmail = userConvertCustomer.email || [];
        const emailSet = new Set(existingEmail);
        emailSet.add(conversion_detail.customer_email);
        userConvertCustomer.email = Array.from(emailSet);
      }
      await this.userConvertCustomerRepository.save(userConvertCustomer);
      this.logger.debug(
        `UserConvertCustomer updated: ${zaloUserId} - ${JSON.stringify(userConvertCustomer)}`,
      );
      const userConvert = this.userConvertsRepository.create({
        convertCustomerId: userConvertCustomer.id,
        userId: zaloOwnerId, // Assuming user_id is passed in args
        conversionType: 'website_conversion',
        source: 'website',
        convertCustomerPhone: conversion_detail.customer_phone || null,
        convertCustomerEmail: conversion_detail.customer_email || null,
        content: rest,
      });
      const savedConvert = await this.userConvertsRepository.save(userConvert);
      this.logger.debug(
        `UserConvert created: ${savedConvert.id} for customer ${userConvertCustomer.id}`,
      );
      return `conversion saved with ID: ${savedConvert.id}. Continue with your workflow as needed.`;
    };

    const updateConversion = async (args: any) => {
      const { conversion_detail, conversionId, ...rest } = args;
      const userConvertCustomer =
        await this.userConvertCustomerRepository.findOneBy({
          id: zaloUserId,
          userId: zaloOwnerId,
        });
      if (!userConvertCustomer) {
        throw new Error(`UserConvertCustomer with ID ${zaloUserId} not found`);
      }
      if (conversion_detail.customer_email) {
        const existingEmail = userConvertCustomer.email || [];
        const emailSet = new Set(existingEmail);
        emailSet.add(conversion_detail.customer_email);
        userConvertCustomer.email = Array.from(emailSet);
      }
      await this.userConvertCustomerRepository.save(userConvertCustomer);
      this.logger.debug(
        `UserConvertCustomer updated: ${zaloUserId} - ${JSON.stringify(userConvertCustomer)}`,
      );
      const userConvert = await this.userConvertsRepository.findOneBy({
        id: conversionId,
        userId: zaloOwnerId,
      });
      if (!userConvert) {
        throw new Error(`UserConvert with ID ${conversionId} not found`);
      }
      userConvert.convertCustomerPhone =
        conversion_detail.customer_phone || null;
      userConvert.convertCustomerEmail =
        conversion_detail.customer_email || null;
      userConvert.content = { ...(userConvert.content || {}), ...rest };
      const updatedConvert =
        await this.userConvertsRepository.save(userConvert);
      this.logger.debug(
        `UserConvert updated: ${updatedConvert.id} for customer ${userConvertCustomer.id}`,
      );
      return `conversion updated with ID: ${updatedConvert.id}. Continue with your workflow as needed.`;
    };

    const createTool = tool(createConversion, {
      name: 'website_conversion',
      description: `Tool to create a conversion for website agents. Requires userConvertCustomerId to identify the customer. No conversionId needed for creation.
          Input schema: ${JSON.stringify(schema)}`,
      schema: schema,
    });

    const updateTool = tool(updateConversion, {
      name: 'website_conversion_update',
      description: `Tool to update an existing conversion for website agents. Requires userConvertCustomerId and conversionId to identify the customer and conversion. conversionId is required for updates.
          Input schema: ${JSON.stringify(schema)}`,
      // shove the conversionId into the schema, it should be on both required and be on the same level as conversion_detail
      schema: {
        type: 'object',
        required: ['conversionId', 'conversion_detail'],
        properties: {
          conversionId: {
            type: 'string',
            description: 'ID of the conversion to update',
          },
          conversion_detail: schema.properties.conversion_detail,
        },
        additionalProperties: false,
      },
    });

    return [createTool, updateTool];
  }

  customizeIntegrationQuery(
    baseQuery: SelectQueryBuilder<Integration>,
    authContext: AuthContext,
  ): SelectQueryBuilder<Integration> {
    const { zaloOwnerId } = authContext;

    // Integration selection logic for Zalo platform
    // Use the Zalo OA owner's API keys (not the Zalo user's - they don't have any)
    if (zaloOwnerId) {
      // For Zalo agents: check use_system_key field using Zalo OA owner's ID
      return baseQuery.andWhere(
        `
        (
          (agent.use_system_key = true AND integration.employee_id IS NOT NULL) OR
          (agent.use_system_key = false AND integration.user_id = :zaloOwnerId)
        )
      `,
        { zaloOwnerId }, // Match the SQL parameter name
      );
    } else {
      // Fallback to system integrations if no Zalo OA owner identified
      return baseQuery.andWhere('integration.employee_id IS NOT NULL');
    }
  }
}
