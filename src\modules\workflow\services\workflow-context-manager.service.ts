import { Injectable, Logger } from '@nestjs/common';
import {
  WorkflowContext,
  WorkflowExecutionMetadata,
  JsonContextOptions,
  GetValueResult
} from '../interfaces/workflow-context.interface';
import { WorkflowContextService } from './workflow-context.service';

/**
 * ✅  WORKFLOW CONTEXT MANAGER
 * Service để quản lý WorkflowContext với type safety cao
 */
@Injectable()
export class WorkflowContextManagerService {
  private readonly logger = new Logger(WorkflowContextManagerService.name);
  private context: WorkflowContext;

  constructor(
    private readonly contextService: WorkflowContextService
  ) {}

  /**
   * Initialize manager với context
   */
  initialize(context?: WorkflowContext): this {
    this.context = context || {
      json: {},
      metadata: {
        executionId: '',
        workflowId: '',
        userId: 0,
        startTime: Date.now(),
        triggerType: 'manual',
        totalNodes: 0,
        completedNodes: 0,
        failedNodes: 0,
        status: 'running'
      }
    };

    this.logger.debug(`Initialized context manager`, {
      hasContext: !!context,
      executionId: this.context.metadata.executionId
    });

    return this;
  }

  /**
   * Add node với type safety
   */
  addNode<T extends Record<string, any>>(
    nodeName: string,
    nodeData: T,
    options?: JsonContextOptions
  ): this {
    if (!this.context) {
      throw new Error('Context manager not initialized. Call initialize() first.');
    }

    this.context.json = this.contextService.addNodeToJsonContext(
      this.context.json, 
      nodeName, 
      nodeData, 
      options
    );

    this.logger.debug(`Added node to context`, {
      nodeName,
      dataKeys: Object.keys(nodeData)
    });

    return this;
  }

  /**
   * Get value với type safety
   */
  getValue<T = any>(
    path: string,
    options?: JsonContextOptions
  ): T | undefined {
    if (!this.context) {
      throw new Error('Context manager not initialized. Call initialize() first.');
    }

    return this.contextService.getValueFromJsonContext(this.context.json, path, options) as T;
  }

  /**
   * Get multiple values
   */
  getMultipleValues(
    paths: string[],
    options?: JsonContextOptions
  ): Record<string, any> | GetValueResult {
    if (!this.context) {
      throw new Error('Context manager not initialized. Call initialize() first.');
    }

    return this.contextService.getMultipleValuesFromJsonContext(this.context.json, paths, options);
  }

  /**
   * Check if path exists
   */
  hasPath(path: string, options?: JsonContextOptions): boolean {
    if (!this.context) {
      throw new Error('Context manager not initialized. Call initialize() first.');
    }

    return this.contextService.hasPathInJsonContext(this.context.json, path, options);
  }

  /**
   * Get all paths
   */
  getAllPaths(options?: JsonContextOptions): string[] {
    if (!this.context) {
      throw new Error('Context manager not initialized. Call initialize() first.');
    }

    return this.contextService.getAllPathsFromJsonContext(this.context.json, options);
  }

  /**
   * Update metadata
   */
  updateMetadata(updates: Partial<WorkflowExecutionMetadata>): this {
    if (!this.context) {
      throw new Error('Context manager not initialized. Call initialize() first.');
    }

    this.context.metadata = { ...this.context.metadata, ...updates };

    this.logger.debug(`Updated metadata`, {
      updates: Object.keys(updates)
    });

    return this;
  }

  /**
   * Get full context
   */
  getContext(): WorkflowContext {
    if (!this.context) {
      throw new Error('Context manager not initialized. Call initialize() first.');
    }

    return JSON.parse(JSON.stringify(this.context));
  }

  /**
   * Get template context
   */
  getTemplateContext(): Record<string, any> {
    if (!this.context) {
      throw new Error('Context manager not initialized. Call initialize() first.');
    }

    return {
      json: this.context.json,
      metadata: this.context.metadata
    };
  }

  /**
   * Clear all data
   */
  clear(): this {
    if (!this.context) {
      throw new Error('Context manager not initialized. Call initialize() first.');
    }

    this.context.json = {};
    this.context.metadata.completedNodes = 0;
    this.context.metadata.failedNodes = 0;

    this.logger.debug(`Cleared context data`);

    return this;
  }

  /**
   * Clone manager
   */
  clone(): WorkflowContextManagerService {
    if (!this.context) {
      throw new Error('Context manager not initialized. Call initialize() first.');
    }

    const cloned = new WorkflowContextManagerService(this.contextService);
    cloned.initialize(this.getContext());
    return cloned;
  }

  /**
   * Export to JSON string
   */
  toJSON(): string {
    if (!this.context) {
      throw new Error('Context manager not initialized. Call initialize() first.');
    }

    return JSON.stringify(this.context, null, 2);
  }

  /**
   * Import from JSON string
   */
  fromJSON(jsonString: string): this {
    try {
      const parsedContext = JSON.parse(jsonString);
      this.initialize(parsedContext);

      this.logger.debug(`Imported context from JSON`, {
        executionId: this.context.metadata.executionId
      });

      return this;
    } catch (error) {
      throw new Error(`Invalid JSON: ${error.message}`);
    }
  }

  /**
   * Validate context structure
   */
  validateContext(): { isValid: boolean; errors: string[] } {
    if (!this.context) {
      return { isValid: false, errors: ['Context not initialized'] };
    }

    const errors: string[] = [];

    // Validate metadata
    if (!this.context.metadata) {
      errors.push('Missing metadata');
    } else {
      if (!this.context.metadata.executionId) {
        errors.push('Missing executionId in metadata');
      }
      if (!this.context.metadata.workflowId) {
        errors.push('Missing workflowId in metadata');
      }
      if (typeof this.context.metadata.userId !== 'number') {
        errors.push('Invalid userId in metadata');
      }
    }

    // Validate json structure
    if (!this.context.json || typeof this.context.json !== 'object') {
      errors.push('Invalid json structure');
    }

    const isValid = errors.length === 0;

    this.logger.debug(`Validated context`, {
      isValid,
      errorCount: errors.length
    });

    return { isValid, errors };
  }

  /**
   * Get context statistics
   */
  getStatistics(): {
    nodeCount: number;
    totalFields: number;
    completedNodes: number;
    failedNodes: number;
    executionDuration?: number;
  } {
    if (!this.context) {
      throw new Error('Context manager not initialized. Call initialize() first.');
    }

    const nodeCount = Object.keys(this.context.json).length;
    const totalFields = Object.values(this.context.json)
      .reduce((total, nodeData) => total + Object.keys(nodeData).length, 0);

    return {
      nodeCount,
      totalFields,
      completedNodes: this.context.metadata.completedNodes,
      failedNodes: this.context.metadata.failedNodes,
      executionDuration: this.context.metadata.executionDuration
    };
  }
}
