import { Injectable, Logger } from '@nestjs/common';
import { AgentConfig } from '../../../interfaces/agent-config.interface';
import { AuthContext } from '../../../interfaces';
import { AgentConfigBuilderService } from '../../../services';
import { WebsitePlatformStrategy } from './website-platform.strategy';

/**
 * Website Agent Config Builder Service (Facade)
 *
 * Provides a website-specific interface for building agent configurations
 * while delegating the actual work to the generic AgentConfigBuilderService.
 * Handles website-specific parameter mapping for business owners and visitors.
 */
@Injectable()
export class WebsiteAgentConfigBuilderService {
  private readonly logger = new Logger(WebsiteAgentConfigBuilderService.name);

  constructor(
    private readonly baseService: AgentConfigBuilderService,
    private readonly platformStrategy: WebsitePlatformStrategy,
  ) {}

  /**
   * Build agent configurations for website platform
   *
   * @param agentIds Array of agent IDs to build configurations for
   * @param promptBuilderMap Map of agent IDs to prompt builder functions
   * @param visitorId Optional visitor ID for tracking (not used for integrations)
   * @param websiteOwnerId Business owner's user ID (determines which API keys to use)
   * @param websiteData Website context and visitor information
   * @returns Map of agent IDs to complete AgentConfigInterface objects
   */
  async buildAgentConfigs(param: {
    agentIds: Array<{ id: string; prompt?: string }>;
    promptBuilderMap: Record<string, Array<() => string | Promise<string>>>;
    visitorId?: string;
    websiteOwnerId?: number;
    websiteData?: any;
  }): Promise<Record<string, AgentConfig>> {
    const {
      agentIds,
      promptBuilderMap,
      visitorId,
      websiteOwnerId,
      websiteData,
    } = param;

    this.logger.debug(
      `Building agent configs for ${agentIds.length} agents (website platform)`,
      {
        agentIds: agentIds.map((agent) => agent.id),
        visitorId,
        websiteOwnerId,
        websiteDataKeys: websiteData ? Object.keys(websiteData) : [],
      },
    );

    // Convert website-specific parameters to generic format
    const authContext: AuthContext = {
      visitorId,
      websiteOwnerId,
      websiteData,
    };

    // Delegate to the generic base service
    return this.baseService.buildAgentConfigs({
      agentIds,
      promptBuilderMap,
      platformStrategy: this.platformStrategy,
      authContext,
    });
  }
}
