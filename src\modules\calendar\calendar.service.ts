import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';

/**
 * Service xử lý các calendar operations trong worker
 */
@Injectable()
export class CalendarService {
  private readonly logger = new Logger(CalendarService.name);

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * G<PERSON><PERSON> nh<PERSON> nhở qua email
   */
  async sendReminder(data: {
    reminderId: string;
    eventId: string;
    userId: number;
    channelType: string;
    channelConfig: any;
    title: string;
    message: string;
    messageTemplate?: string;
    metadata?: any;
  }): Promise<void> {
    this.logger.log(`Sending reminder: ${data.reminderId} via ${data.channelType}`);

    try {
      // Gọi API backend để gửi notification
      const backendUrl = this.configService.get<string>('BACKEND_URL') || 'http://localhost:3000';
      const apiUrl = `${backendUrl}/api/v1/calendar/internal/send-notification`;

      const response = await firstValueFrom(
        this.httpService.post(apiUrl, {
          reminderId: data.reminderId,
          eventId: data.eventId,
          userId: data.userId,
          channelType: data.channelType,
          channelConfig: data.channelConfig,
          title: data.title,
          message: data.message,
          messageTemplate: data.messageTemplate,
          metadata: data.metadata,
        }, {
          headers: {
            'Content-Type': 'application/json',
            'X-Internal-Request': 'true', // Đánh dấu là internal request
          },
          timeout: 30000, // 30 seconds timeout
        })
      );

      this.logger.log(`Reminder sent successfully: ${data.reminderId} - Status: ${response.status}`);
    } catch (error) {
      this.logger.error(
        `Failed to send reminder: ${data.reminderId} - Error: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Thực thi nhiệm vụ
   */
  async executeTask(data: {
    taskId: string;
    eventId: string;
    userId: number;
    agentId: string;
    workflowTaskId: string;
    taskName: string;
    taskDescription: string;
    resources?: any;
    executionConfig?: any;
    executionParams?: any;
    metadata?: any;
  }): Promise<void> {
    this.logger.log(`Executing task: ${data.taskId} - Agent: ${data.agentId}`);

    try {
      // Gọi API backend để thực thi task
      const backendUrl = this.configService.get<string>('BACKEND_URL') || 'http://localhost:3000';
      const apiUrl = `${backendUrl}/api/v1/calendar/internal/execute-task`;

      const response = await firstValueFrom(
        this.httpService.post(apiUrl, data, {
          headers: {
            'Content-Type': 'application/json',
            'X-Internal-Request': 'true',
          },
          timeout: 60000, // 60 seconds timeout for task execution
        })
      );

      this.logger.log(`Task executed successfully: ${data.taskId} - Status: ${response.status}`);
    } catch (error) {
      this.logger.error(
        `Failed to execute task: ${data.taskId} - Error: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Tạo báo cáo
   */
  async generateReport(data: {
    reportId: string;
    eventId: string;
    userId: number;
    reportType: string;
    reportConfig: any;
    metadata?: any;
  }): Promise<void> {
    this.logger.log(`Generating report: ${data.reportId} - Type: ${data.reportType}`);

    try {
      const backendUrl = this.configService.get<string>('BACKEND_URL') || 'http://localhost:3000';
      const apiUrl = `${backendUrl}/api/v1/calendar/internal/generate-report`;

      const response = await firstValueFrom(
        this.httpService.post(apiUrl, data, {
          headers: {
            'Content-Type': 'application/json',
            'X-Internal-Request': 'true',
          },
          timeout: 120000, // 2 minutes timeout for report generation
        })
      );

      this.logger.log(`Report generated successfully: ${data.reportId} - Status: ${response.status}`);
    } catch (error) {
      this.logger.error(
        `Failed to generate report: ${data.reportId} - Error: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Tạo recurrence instances
   */
  async createRecurrenceInstances(data: {
    recurrenceId: string;
    maxInstances?: number;
  }): Promise<void> {
    this.logger.log(`Creating recurrence instances: ${data.recurrenceId}`);

    try {
      const backendUrl = this.configService.get<string>('BACKEND_URL') || 'http://localhost:3000';
      const apiUrl = `${backendUrl}/api/v1/calendar/internal/create-recurrence-instances`;

      const response = await firstValueFrom(
        this.httpService.post(apiUrl, data, {
          headers: {
            'Content-Type': 'application/json',
            'X-Internal-Request': 'true',
          },
          timeout: 60000,
        })
      );

      this.logger.log(`Recurrence instances created successfully: ${data.recurrenceId} - Status: ${response.status}`);
    } catch (error) {
      this.logger.error(
        `Failed to create recurrence instances: ${data.recurrenceId} - Error: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Đồng bộ Google Calendar
   */
  async syncGoogleCalendar(data: {
    eventId: string;
    userId: number;
    accessToken: string;
  }): Promise<void> {
    this.logger.log(`Syncing Google Calendar: ${data.eventId}`);

    try {
      const backendUrl = this.configService.get<string>('BACKEND_URL') || 'http://localhost:3000';
      const apiUrl = `${backendUrl}/api/v1/calendar/internal/sync-google-calendar`;

      const response = await firstValueFrom(
        this.httpService.post(apiUrl, data, {
          headers: {
            'Content-Type': 'application/json',
            'X-Internal-Request': 'true',
          },
          timeout: 30000,
        })
      );

      this.logger.log(`Google Calendar synced successfully: ${data.eventId} - Status: ${response.status}`);
    } catch (error) {
      this.logger.error(
        `Failed to sync Google Calendar: ${data.eventId} - Error: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Tạo Zoom meeting
   */
  async createZoomMeeting(data: {
    eventId: string;
    userId: number;
    zoomConfig: any;
  }): Promise<void> {
    this.logger.log(`Creating Zoom meeting: ${data.eventId}`);

    try {
      const backendUrl = this.configService.get<string>('BACKEND_URL') || 'http://localhost:3000';
      const apiUrl = `${backendUrl}/api/v1/calendar/internal/create-zoom-meeting`;

      const response = await firstValueFrom(
        this.httpService.post(apiUrl, data, {
          headers: {
            'Content-Type': 'application/json',
            'X-Internal-Request': 'true',
          },
          timeout: 30000,
        })
      );

      this.logger.log(`Zoom meeting created successfully: ${data.eventId} - Status: ${response.status}`);
    } catch (error) {
      this.logger.error(
        `Failed to create Zoom meeting: ${data.eventId} - Error: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
