import { Repository } from 'typeorm';
import { UserConvertCustomerMemory } from '../domains/external/entities/user-convert-customer-memory.entity';
import {
  UserConvertCustomerInfo,
  UserInfo,
} from '../shared/interfaces';

/**
 * Zalo User Context Interface
 * Represents Zalo-specific context information
 */
export interface ZaloContextInfo {
  oaId: string;
  oaName: string;
  encryptedConfig: string;
  secretKey: string;
  zaloUserDetail?: any;
}

/**
 * Zalo User Context Only Prompt Builder (without memory)
 * Generates XML representation of Zalo user context for personalized agent responses
 * Uses attribute-heavy approach for minimal token usage
 */
export function buildZaloUserContextOnlyPrompt(
  zaloUser?: UserConvertCustomerInfo,
  zaloOwner?: UserInfo,
  zaloInfo?: ZaloContextInfo,
): string {
  if (!zaloUser) {
    return ''; // No Zalo user info available
  }

  const parts: string[] = ['<zalo-context>'];

  // Zalo user identity section
  const identityAttrs: string[] = [`id="${zaloUser.id}"`];
  if (zaloUser.name) identityAttrs.push(`name="${zaloUser.name}"`);
  if (zaloUser.email) identityAttrs.push(`email="${zaloUser.email.join(', ')}"`);
  if (zaloUser.phone) identityAttrs.push(`phone="${zaloUser.phone}"`);
  if (zaloUser.countryCode) identityAttrs.push(`country-code="${zaloUser.countryCode}"`);
  if (zaloUser.address) identityAttrs.push(`address="${zaloUser.address}"`);

  if (identityAttrs.length > 0) {
    parts.push(`  <zalo-user ${identityAttrs.join(' ')} />`);
  }

  // Zalo user tags (interests/categories)
  if (zaloUser.tags && zaloUser.tags.length > 0) {
    const tagString = zaloUser.tags.join(',');
    parts.push(`  <interests tags="${tagString}" />`);
  }

  // Zalo user metadata (custom fields)
  if (zaloUser.metadata && Object.keys(zaloUser.metadata).length > 0) {
    const metadataEntries = Object.entries(zaloUser.metadata)
      .filter(([key, value]) => value !== null && value !== undefined)
      .map(([key, value]) => {
        const escapedValue = String(value).replace(/"/g, '&quot;');
        return `${key}="${escapedValue}"`;
      })
      .join(' ');

    if (metadataEntries) {
      parts.push(`  <zalo-user-metadata ${metadataEntries} />`);
    }
  }

  // Zalo business owner context (OA owner information)
  if (zaloOwner) {
    const ownerAttrs: string[] = [`owner-id="${zaloOwner.userId}"`];
    if (zaloOwner.fullName) ownerAttrs.push(`owner-name="${zaloOwner.fullName}"`);
    if (zaloOwner.email) ownerAttrs.push(`contact-email="${zaloOwner.email}"`);
    if (zaloOwner.timezone) ownerAttrs.push(`timezone="${zaloOwner.timezone}"`);
    if (zaloOwner.currency) ownerAttrs.push(`currency="${zaloOwner.currency}"`);
    if (zaloOwner.type) ownerAttrs.push(`business-type="${zaloOwner.type}"`);

    parts.push(`  <business ${ownerAttrs.join(' ')} />`);
  }

  // Zalo Official Account context
  if (zaloInfo) {
    const oaAttrs: string[] = [`oa-id="${zaloInfo.oaId}"`];
    if (zaloInfo.oaName) oaAttrs.push(`oa-name="${zaloInfo.oaName}"`);

    parts.push(`  <zalo-oa ${oaAttrs.join(' ')} />`);

    // Zalo user detail from API (if available)
    if (zaloInfo.zaloUserDetail) {
      const userDetail = zaloInfo.zaloUserDetail;
      const detailAttrs: string[] = [];
      
      if (userDetail.user_id) detailAttrs.push(`zalo-id="${userDetail.user_id}"`);
      if (userDetail.display_name) detailAttrs.push(`display-name="${userDetail.display_name}"`);
      if (userDetail.user_alias) detailAttrs.push(`alias="${userDetail.user_alias}"`);
      if (userDetail.user_gender !== undefined) detailAttrs.push(`gender="${userDetail.user_gender}"`);
      if (userDetail.user_is_follower !== undefined) detailAttrs.push(`is-follower="${userDetail.user_is_follower}"`);

      if (detailAttrs.length > 0) {
        parts.push(`  <zalo-profile ${detailAttrs.join(' ')} />`);
      }
    }
  }

  parts.push('</zalo-context>');
  return parts.join('\n');
}

/**
 * Zalo User Memory Prompt Builder with Repository Access
 * Follows website pattern of directly fetching memory data
 * Generates comprehensive Zalo user context including conversation history
 */
export const buildZaloUserContextPrompt = async (
  zaloUserId: string,
  userConvertCustomerMemoryRepository: Repository<UserConvertCustomerMemory>,
  zaloUser?: UserConvertCustomerInfo,
  zaloOwner?: UserInfo,
  zaloInfo?: ZaloContextInfo,
): Promise<string> => {
  if (!zaloUserId) {
    return ''; // No Zalo user ID, no context
  }

  const parts: string[] = [];

  // Add Zalo user context (without memory)
  const contextPrompt = buildZaloUserContextOnlyPrompt(
    zaloUser,
    zaloOwner,
    zaloInfo,
  );
  if (contextPrompt) {
    parts.push(contextPrompt);
  }

  // Fetch and add Zalo user memories
  try {
    const memories = await userConvertCustomerMemoryRepository
      .createQueryBuilder('memory')
      .where('memory.userConvertCustomerId = :zaloUserId', { zaloUserId })
      .orderBy('memory.createdAt', 'DESC')
      .limit(10) // Limit to recent memories
      .getMany();

    if (memories.length > 0) {
      const memoryParts: string[] = ['<zalo-user-memories>'];

      memories.forEach((memory) => {
        const attributes: string[] = [`id="${memory.id}"`];

        // Add created date if available (convert timestamp to date)
        if (memory.createdAt && typeof memory.createdAt === 'string') {
          try {
            const timestamp = parseInt(memory.createdAt, 10);
            if (!isNaN(timestamp) && timestamp > 0) {
              const date = new Date(timestamp);
              if (!isNaN(date.getTime())) {
                const dateString = date.toISOString().split('T')[0];
                attributes.push(`created="${dateString}"`);
              }
            }
          } catch (error) {
            // Skip invalid timestamp, continue without date
            console.warn(
              `Invalid timestamp for Zalo user memory ${memory.id}: ${memory.createdAt}`,
            );
          }
        }

        memoryParts.push(`  <memory ${attributes.join(' ')}>`);

        // Content field - main memory content
        if (memory.content) {
          // Escape XML characters in content
          const escapedContent = memory.content
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;');

          memoryParts.push(`    <content>${escapedContent}</content>`);
        }

        // Metadata (Zalo user preferences, tags, categories)
        if (memory.metadata && Object.keys(memory.metadata).length > 0) {
          const metadataEntries = Object.entries(memory.metadata)
            .filter(([key, value]) => value !== null && value !== undefined)
            .map(([key, value]) => {
              // Handle different data types appropriately
              let stringValue: string;
              if (typeof value === 'object') {
                stringValue = JSON.stringify(value);
              } else {
                stringValue = String(value);
              }

              // Escape XML characters
              const escapedValue = stringValue
                .replace(/&/g, '&amp;')
                .replace(/"/g, '&quot;');

              return `${key}="${escapedValue}"`;
            })
            .join(' ');

          if (metadataEntries) {
            memoryParts.push(`    <metadata ${metadataEntries} />`);
          }
        }

        memoryParts.push('  </memory>');
      });

      memoryParts.push('</zalo-user-memories>');
      parts.push(memoryParts.join('\n'));
    }
  } catch (error) {
    console.error('Error fetching Zalo user memories:', error);
    // Continue without memories rather than breaking the prompt
  }

  return parts.join('\n\n');
};