import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { Injectable, Logger } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { UserAudience } from '../modules/marketing/entities/user-audience.entity';
import { ZaloPersonalGroup } from '../modules/marketing/entities/zalo-personal-group.entity';
import { AutomationWebService } from '../shared/services/automation-web.service';

/**
 * Interface cho job crawl friends từ Zalo Personal
 */
interface ZaloPersonalCrawlFriendsJobData {
  integrationId: string;
  userId: number;
  zaloUid: string;
  headless?: boolean;
  timestamp: number;
  trackingId?: string;
}

/**
 * Interface cho job crawl groups từ Zalo Personal
 */
interface ZaloPersonalCrawlGroupsJobData {
  integrationId: string;
  userId: number;
  zaloUid: string;
  headless?: boolean;
  timestamp: number;
  trackingId?: string;
}

/**
 * Interface cho job send friend request batch từ Zalo Personal
 */
interface ZaloPersonalSendFriendRequestJobData {
  integrationId: string;
  userId: number;
  zaloUid: string;
  phoneNumbers: string[];
  delayBetweenRequests?: number;
  headless?: boolean;
  timestamp: number;
  trackingId?: string;
}

/**
 * Interface cho job send all từ Zalo Personal
 */
interface ZaloPersonalSendAllJobData {
  integrationId: string;
  userId: number;
  zaloUid: string;
  phoneNumbers: string[];
  messageContent: string;
  delayBetweenRequests?: number;
  delayBetweenMessages?: number;
  sendFriendRequest?: boolean;
  headless?: boolean;
  timestamp: number;
  trackingId?: string;
}

/**
 * Interface cho response crawl friends từ Python API
 */
interface ZaloCrawlFriendsResponse {
  success: boolean;
  message?: string;
  zalo_uid?: string;
  friends?: Array<{
    name: string;
    avatar_url: string;
  }>;
  total_count?: number;
  crawl_timestamp?: string;
  error?: string;
}

/**
 * Interface cho response crawl groups từ Python API
 */
interface ZaloCrawlGroupsResponse {
  success: boolean;
  message?: string;
  zalo_uid?: string;
  groups?: Array<{
    name: string;
    avatar_url: string;
    tag?: string;
  }>;
  total_count?: number;
  crawl_timestamp?: string;
  error?: string;
}

/**
 * Interface cho response send all từ Python API
 */
interface ZaloSendAllResponse {
  success: boolean;
  total_numbers?: number;
  results?: Array<{
    phone: string;
    friend_request_status: string;
    message_status: string;
    error?: string;
  }>;
  summary?: {
    total_processed: number;
    friend_requests_sent: number;
    messages_sent: number;
    errors: number;
  };
  processing_time?: number;
  error?: string;
}

/**
 * Interface cho response message batch từ Python API
 */
interface ZaloMessageBatchResponse {
  success: boolean;
  total_numbers?: number;
  results?: Array<{
    phone: string;
    status: string;
    error?: string;
  }>;
  summary?: {
    total_processed: number;
    messages_sent: number;
    errors: number;
  };
  processing_time?: number;
  error?: string;
}

/**
 * Processor xử lý các job trong queue Zalo Personal
 */
@Injectable()
@Processor('zalo-personal')
export class ZaloPersonalProcessor extends WorkerHost {
  private readonly logger = new Logger(ZaloPersonalProcessor.name);

  constructor(
    private readonly automationWebService: AutomationWebService,
    private readonly dataSource: DataSource,
  ) {
    super();
  }

  /**
   * Main process method required by WorkerHost
   */
  async process(job: Job): Promise<any> {
    console.log('Processing job: ', job);
    switch (job.name) {
      case 'crawl-friends':
        return this.processCrawlFriends(
          job as Job<ZaloPersonalCrawlFriendsJobData>,
        );
      case 'crawl-groups':
        return this.processCrawlGroups(
          job as Job<ZaloPersonalCrawlGroupsJobData>,
        );
      case 'send-friend-request-batch':
        return this.processSendFriendRequestBatch(
          job as Job<ZaloPersonalSendFriendRequestJobData>,
        );
      case 'send-message-batch':
        return this.processSendMessageBatch(job);
      case 'send-all':
        return this.processSendAll(job as Job<ZaloPersonalSendAllJobData>);
      default:
        throw new Error(`Unknown job name: ${job.name}`);
    }
  }

  /**
   * Xử lý job crawl friends từ Zalo Personal
   */
  async processCrawlFriends(
    job: Job<ZaloPersonalCrawlFriendsJobData>,
  ): Promise<void> {
    const { data } = job;
    const { integrationId, userId, zaloUid, headless, trackingId } = data;

    this.logger.log(
      `Processing crawl friends job ${job.id} - Integration: ${integrationId} - User: ${userId} - ZaloUID: ${zaloUid}`,
    );

    try {
      // Cập nhật progress
      await job.updateProgress(10);

      // Gọi API crawl friends từ Python
      this.logger.log(`Calling crawlFriends for ZaloUID: ${zaloUid}`);

      const crawlResult: ZaloCrawlFriendsResponse =
        await this.automationWebService.crawlZaloFriends(
          zaloUid,
          headless ?? false,
        );

      await job.updateProgress(50);

      // Kiểm tra kết quả crawl
      if (!crawlResult.success) {
        throw new Error(
          `Crawl friends failed: ${crawlResult.error || crawlResult.message}`,
        );
      }

      if (!crawlResult.friends || crawlResult.friends.length === 0) {
        this.logger.warn(`No friends found for ZaloUID: ${zaloUid}`);
        await job.updateProgress(100);
        return;
      }

      this.logger.log(
        `Successfully crawled ${crawlResult.friends.length} friends for ZaloUID: ${zaloUid}`,
      );

      await job.updateProgress(70);

      // Tạo individual user_audience records cho từng friend
      const now = Date.now();

      await job.updateProgress(80);

      // Sử dụng transaction để đảm bảo tính nhất quán
      await this.dataSource.transaction(async (manager) => {
        // Tạo individual audience records cho từng friend
        if (crawlResult.friends && crawlResult.friends.length > 0) {
          const friendAudiences: UserAudience[] = [];

          for (let index = 0; index < crawlResult.friends.length; index++) {
            const friend = crawlResult.friends[index];

            // Tạo audience record cho friend
            const friendAudience = new UserAudience();
            friendAudience.userId = userId;
            friendAudience.name = friend.name;
            friendAudience.email = '';
            friendAudience.countryCode = null;
            friendAudience.phoneNumber = null;
            friendAudience.avatar = friend.avatar_url;
            friendAudience.zaloSocialId = null;
            friendAudience.integrationId = integrationId; // Thêm integrationId để tracking
            friendAudience.avatarsExternal = friend.avatar_url
              ? [friend.avatar_url]
              : null;
            friendAudience.importResource = 'ZALO_PERSONAL' as any;
            friendAudience.zaloOfficialAccountId = null;
            friendAudience.zaloUserIsFollower = null;
            friendAudience.userLastInteractionDate = null;
            friendAudience.createdAt = now;
            friendAudience.updatedAt = now;

            friendAudiences.push(friendAudience);
          }

          // Batch save friend audiences
          await manager.save(UserAudience, friendAudiences);
        }

        this.logger.log(
          `Successfully created ${crawlResult.friends?.length || 0} individual audience records for user ${userId} from integration ${integrationId}`,
        );
      });

      await job.updateProgress(100);

      this.logger.log(
        `Completed crawl friends job ${job.id} - Created ${crawlResult.friends.length} individual audience records`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to process crawl friends job ${job.id}: ${error.message}`,
        error.stack,
      );

      // Log chi tiết lỗi để debug
      this.logger.error('Job data:', data);
      this.logger.error('Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack,
      });

      throw error;
    }
  }

  /**
   * Xử lý job crawl groups từ Zalo Personal
   */
  async processCrawlGroups(
    job: Job<ZaloPersonalCrawlGroupsJobData>,
  ): Promise<void> {
    const { data } = job;
    const { integrationId, userId, zaloUid, headless, trackingId } = data;

    this.logger.log(
      `Processing crawl groups job ${job.id} - Integration: ${integrationId} - User: ${userId} - ZaloUID: ${zaloUid}`,
    );

    try {
      // Cập nhật progress
      await job.updateProgress(10);

      // Gọi API crawl groups từ Python
      this.logger.log(`Calling crawlGroups for ZaloUID: ${zaloUid}`);

      const crawlResult: ZaloCrawlGroupsResponse =
        await this.automationWebService.crawlZaloGroups(
          zaloUid,
          headless ?? false,
        );

      await job.updateProgress(50);

      // Kiểm tra kết quả crawl
      if (!crawlResult.success) {
        throw new Error(
          `Crawl groups failed: ${crawlResult.error || crawlResult.message}`,
        );
      }

      if (!crawlResult.groups || crawlResult.groups.length === 0) {
        this.logger.warn(`No groups found for ZaloUID: ${zaloUid}`);
        await job.updateProgress(100);
        return;
      }

      this.logger.log(
        `Successfully crawled ${crawlResult.groups.length} groups for ZaloUID: ${zaloUid}`,
      );

      await job.updateProgress(70);

      // Lưu groups vào database
      const now = Date.now();
      const crawlTimestamp = crawlResult.crawl_timestamp
        ? new Date(crawlResult.crawl_timestamp).getTime()
        : now;

      await job.updateProgress(80);

      // Sử dụng transaction để đảm bảo tính nhất quán
      await this.dataSource.transaction(async (manager) => {
        // Tạo records cho từng group
        if (crawlResult.groups && crawlResult.groups.length > 0) {
          const groupRecords: ZaloPersonalGroup[] = [];

          for (let index = 0; index < crawlResult.groups.length; index++) {
            const group = crawlResult.groups[index];

            // Tạo record cho group
            const groupRecord = new ZaloPersonalGroup();
            groupRecord.userId = userId;
            groupRecord.integrationId = integrationId;
            groupRecord.name = group.name;
            groupRecord.avatarUrl = group.avatar_url;
            groupRecord.tag = group.tag;
            groupRecord.crawlTimestamp = crawlTimestamp;
            groupRecord.createdAt = now;
            groupRecord.updatedAt = now;
            groupRecord.metadata = {
              trackingId,
              crawlJobId: job.id,
              originalIndex: index,
            };

            groupRecords.push(groupRecord);
          }

          // Batch insert groups
          if (groupRecords.length > 0) {
            await manager.save(ZaloPersonalGroup, groupRecords);
            this.logger.log(
              `Saved ${groupRecords.length} groups to database for user ${userId}`,
            );
          }
        }
      });

      await job.updateProgress(100);

      this.logger.log(
        `Successfully completed crawl groups job ${job.id} for user ${userId}`,
      );
    } catch (error) {
      this.logger.error(
        `Error processing crawl groups job ${job.id}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Xử lý job send friend request batch từ Zalo Personal
   */
  async processSendFriendRequestBatch(
    job: Job<ZaloPersonalSendFriendRequestJobData>,
  ): Promise<void> {
    const { data } = job;
    const {
      integrationId,
      userId,
      zaloUid,
      phoneNumbers,
      delayBetweenRequests,
      headless,
    } = data;

    this.logger.log(
      `Processing send friend request batch job ${job.id} - Integration: ${integrationId} - User: ${userId} - ZaloUID: ${zaloUid} - Phones: ${phoneNumbers.length}`,
    );

    try {
      // Cập nhật progress
      await job.updateProgress(10);

      // Gọi API send friend request batch từ AutomationWebService
      this.logger.log(
        `Calling sendFriendRequestBatch for ZaloUID: ${zaloUid} with ${phoneNumbers.length} phone numbers`,
      );

      const result = await this.automationWebService.sendFriendRequestBatch(
        zaloUid,
        phoneNumbers,
        delayBetweenRequests || 3,
        headless ?? true,
      );

      await job.updateProgress(90);

      // Log kết quả
      this.logger.log(
        `Successfully processed send friend request batch job ${job.id}`,
        { result },
      );

      await job.updateProgress(100);
    } catch (error) {
      this.logger.error(
        `Failed to process send friend request batch job ${job.id}: ${error.message}`,
      );
      this.logger.error('Job data:', data);
      this.logger.error('Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  /**
   * Xử lý job send message batch từ Zalo Personal (TODO)
   */
  async processSendMessageBatch(job: Job): Promise<void> {
    this.logger.log(
      `Processing send message batch job ${job.id} - TODO: Implement`,
    );
    throw new Error('Send message batch job processor chưa được implement');
  }

  /**
   * Xử lý job send all từ Zalo Personal
   */
  async processSendAll(job: Job<ZaloPersonalSendAllJobData>): Promise<void> {
    const { data } = job;
    const {
      integrationId,
      userId,
      zaloUid,
      phoneNumbers,
      messageContent,
      delayBetweenRequests,
      delayBetweenMessages,
      sendFriendRequest,
      headless,
      trackingId,
    } = data;

    console.log('Data: ', data);

    this.logger.log(
      `Processing send all job ${job.id} - Integration: ${integrationId} - User: ${userId} - ZaloUID: ${zaloUid} - Phones: ${phoneNumbers.length} - SendFriendRequest: ${sendFriendRequest} (type: ${typeof sendFriendRequest})`,
    );

    try {
      // Cập nhật progress
      await job.updateProgress(10);

      let sendResult: any;

      // Kiểm tra chính xác giá trị sendFriendRequest
      const shouldSendFriendRequest =
        sendFriendRequest === true || sendFriendRequest === undefined;

      this.logger.log(
        `SendFriendRequest decision: ${shouldSendFriendRequest} (original value: ${sendFriendRequest})`,
      );

      if (shouldSendFriendRequest) {
        // Gửi kết bạn + tin nhắn (mặc định)
        this.logger.log(
          `Calling sendAllFriendRequestAndMessage for ZaloUID: ${zaloUid}`,
        );

        sendResult =
          await this.automationWebService.sendAllFriendRequestAndMessage(
            zaloUid,
            phoneNumbers,
            messageContent,
            delayBetweenRequests ?? 3,
            delayBetweenMessages ?? 2,
            headless ?? false,
          );
      } else {
        // Chỉ gửi tin nhắn
        this.logger.log(`Calling sendMessageBatch for ZaloUID: ${zaloUid}`);

        sendResult = await this.automationWebService.sendMessageBatch(
          zaloUid,
          phoneNumbers,
          messageContent,
          delayBetweenRequests ?? 3,
          delayBetweenMessages ?? 2,
          headless ?? false,
          `SendAll_MessageOnly_${trackingId || Date.now()}`,
        );
      }

      await job.updateProgress(80);

      // Kiểm tra kết quả
      if (!sendResult.success) {
        throw new Error(
          `Send operation failed: ${sendResult.error || 'Unknown error'}`,
        );
      }

      this.logger.log(
        `Successfully completed send operation for ZaloUID: ${zaloUid}`,
        {
          sendFriendRequest: sendFriendRequest !== false,
          totalNumbers: sendResult.total_numbers,
          summary: sendResult.summary,
          processingTime: sendResult.processing_time,
        },
      );

      await job.updateProgress(100);

      this.logger.log(
        `Successfully completed send all job ${job.id} for user ${userId}`,
      );
    } catch (error) {
      this.logger.error(
        `Error processing send all job ${job.id}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
