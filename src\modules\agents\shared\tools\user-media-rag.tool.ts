import { Injectable, Logger } from '@nestjs/common';
import { StructuredTool, ToolRunnableConfig } from '@langchain/core/tools';
import { CallbackManagerForToolRun } from '@langchain/core/callbacks/manager';
import { z } from 'zod';
import { RagEngineService } from 'src/infra';
import { InjectRepository } from '@nestjs/typeorm';
import { In, IsNull, Not, Repository } from 'typeorm';
import { InAppSupervisorWorkersConfig } from '../../platforms/in-app/graph-configs/in-app-supervisor-workers.config';
import { AssistantSpendingType } from '../enums';
import { UserBillingService } from '../services';
import { MediaData, MediaStatus } from '../entities/data';

@Injectable()
export class UserMediaRAGTool extends StructuredTool {
  name: string;
  description: string;
  schema: z.ZodSchema;
  private readonly rPointEmbeddingRate = 2;
  private readonly logger = new Logger(UserMediaRAGTool.name);

  constructor(
    private readonly ragEngineService: RagEngineService,
    @InjectRepository(MediaData)
    private readonly mediaDataRepository: Repository<MediaData>,
    private readonly userBilling: UserBillingService,
  ) {
    super();
    this.logger.log('Initializing UserMediaRAGTool');
    this.name = 'search_image_content';
    this.description =
      'Search through uploaded image content to find relevant information based on queries. ' +
      'Use this tool when the user has uploaded images and you need to find specific information from them. ' +
      "Generate appropriate search queries based on the user's question and the context of their images.";

    this.schema = z.object({
      query: z
        .string()
        .describe(
          'The search query to find relevant information in the image content',
        ),
      media_ids: z
        .array(z.string())
        .optional()
        .describe('Optional array of specific image IDs to search within'),
      media_type: z
        .array(z.enum(['image']))
        .optional()
        .describe('The type of media to search within'),
      max_results: z
        .number()
        .min(1)
        .max(100)
        .optional()
        .describe('Optional maximum number of results to return'),
    });
  }

  protected async _call(
    arg: {
      query: string;
      media_ids?: string[];
      max_results?: number;
      media_type?: ('image' | 'video' | 'audio')[];
    },
    runManager?: CallbackManagerForToolRun,
    parentConfig?: ToolRunnableConfig<InAppSupervisorWorkersConfig>,
  ): Promise<string> {
    try {
      const userId = parentConfig?.configurable?.currentUser?.user?.userId;
      if (!userId) {
        return 'internal error: User ID is required to search media content. Stop using this tool.';
      }
      const mediaIds = arg.media_ids
        ? arg.media_ids
        : await this.getAllMediaIdsByUserId(userId);
      // Call the RAG engine service to search media with default settings (hardcoded to image type)
      const searchResult = await this.ragEngineService.searchMedia({
        query: arg.query,
        media_ids: mediaIds,
        limit: arg.max_results || 10, // Default to 10 if not provided
        media_type: arg.media_type || ['image'], // Default to 'image' if not provided
      });
      const usageToken = searchResult.token_usage?.embedding_tokens || 0;
      this.logger.debug(
        `User ${userId} used ${usageToken} points for image search query "${arg.query}"`,
      );
      const usageRPoint = usageToken * this.rPointEmbeddingRate;
      this.logger.debug(
        `User ${userId} will be charged ${usageRPoint} R-Points for image search query "${arg.query}"`,
      );
      await this.userBilling.updateUserPointBalance(
        userId,
        usageRPoint,
        AssistantSpendingType.IN_APP,
      );
      await this.userBilling.createSpendingRecords(
        [
          {
            agentId: parentConfig?.configurable?.executorAgent?.id as string,
            model: 'jina-embedding-v4',
            inputTokens: usageToken,
            outputTokens: 0,
            totalTokens: usageToken,
            pointCost: usageRPoint,
          },
        ],
        userId,
        AssistantSpendingType.IN_APP,
        parentConfig?.configurable?.run_id || '',
      );

      // Format the results for the LLM
      if (!searchResult.success || searchResult.results.length === 0) {
        return `No relevant image content found for query: "${arg.query}"`;
      }

      const formattedResults = searchResult.results
        .map((result, index) => {
          let resultText = `--- Image Result ${index + 1} ---\n`;
          resultText += `Similarity Score: ${result.similarity.toFixed(3)}\n`;

          if (result.rerank_score) {
            resultText += `Rerank Score: ${result.rerank_score.toFixed(3)}\n`;
          }

          if (result.name) {
            resultText += `Image Name: ${result.name}\n`;
          }

          if (result.description) {
            resultText += `Description: ${result.description}\n`;
          }

          if (result.tags && result.tags.length > 0) {
            resultText += `Tags: ${result.tags.join(', ')}\n`;
          }

          resultText += `Content: ${result.content}\n`;

          return resultText;
        })
        .join('\n');

      const summary = `Found ${searchResult.total_found} relevant image results for "${arg.query}" (showing top ${searchResult.results.length}):\n\n${formattedResults}`;

      // Log search metadata for debugging
      if (runManager) {
        runManager.handleText(
          `Image search executed in ${searchResult.execution_time_ms}ms with threshold ${searchResult.threshold_used}`,
        );
      }

      return summary;
    } catch (error) {
      const errorMessage = `Error searching image content: ${error.message}`;
      if (runManager) {
        runManager.handleText(errorMessage);
      }
      throw new Error(errorMessage);
    }
  }

  async getAllMediaIdsByUserId(userId: number): Promise<string[]> {
    try {
      this.logger.debug(`Lấy tất cả media IDs cho user ${userId}`);

      const result = await this.mediaDataRepository.findBy({
        ownedBy: userId,
        status: Not(In([MediaStatus.DELETED, MediaStatus.REJECTED])),
        mediaId: Not(IsNull()),
      });

      const mediaIds = result.map((item) => item.mediaId).filter((id) => id);
      this.logger.debug(
        `Tìm thấy ${mediaIds.length} media IDs cho user ${userId}`,
      );

      return mediaIds;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy media IDs cho user ${userId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
