import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { QueueName, ZaloArticleTrackingJobName } from '../../../queue/queue-name.enum';
import { ZaloArticleTrackingJobData } from '../../../queue/queue.types';
import { ZaloArticleService } from '../../../shared/services/zalo/zalo-article.service';
import { ZaloTokenUtilsService } from '../../../shared/services/zalo/zalo-token-utils.service';

/**
 * Service xử lý tracking bài viết Zalo
 */
@Injectable()
export class ZaloArticleTrackingService {
  private readonly logger = new Logger(ZaloArticleTrackingService.name);

  constructor(
    @InjectQueue(QueueName.ZALO_ARTICLE_TRACKING)
    private readonly zaloArticleTrackingQueue: Queue,
    private readonly zaloArticleService: ZaloArticleService,
    private readonly zaloTokenUtilsService: ZaloTokenUtilsService,
  ) {}

  /**
   * Kiểm tra trạng thái bài viết và cập nhật database
   * @param token Token của bài viết
   * @param accessToken Access token của OA
   * @param userId ID của user
   * @param integrationId ID của integration
   * @param localArticleId ID của bài viết trong database local (optional)
   * @returns Kết quả kiểm tra
   */
  async checkAndUpdateArticleStatus(
    token: string,
    accessToken: string,
    userId: number,
    integrationId: string,
    localArticleId?: string,
  ): Promise<{
    status: 'processing' | 'success' | 'failed';
    article_id?: string;
    error_message?: string;
  }> {
    try {
      this.logger.debug(`Checking article status for token: ${token.substring(0, 20)}...`);

      // Kiểm tra access token còn hợp lệ không
      const validAccessToken = await this.zaloTokenUtilsService.getValidAccessToken(
        integrationId,
      );

      if (!validAccessToken) {
        this.logger.warn(`Invalid access token for integration: ${integrationId}`);
        return {
          status: 'failed',
          error_message: 'Access token không hợp lệ',
        };
      }

      // Gọi Zalo API để verify article và lấy ID
      const zaloResult = await this.zaloArticleService.verifyArticle(
        validAccessToken,
        token,
      );

      this.logger.debug('Zalo API result:', JSON.stringify(zaloResult, null, 2));

      // Nếu có article_id, cập nhật vào database local (nếu có localArticleId)
      if (zaloResult?.id && localArticleId) {
        try {
          // TODO: Cập nhật article_id vào database local
          // await this.updateLocalArticleId(localArticleId, zaloResult.id);
          this.logger.log(`Article tracking completed - Local ID: ${localArticleId}, Zalo ID: ${zaloResult.id}`);
        } catch (error) {
          // Log lỗi nhưng không throw để không ảnh hưởng đến response chính
          this.logger.warn(`Failed to update local article ID: ${error.message}`);
        }
      }

      return {
        status: zaloResult?.id ? 'success' : 'processing',
        article_id: zaloResult?.id,
        error_message: undefined,
      };

    } catch (error) {
      this.logger.error(
        `Error checking article status for token ${token.substring(0, 20)}...: ${error.message}`,
        error.stack,
      );

      // Nếu là lỗi từ Zalo API về token expired, có thể bài viết đã hoàn thành
      if (error.message && error.message.includes('token was expired')) {
        return {
          status: 'success',
          error_message: 'Token đã hết hạn - bài viết có thể đã được xử lý xong',
        };
      }

      // Nếu là lỗi khác, có thể bài viết vẫn đang processing
      return {
        status: 'processing',
        error_message: error.message,
      };
    }
  }

  /**
   * Lên lịch check tiếp theo
   * @param data Dữ liệu job tracking
   */
  async scheduleNextCheck(data: ZaloArticleTrackingJobData): Promise<void> {
    try {
      const job = await this.zaloArticleTrackingQueue.add(
        ZaloArticleTrackingJobName.CHECK_ARTICLE_STATUS,
        data,
        {
          delay: data.delayMs || 10000,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
          removeOnComplete: true,
          removeOnFail: false,
        },
      );

      this.logger.debug(
        `Scheduled next article check - Job ID: ${job.id}, Token: ${data.token.substring(0, 20)}..., Delay: ${data.delayMs}ms`,
      );
    } catch (error) {
      this.logger.error(
        `Error scheduling next article check: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Tạo job tracking bài viết mới
   * @param data Dữ liệu job tracking
   * @returns Job ID
   */
  async createTrackingJob(data: ZaloArticleTrackingJobData): Promise<string | undefined> {
    try {
      const job = await this.zaloArticleTrackingQueue.add(
        ZaloArticleTrackingJobName.CHECK_ARTICLE_STATUS,
        data,
        {
          delay: data.delayMs || 5000, // Delay 5 giây mặc định
          attempts: 15,
          backoff: {
            type: 'exponential',
            delay: 3000,
          },
          removeOnComplete: true,
          removeOnFail: false,
        },
      );

      this.logger.log(
        `Created article tracking job - Job ID: ${job.id}, Token: ${data.token.substring(0, 20)}...`,
      );

      return job.id;
    } catch (error) {
      this.logger.error(
        `Error creating article tracking job: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Cập nhật article ID vào database local (placeholder)
   * @param localArticleId ID của bài viết trong database local
   * @param zaloArticleId ID của bài viết từ Zalo
   */
  private async updateLocalArticleId(localArticleId: string, zaloArticleId: string): Promise<void> {
    // TODO: Implement logic cập nhật article ID vào database
    // Có thể sử dụng repository pattern hoặc gọi service khác
    this.logger.debug(`Updating local article ${localArticleId} with Zalo article ID: ${zaloArticleId}`);
    
    // Placeholder implementation
    // await this.articleRepository.updateZaloArticleId(localArticleId, zaloArticleId);
  }
}
