import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from 'src/infra/redis';
import { BaseStreamEvent } from '../interfaces';

/**
 * Streaming Service for Redis-based event streaming
 *
 * Handles platform-aware stream key generation, event publishing,
 * and stream lifecycle management using dependency injection.
 */
@Injectable()
export class StreamingService {
  private readonly logger = new Logger(StreamingService.name);

  constructor(private readonly redisService: RedisService) {}

  // ============================================================================
  // EVENT PUBLISHING
  // ============================================================================

  /**
   * Publish event to Redis stream using stream key
   *
   * @param streamKey Redis stream key
   * @param event Stream event to publish
   * @throws Does not throw - logs warnings on failure (non-critical)
   */
  async publishEvent(streamKey: string, event: BaseStreamEvent): Promise<void> {
    try {
      await this.redisService.xadd(streamKey, {
        event: JSON.stringify(event),
      });

      this.logger.debug(
        `Published event ${event.type} to stream ${streamKey}`,
        {
          streamKey,
          eventType: event.type,
        },
      );
    } catch (error) {
      // Log warnings but don't throw (non-critical)
      // Worker processing should continue even if streaming fails
      this.logger.warn(
        `Failed to publish event to Redis stream: ${error.message}`,
        {
          streamKey,
          eventType: event.type,
          error: error.message,
          stack: error.stack,
        },
      );
    }
  }

  // ============================================================================
  // STREAM LIFECYCLE MANAGEMENT
  // ============================================================================

  /**
   * Set TTL (Time To Live) for a stream
   *
   * @param streamKey Redis stream key
   * @param ttlSeconds TTL in seconds (default: 24 hours)
   * @returns True if TTL was set successfully, false otherwise
   */
  async setStreamTTL(
    streamKey: string,
    ttlSeconds: number = 86400,
  ): Promise<number> {
    try {
      const result = await this.redisService.expire(streamKey, ttlSeconds);

      if (result) {
        this.logger.debug(
          `Set TTL for stream ${streamKey} to ${ttlSeconds} seconds`,
        );
      } else {
        this.logger.warn(
          `Failed to set TTL for stream ${streamKey} - stream may not exist`,
        );
      }

      return result;
    } catch (error) {
      this.logger.error(
        `Error setting TTL for stream ${streamKey}: ${error.message}`,
        {
          streamKey,
          ttlSeconds,
          error: error.message,
        },
      );
      return 0;
    }
  }

  /**
   * Delete a stream immediately
   *
   * @param streamKey Redis stream key
   * @returns Number of streams deleted (1 if successful, 0 if not found)
   */
  async deleteStream(streamKey: string): Promise<number> {
    try {
      const deletedCount = await this.redisService.del(streamKey);

      if (deletedCount > 0) {
        this.logger.debug(`Deleted stream ${streamKey}`);
      } else {
        this.logger.debug(`Stream ${streamKey} not found for deletion`);
      }

      return deletedCount;
    } catch (error) {
      this.logger.error(
        `Error deleting stream ${streamKey}: ${error.message}`,
        {
          streamKey,
          error: error.message,
        },
      );
      return 0;
    }
  }

  /**
   * Check if a stream exists
   *
   * @param streamKey Redis stream key
   * @returns True if stream exists, false otherwise
   */
  async streamExists(streamKey: string): Promise<boolean> {
    try {
      return await this.redisService.exists(streamKey);
    } catch (error) {
      this.logger.error(
        `Error checking stream existence ${streamKey}: ${error.message}`,
        {
          streamKey,
          error: error.message,
        },
      );
      return false;
    }
  }
}
