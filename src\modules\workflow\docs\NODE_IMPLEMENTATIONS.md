# Node Implementations Documentation

## 📋 Overview

Đã implement tất cả các ENodeType trong `NodeExecutionService` với các hàm thực thi tương ứng.

## 🔧 Implemented Node Types

### **HTTP Nodes**

#### **1. HTTP_REQUEST**
- **Method:** `executeHttpRequest(node, context)`
- **Purpose:** Th<PERSON><PERSON> hiện HTTP requests
- **Parameters:**
  - `method`: HTTP method (GET, POST, PUT, DELETE)
  - `url`: Target URL
  - `headers`: Request headers
  - `body`: Request body
- **Output:**
  ```typescript
  {
    success: true,
    status_code: 200,
    status_text: 'OK',
    headers: { 'content-type': 'application/json' },
    body: { /* response data */ },
    response_time: 1500,
    final_url: 'https://api.example.com',
    metadata: { /* execution metadata */ }
  }
  ```

#### **2. WEBHOOK**
- **Method:** `executeWebhook(node, context)`
- **Purpose:** Process webhook data
- **Parameters:**
  - `webhookUrl`: Webhook URL
  - `method`: HTTP method
  - `headers`: Headers
- **Output:**
  ```typescript
  {
    success: true,
    webhook_triggered: true,
    webhook_url: 'https://webhook.url',
    method: 'POST',
    received_data: { /* webhook payload */ },
    processed_at: '2024-01-01T00:00:00.000Z'
  }
  ```

### **Logic Nodes**

#### **3. IF_CONDITION**
- **Method:** `executeIfCondition(node, context)`
- **Purpose:** Conditional logic execution
- **Parameters:**
  - `condition`: Condition expression
  - `trueValue`: Value when condition is true
  - `falseValue`: Value when condition is false
- **Output:**
  ```typescript
  {
    success: true,
    condition_met: true,
    condition_expression: 'value > 10',
    result_value: 'condition_true_result',
    evaluated_at: '2024-01-01T00:00:00.000Z'
  }
  ```

#### **4. SWITCH**
- **Method:** `executeSwitch(node, context)`
- **Purpose:** Multi-branch conditional logic
- **Parameters:**
  - `switchExpression`: Expression to evaluate
  - `cases`: Array of case objects
  - `defaultCase`: Default value
- **Output:**
  ```typescript
  {
    success: true,
    switch_expression: 'status',
    expression_value: 'active',
    matched_case: { value: 'active', result: 'user_active' },
    result_value: 'user_active'
  }
  ```

#### **5. LOOP**
- **Method:** `executeLoop(node, context)`
- **Purpose:** Iterative execution
- **Parameters:**
  - `loopType`: 'for' | 'foreach'
  - `iterations`: Number of iterations (for 'for' type)
  - `items`: Array of items (for 'foreach' type)
- **Output:**
  ```typescript
  {
    success: true,
    loop_type: 'for',
    total_iterations: 5,
    results: [
      { iteration: 1, data: 'Loop iteration 1', timestamp: 1234567890 },
      // ... more iterations
    ]
  }
  ```

#### **6. EDIT_FIELDS**
- **Method:** `executeEditFields(node, context)`
- **Purpose:** Modify data fields
- **Parameters:**
  - `operations`: Array of field operations
    - `{ type: 'set', field: 'name', value: 'new_value' }`
    - `{ type: 'remove', field: 'old_field' }`
    - `{ type: 'rename', field: 'old_name', newField: 'new_name' }`
- **Output:**
  ```typescript
  {
    success: true,
    original_data: { /* original data */ },
    edited_data: { /* modified data */ },
    operations_applied: [
      { type: 'set', field: 'name', value: 'new_value' }
    ],
    fields_count: 5
  }
  ```

#### **7. MERGE**
- **Method:** `executeMerge(node, context)`
- **Purpose:** Merge data from multiple sources
- **Parameters:**
  - `mergeMode`: 'combine' | 'array' | 'selective'
  - `sources`: Array of source types to merge
- **Output:**
  ```typescript
  {
    success: true,
    merge_mode: 'combine',
    merged_data: { /* merged data */ },
    source_count: 3,
    merged_fields: 10
  }
  ```

#### **8. FILTER**
- **Method:** `executeFilter(node, context)`
- **Purpose:** Filter data based on conditions
- **Parameters:**
  - `filterCondition`: Filter expression
  - `filterType`: 'include' | 'exclude'
- **Output:**
  ```typescript
  {
    success: true,
    filter_condition: 'item.status == "active"',
    filter_type: 'include',
    original_count: 10,
    filtered_count: 7,
    filtered_data: [ /* filtered items */ ]
  }
  ```

#### **9. WAIT**
- **Method:** `executeWait(node, context)`
- **Purpose:** Pause execution
- **Parameters:**
  - `waitType`: 'duration' | 'until'
  - `duration`: Wait duration in milliseconds
  - `waitUntil`: Condition to wait for
- **Output:**
  ```typescript
  {
    success: true,
    wait_type: 'duration',
    planned_duration: 5000,
    actual_duration: 5001,
    completed_at: '2024-01-01T00:00:00.000Z'
  }
  ```

## 🛠️ Helper Methods

### **Expression Evaluation**
- `evaluateCondition(condition, context)`: Evaluate boolean conditions
- `evaluateExpression(expression, context)`: Evaluate general expressions
- `evaluateFilterCondition(condition, item, context)`: Evaluate filter conditions

### **Context Utilities**
- `getValueFromContext(path, context)`: Get value using dot notation
- `parseValue(value)`: Parse string values to appropriate types

## 📊 Usage Examples

### **Basic Node Execution**
```typescript
const result = await nodeExecutionService.executeNode({
  workflowId: 'workflow-123',
  nodeId: 'node-456',
  userId: 1,
  context: {
    inputData: { name: 'John', age: 30 },
    previousOutputs: { node1: { result: 'success' } }
  }
});
```

### **HTTP Request Node**
```typescript
// Node parameters
{
  nodeDefinitionId: 'http-request',
  parameters: {
    method: 'POST',
    url: 'https://api.example.com/users',
    headers: { 'Content-Type': 'application/json' },
    body: { name: 'John', email: '<EMAIL>' }
  }
}
```

### **If Condition Node**
```typescript
// Node parameters
{
  nodeDefinitionId: 'if-condition',
  parameters: {
    condition: 'inputData.age >= 18',
    trueValue: 'adult',
    falseValue: 'minor'
  }
}
```

### **Loop Node**
```typescript
// Node parameters
{
  nodeDefinitionId: 'loop',
  parameters: {
    loopType: 'foreach',
    items: ['item1', 'item2', 'item3']
  }
}
```

## 🔄 Integration với XState

Các node implementations này được sử dụng bởi:
1. **XState Executors** - Thông qua existing executor system
2. **Standalone Execution** - Thông qua NodeExecutionService
3. **Testing** - Cho unit testing và debugging

## 🎯 Key Features

### **Error Handling**
- Comprehensive try-catch blocks
- Detailed error logging
- Graceful error recovery

### **Context Awareness**
- Access to input data, previous outputs, trigger data
- Flexible context structure
- Dot notation path resolution

### **Type Safety**
- TypeScript interfaces
- Proper type annotations
- Runtime type checking

### **Extensibility**
- Easy to add new node types
- Modular implementation
- Consistent interface

**All ENodeType implementations are now complete and ready for use!**
