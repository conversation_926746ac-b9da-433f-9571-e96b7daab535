// server-b/src/app.controller.ts
import { Controller } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { IExecutionPayload, NodeResponseDto } from '../dto/node-execute.dto';
import { AdminWorkflowService } from '../service/admin-workflow.service';

@Controller()
export class AdminWorkflowController {
    constructor(private readonly adminWorkflowService: AdminWorkflowService) { }

    // Bất kỳ tin nhắn nào gửi đến pattern này sẽ được hàm này xử lý
    @MessagePattern({ cmd: 'admin_execute_node' })
    async handleExecuteNode(@Payload() data: IExecutionPayload): Promise<NodeResponseDto> {
        console.log(`Server B received data:`, data);


        console.log('Server B finished processing. Sending result back.');

        // Trả về kết quả. Server A sẽ nhận được giá trị này.
        return { result: { message: 'Hello from Server B!' } };
    }

    // Bất kỳ tin nhắn nào gửi đến pattern này sẽ được hàm này xử lý
    @MessagePattern({ cmd: 'admin_execute_node' })
    async handleExecuteWorkflow(@Payload() data: IExecutionPayload): Promise<NodeResponseDto> {
        console.log(`Server B received data:`, data);


        console.log('Server B finished processing. Sending result back.');

        // Trả về kết quả. Server A sẽ nhận được giá trị này.
        return { result: { message: 'Hello from Server B!' } };
    }
}