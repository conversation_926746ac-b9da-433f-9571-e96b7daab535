import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AgentConfig } from '../../../shared/interfaces/agent-config.interface';
import { UserConvertCustomerMemory } from '../../../domains/external/entities/user-convert-customer-memory.entity';
import { WebsiteAgentConfigBuilderService } from './website-agent-config-builder.service';
import {
  ThreadKnowledgeFileAttachments,
  ThreadMediaAttachments,
  UserConvertCustomerInfo,
  UserInfo,
} from '../../../shared/interfaces';
import { WebsiteInfo } from '../interfaces/website-info.interface';
import {
  buildThreadAttachmentsPrompt,
  buildVisitorContextPrompt,
} from '../../../common-prompt-builders';

/**
 * Website Agent Configuration Service
 *
 * Handles website-specific agent configuration logic including:
 * - Dual-agent setup (planner + executor)
 * - Visitor memory integration
 * - Website-specific prompt building
 * - Business context and visitor data integration
 */
@Injectable()
export class WebsiteAgentConfigurationService {
  private readonly logger = new Logger(WebsiteAgentConfigurationService.name);

  constructor(
    @InjectRepository(UserConvertCustomerMemory)
    private readonly userConvertCustomerMemoryRepository: Repository<UserConvertCustomerMemory>,
    private readonly websiteAgentConfigBuilder: WebsiteAgentConfigBuilderService,
  ) {}

  /**
   * Build planner-executor configuration for website platform
   *
   * @param userConvertCustomer The website visitor/lead
   * @param websiteOwner Business owner information for API key context
   * @param websiteInfo Rich website context (browser, device, location data)
   * @param threadMediaAttachments Images from the conversation thread
   * @param threadKnowledgeFileAttachments Knowledge files from the conversation thread
   * @returns Object containing both planner and executor agent configurations
   */
  async buildPlannerExecutorConfiguration(param: {
    userConvertCustomer: UserConvertCustomerInfo;
    websiteOwner: UserInfo;
    websiteInfo: WebsiteInfo;
    threadMediaAttachments: ThreadMediaAttachments;
    threadKnowledgeFileAttachments?: ThreadKnowledgeFileAttachments;
    plannerAgentId?: string;
    executorAgentId?: string;
  }): Promise<{
    plannerAgent?: AgentConfig;
    executorAgent: AgentConfig;
  }> {
    const {
      userConvertCustomer,
      websiteOwner,
      websiteInfo,
      threadMediaAttachments,
      threadKnowledgeFileAttachments,
      plannerAgentId,
      executorAgentId,
    } = param;

    this.logger.debug(
      'Building planner-executor configuration for website platform',
      {
        visitorId: userConvertCustomer.id,
        websiteOwnerId: websiteOwner.userId,
        plannerAgentId,
        executorAgentId,
        threadMediaCount: Object.keys(threadMediaAttachments).length,
        threadKnowledgeFileCount: threadKnowledgeFileAttachments
          ? Object.keys(threadKnowledgeFileAttachments).length
          : 0,
      },
    );

    if (!executorAgentId) {
      throw new Error('ExecutorAgentId is required for website platform');
    }

    // Step 1: Build prompt builder map with website-specific prompts
    const promptBuilderMap = await this.buildPromptBuilderMap({
      userConvertCustomer,
      websiteOwner,
      websiteInfo,
      threadMediaAttachments,
      threadKnowledgeFileAttachments,
      plannerAgentId,
      executorAgentId,
    });

    // Step 2: Prepare agent IDs for configuration
    const agentIds = [
      ...(plannerAgentId ? [{ id: plannerAgentId }] : []),
      { id: executorAgentId },
    ];

    // Step 3: Build agent configurations using our facade service
    const agentConfigs = await this.websiteAgentConfigBuilder.buildAgentConfigs(
      {
        agentIds,
        promptBuilderMap,
        visitorId: userConvertCustomer.id,
        websiteOwnerId: websiteOwner.userId,
        websiteData: { websiteOwner, websiteInfo }, // Pass structured data
      },
    );

    // Step 4: Return structured result
    const result = {
      plannerAgent: plannerAgentId ? agentConfigs[plannerAgentId] : undefined,
      executorAgent: agentConfigs[executorAgentId],
    };

    this.logger.debug('Planner-executor configuration built successfully', {
      hasPlanner: !!result.plannerAgent,
      hasExecutor: !!result.executorAgent,
      executorTools: result.executorAgent?.tools?.length || 0,
    });

    return result;
  }

  /**
   * Build prompt builder map with website-specific prompts
   * Memory is fetched directly within the visitor context prompt builder
   */
  private async buildPromptBuilderMap(param: {
    userConvertCustomer: UserConvertCustomerInfo;
    websiteOwner: UserInfo;
    websiteInfo: WebsiteInfo;
    threadMediaAttachments: ThreadMediaAttachments;
    threadKnowledgeFileAttachments?: ThreadKnowledgeFileAttachments;
    plannerAgentId?: string;
    executorAgentId: string;
  }): Promise<Record<string, Array<() => string | Promise<string>>>> {
    const {
      userConvertCustomer,
      websiteOwner,
      websiteInfo,
      threadMediaAttachments,
      threadKnowledgeFileAttachments,
      plannerAgentId,
      executorAgentId,
    } = param;

    const promptBuilderMap: Record<
      string,
      Array<() => string | Promise<string>>
    > = {};

    // Common prompt builders for both agents
    const commonPromptBuilders = [
      () =>
        buildVisitorContextPrompt(
          userConvertCustomer.id,
          this.userConvertCustomerMemoryRepository,
          userConvertCustomer,
          websiteOwner,
          websiteInfo,
        ),
      () =>
        buildThreadAttachmentsPrompt(
          threadMediaAttachments,
          threadKnowledgeFileAttachments,
        ),
    ];

    // Planner-specific prompts
    if (plannerAgentId) {
      promptBuilderMap[plannerAgentId] = [...commonPromptBuilders];
    }

    // Executor-specific prompts
    promptBuilderMap[executorAgentId] = [...commonPromptBuilders];

    this.logger.debug('Built prompt builder map', {
      plannerPrompts: plannerAgentId
        ? promptBuilderMap[plannerAgentId].length
        : 0,
      executorPrompts: promptBuilderMap[executorAgentId].length,
    });

    return promptBuilderMap;
  }
}
