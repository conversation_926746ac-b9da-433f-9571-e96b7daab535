/**
 * HTTP Request Node Example
 * Demonstrates the enhanced HTTP request execution with detailed code and response
 */

import { IHttpRequestParameters } from '../interfaces';

/**
 * Example HTTP Request Parameters
 */
export const exampleHttpRequestParameters: IHttpRequestParameters = {
  url: 'https://jsonplaceholder.typicode.com/posts/{{json.trigger.postId}}',
  method: 'GET',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer {{json.auth.token}}',
    'User-Agent': 'RedAI-Workflow/1.0'
  },
  query_params: {
    'userId': '{{json.trigger.userId}}',
    'include': 'comments'
  },
  auth_type: 'BEARER',
  auth_config: {
    token: '{{json.auth.token}}'
  },
  timeout: 30000
};

/**
 * Example Context Data
 */
export const exampleContext = {
  trigger: {
    postId: '1',
    userId: '123'
  },
  auth: {
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
  }
};

/**
 * Example Success Response
 */
export const exampleSuccessResponse = {
  success: true,
  status_code: 200,
  status_text: 'OK',
  headers: {
    'content-type': 'application/json; charset=utf-8',
    'content-length': '292',
    'x-request-id': 'req-12345'
  },
  body: {
    id: 1,
    title: 'sunt aut facere repellat provident occaecati excepturi optio reprehenderit',
    body: 'quia et suscipit\nsuscipit recusandae consequuntur expedita et cum\nreprehenderit molestiae ut ut quas totam\nnostrum rerum est autem sunt rem eveniet architecto',
    userId: 1
  },
  body_text: '{"id":1,"title":"sunt aut facere...","body":"quia et suscipit...","userId":1}',
  response_time: 1250,
  final_url: 'https://jsonplaceholder.typicode.com/posts/1',
  error: null,
  
  // Detailed response information
  response: {
    code: 200,
    status: 'OK',
    headers: {
      'content-type': 'application/json; charset=utf-8',
      'content-length': '292',
      'x-request-id': 'req-12345'
    },
    data: {
      id: 1,
      title: 'sunt aut facere repellat provident occaecati excepturi optio reprehenderit',
      body: 'quia et suscipit\nsuscipit recusandae consequuntur expedita et cum\nreprehenderit molestiae ut ut quas totam\nnostrum rerum est autem sunt rem eveniet architecto',
      userId: 1
    },
    size: 292,
    contentType: 'application/json; charset=utf-8'
  },
  
  // Request information for debugging
  request: {
    method: 'GET',
    url: 'https://jsonplaceholder.typicode.com/posts/1',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
      'User-Agent': 'RedAI-Workflow/1.0'
    },
    body: null,
    timestamp: 1704067200000
  },
  
  metadata: {
    content_length: 292,
    request_id: 'req-12345',
    executionTime: 1250,
    nodeId: 'http-node-123',
    workflowId: 'workflow-456',
    executionId: 'exec-789',
    userId: 1
  }
};

/**
 * Example Error Response (HTTP Error)
 */
export const exampleErrorResponse = {
  success: false,
  status_code: 404,
  status_text: 'Not Found',
  headers: {
    'content-type': 'application/json',
    'x-request-id': 'req-12346'
  },
  body: {
    error: 'Post not found',
    message: 'The requested post does not exist'
  },
  body_text: '{"error":"Post not found","message":"The requested post does not exist"}',
  response_time: 850,
  final_url: 'https://jsonplaceholder.typicode.com/posts/999',
  error: 'Request failed with status code 404',
  
  // Detailed error response information
  response: {
    code: 404,
    status: 'Not Found',
    headers: {
      'content-type': 'application/json',
      'x-request-id': 'req-12346'
    },
    data: {
      error: 'Post not found',
      message: 'The requested post does not exist'
    },
    size: 78,
    contentType: 'application/json'
  },
  
  // Request information for debugging
  request: {
    method: 'GET',
    url: 'https://jsonplaceholder.typicode.com/posts/999',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
      'User-Agent': 'RedAI-Workflow/1.0'
    },
    body: null,
    timestamp: 1704067200000
  },
  
  metadata: {
    content_length: 78,
    request_id: 'req-12346',
    executionTime: 850,
    nodeId: 'http-node-123',
    workflowId: 'workflow-456',
    executionId: 'exec-789',
    userId: 1
  }
};

/**
 * Example Network Error Response
 */
export const exampleNetworkErrorResponse = {
  success: false,
  status_code: 0,
  status_text: 'Request Failed',
  headers: {},
  body: null,
  body_text: 'Network Error: connect ECONNREFUSED',
  response_time: 30000,
  final_url: 'https://invalid-domain.example.com/api',
  error: 'Network Error: connect ECONNREFUSED',
  
  // Empty response for network errors
  response: {
    code: 0,
    status: 'Network Error',
    headers: {},
    data: null,
    size: 0,
    contentType: 'unknown'
  },
  
  // Request information for debugging
  request: {
    method: 'GET',
    url: 'https://invalid-domain.example.com/api',
    headers: {
      'Content-Type': 'application/json',
      'User-Agent': 'RedAI-Workflow/1.0'
    },
    body: null,
    timestamp: 1704067200000
  },
  
  metadata: {
    content_length: 0,
    request_id: 'unknown',
    executionTime: 30000,
    nodeId: 'http-node-123',
    workflowId: 'workflow-456',
    executionId: 'exec-789',
    userId: 1
  }
};

/**
 * Usage Example
 */
export const usageExample = `
// Execute HTTP Request Node
const result = await nodeExecutionService.executeNode({
  workflowId: 'workflow-456',
  nodeId: 'http-node-123',
  executionId: 'exec-789',
  userId: 1,
  context: {
    trigger: { postId: '1', userId: '123' },
    auth: { token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' }
  }
});

// Access response details
console.log('Success:', result.success);
console.log('Status Code:', result.response.code);
console.log('Response Data:', result.response.data);
console.log('Request Method:', result.request.method);
console.log('Request URL:', result.request.url);
console.log('Response Size:', result.response.size);
console.log('Content Type:', result.response.contentType);
`;
