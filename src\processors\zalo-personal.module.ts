import { Module } from '@nestjs/common';
import { ZaloPersonalProcessor } from './zalo-personal.processor';
import { AutomationWebService } from '../shared/services/automation-web.service';

/**
 * Module cho Zalo Personal processors
 * Note: Queue registration được handle ở QueueModule global
 */
@Module({
  providers: [ZaloPersonalProcessor, AutomationWebService],
  exports: [ZaloPersonalProcessor],
})
export class ZaloPersonalModule {}
