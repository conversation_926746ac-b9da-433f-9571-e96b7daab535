import { Injectable, Logger } from '@nestjs/common';
import { Platform, ModelFeature, InputModality } from '../../../enums';
import { AgentConfig } from '../../../interfaces/agent-config.interface';
import { McpConfiguration } from '../../../interfaces/mcp-config.interface';
import { McpClientService } from '../../../services';
import { WebSearchTool } from '../../../runtime/tools/web-search.tool';
import { AgentMemoryTool } from '../../../runtime/tools/agent-memory.tool';
import { UserMemoryTool } from '../../../runtime/tools/user-memory.tool';
import { ImageLoaderTool } from '../../../runtime/tools/image-loader.tool';
import { AuthContext, PlatformStrategy } from 'src/modules/agents/interfaces';
import { UserKnowledgeRAGTool } from 'src/modules/agents/runtime/tools/user-knowledge-rag.tool';
import { UserMediaRAGTool } from 'src/modules/agents/runtime/tools/user-media-rag.tool';
import { UserProductRAGTool } from 'src/modules/agents/runtime/tools/user-product-rag.tool';
import { SelectQueryBuilder } from 'typeorm';
import { Integration } from '../../../entities';

@Injectable()
export class InAppPlatformStrategy implements PlatformStrategy {
  private readonly logger = new Logger(InAppPlatformStrategy.name);
  readonly platform = Platform.IN_APP;

  constructor(
    private readonly mcpClientService: McpClientService,
    private readonly webSearchService: WebSearchTool,
    private readonly agentMemoryService: AgentMemoryTool,
    private readonly userMemoryTool: UserMemoryTool,
    private readonly imageLoaderTool: ImageLoaderTool,
    private readonly userKnowledgeRAGTool: UserKnowledgeRAGTool,
    // private readonly userMediaRAGTool: UserMediaRAGTool,
    // private readonly userProductRAGTool: UserProductRAGTool,
  ) {}

  async buildMcpClient(
    mcpConfig: Record<string, McpConfiguration> | null,
    authContext: AuthContext,
  ): Promise<any> {
    const { jwt } = authContext;

    return jwt && mcpConfig
      ? await this.mcpClientService.buildMcpClients(
          mcpConfig,
          undefined,
          this.platform,
          { jwt },
        )
      : null;
  }

  getToolsForAgent(agentConfig: AgentConfig): any[] {
    const result = agentConfig.model.features.includes(ModelFeature.TOOL_CALL)
      ? [
          this.webSearchService,
          this.agentMemoryService,
          this.userMemoryTool,
          agentConfig.model.inputModalities.includes(InputModality.TEXT)
            ? this.userKnowledgeRAGTool
            : undefined,
          // agentConfig.model.inputModalities.includes(InputModality.TEXT)
          //   ? this.userMediaRAGTool
          //   : undefined,
          // agentConfig.model.inputModalities.includes(InputModality.TEXT)
          //   ? this.userProductRAGTool
          //   : undefined,
          agentConfig.model.inputModalities.includes(InputModality.IMAGE)
            ? this.imageLoaderTool
            : undefined,
        ].filter(Boolean)
      : [];
    this.logger.debug(
      `getToolsForAgent: ${agentConfig.id} - ${result.length} tools available`,
    );
    return result;
  }

  customizeIntegrationQuery(
    baseQuery: SelectQueryBuilder<Integration>,
    authContext: AuthContext,
  ): SelectQueryBuilder<Integration> {
    const { userId } = authContext;

    // Integration selection logic based on Agent.use_system_key and agent type
    if (userId) {
      // For user agents: check use_system_key field
      return baseQuery.andWhere(
        `
        (
          (agent.use_system_key = true AND integration.employee_id IS NOT NULL) OR
          (agent.use_system_key = false AND integration.user_id = :userId)
        )
      `,
        { userId },
      );
    } else {
      // For system agents: always use system integrations
      return baseQuery.andWhere('integration.employee_id IS NOT NULL');
    }
  }
}
