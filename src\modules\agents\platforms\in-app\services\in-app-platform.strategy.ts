import { Injectable, Logger } from '@nestjs/common';
import { Platform, ModelFeature, InputModality } from '../../../shared/enums';
import { AgentConfig } from '../../../shared/interfaces/agent-config.interface';
import { McpConfiguration } from '../../../shared/interfaces/mcp-config.interface';
import { McpClientService } from '../../../shared/services';
import { WebSearchTool } from '../../../shared/tools/web-search.tool';
import { AgentMemoryTool } from '../../../shared/tools/agent-memory.tool';
import { UserMemoryTool } from '../../../shared/tools/user-memory.tool';
import { ImageLoaderTool } from '../../../shared/tools/image-loader.tool';
import { AuthContext, PlatformStrategy } from '../../../shared/interfaces';
import { UserKnowledgeRAGTool } from '../../../shared/tools/user-knowledge-rag.tool';
import { UserMediaRAGTool } from '../../../shared/tools/user-media-rag.tool';
import { UserProductRAGTool } from '../../../shared/tools/user-product-rag.tool';
import { SelectQueryBuilder } from 'typeorm';
import { Integration } from '../../../shared/entities';

@Injectable()
export class InAppPlatformStrategy implements PlatformStrategy {
  private readonly logger = new Logger(InAppPlatformStrategy.name);
  readonly platform = Platform.IN_APP;

  constructor(
    private readonly mcpClientService: McpClientService,
    private readonly webSearchService: WebSearchTool,
    private readonly agentMemoryService: AgentMemoryTool,
    private readonly userMemoryTool: UserMemoryTool,
    private readonly imageLoaderTool: ImageLoaderTool,
    private readonly userKnowledgeRAGTool: UserKnowledgeRAGTool,
    // private readonly userMediaRAGTool: UserMediaRAGTool,
    // private readonly userProductRAGTool: UserProductRAGTool,
  ) {}

  async buildMcpClient(
    mcpConfig: Record<string, McpConfiguration> | null,
    authContext: AuthContext,
  ): Promise<any> {
    const { jwt } = authContext;

    return jwt && mcpConfig
      ? await this.mcpClientService.buildMcpClients(
          mcpConfig,
          undefined,
          this.platform,
          { jwt },
        )
      : null;
  }

  getToolsForAgent(agentConfig: AgentConfig): any[] {
    const result = agentConfig.model.features.includes(ModelFeature.TOOL_CALL)
      ? [
          this.webSearchService,
          this.agentMemoryService,
          this.userMemoryTool,
          agentConfig.model.inputModalities.includes(InputModality.TEXT)
            ? this.userKnowledgeRAGTool
            : undefined,
          // agentConfig.model.inputModalities.includes(InputModality.TEXT)
          //   ? this.userMediaRAGTool
          //   : undefined,
          // agentConfig.model.inputModalities.includes(InputModality.TEXT)
          //   ? this.userProductRAGTool
          //   : undefined,
          agentConfig.model.inputModalities.includes(InputModality.IMAGE)
            ? this.imageLoaderTool
            : undefined,
        ].filter(Boolean)
      : [];
    this.logger.debug(
      `getToolsForAgent: ${agentConfig.id} - ${result.length} tools available`,
    );
    return result;
  }

  customizeIntegrationQuery(
    baseQuery: SelectQueryBuilder<Integration>,
    authContext: AuthContext,
  ): SelectQueryBuilder<Integration> {
    const { userId } = authContext;

    // Integration selection logic based on Agent.use_system_key and agent type
    if (userId) {
      // For user agents: check use_system_key field
      return baseQuery.andWhere(
        `
        (
          (agent.use_system_key = true AND integration.employee_id IS NOT NULL) OR
          (agent.use_system_key = false AND integration.user_id = :userId)
        )
      `,
        { userId },
      );
    } else {
      // For system agents: always use system integrations
      return baseQuery.andWhere('integration.employee_id IS NOT NULL');
    }
  }
}
