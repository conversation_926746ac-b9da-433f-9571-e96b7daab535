import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { HttpStatusCode } from 'axios';

/**
 * HTTP request parameters interface
 */
export interface IHttpRequestParameters {
  url: string;
  method: string;
  headers?: Record<string, string>;
  queryParams?: Record<string, any>;
  timeout?: number;
  auth?: {
    type: string;
    token?: string;
    username?: string;
    password?: string;
    key?: string;
    value?: string;
    location?: 'header' | 'query';
  };
}

/**
 * HTTP request input interface
 */
export interface IHttpRequestInput {
  body?: any;
}

/**
 * HTTP request output interface
 */
export interface IHttpRequestOutput {
  success: boolean;
  status_code: number;
  status_text: string;
  headers: Record<string, any>;
  body: any;
  response_time: number;
  final_url: string;
  error: string | null;

  /** Raw response data for debugging */
  response: {
    /** HTTP status code */
    code: number;
    /** Response status text */
    status: string;
    /** Response headers */
    headers: Record<string, any>;
    /** Response body/data */
    data: any;
    /** Response size in bytes */
    size?: number;
    /** Response content type */
    contentType?: string;
  };

  /** Request details for debugging */
  request: {
    /** Request method */
    method: string;
    /** Request URL */
    url: string;
    /** Request headers */
    headers: Record<string, any>;
    /** Request body */
    body?: any;
    /** Request timestamp */
    timestamp: number;
  };

  metadata?: {
    content_length?: number;
    request_id?: string;
    [key: string]: any;
  };
}

/**
 * Shared service for HTTP operations
 * Used by HTTP_REQUEST nodes
 */
@Injectable()
export class HttpClientService {
  private readonly logger = new Logger(HttpClientService.name);

  constructor(private readonly httpService: HttpService) {}

  /**
   * Execute HTTP request with retry logic
   */
  async executeHttpRequest(
    input: IHttpRequestInput,
    parameters: IHttpRequestParameters
  ): Promise<{code: HttpStatusCode, data: Record<string, any>}> {
    const startTime = Date.now();

    try {
      this.logger.debug(`Executing HTTP ${parameters.method} request to ${parameters.url}`);

      // Build request configuration
      const config = this.buildRequestConfig(parameters);

      // Execute request based on method
      const response = await this.performRequest(parameters.method, parameters.url, input.body, config);

      const endTime = Date.now();
      const executionTime = endTime - startTime;

      this.logger.debug(`HTTP request completed in ${executionTime}ms with status ${response.status}`);

      // Calculate response size
      const responseSize = response.headers['content-length']
        ? parseInt(response.headers['content-length'])
        : (typeof response.data === 'string' ? response.data.length : JSON.stringify(response.data || {}).length);

      return {
        success: response.status >= 200 && response.status < 300,
        status_code: response.status,
        status_text: response.statusText,
        headers: response.headers,
        body: response.data,
        response_time: executionTime,
        final_url: parameters.url,
        error: null,

        // Detailed response information
        response: {
          code: response.status,
          status: response.statusText,
          headers: response.headers,
          data: response.data,
          size: responseSize,
          contentType: response.headers['content-type'] || 'unknown'
        },

        // Request information for debugging
        request: {
          method: parameters.method.toUpperCase(),
          url: parameters.url,
          headers: config.headers || {},
          body: input.body,
          timestamp: startTime
        },

        metadata: {
          content_length: responseSize,
          request_id: response.headers['x-request-id'] || 'unknown'
        }
      };
      
    } catch (error) {
      const endTime = Date.now();
      const executionTime = endTime - startTime;

      this.logger.error(`HTTP request failed after ${executionTime}ms:`, error);

      // Build request configuration for error response
      const config = this.buildRequestConfig(parameters);

      // Handle axios error (HTTP error response)
      if (error.response) {
        const errorResponseSize = error.response.headers['content-length']
          ? parseInt(error.response.headers['content-length'])
          : (typeof error.response.data === 'string' ? error.response.data.length : JSON.stringify(error.response.data || {}).length);

        return {
          success: false,
          status_code: error.response.status,
          status_text: error.response.statusText,
          headers: error.response.headers,
          body: error.response.data,
          response_time: executionTime,
          final_url: parameters.url,
          error: error.message,

          // Detailed error response information
          response: {
            code: error.response.status,
            status: error.response.statusText,
            headers: error.response.headers,
            data: error.response.data,
            size: errorResponseSize,
            contentType: error.response.headers['content-type'] || 'unknown'
          },

          // Request information for debugging
          request: {
            method: parameters.method.toUpperCase(),
            url: parameters.url,
            headers: config.headers || {},
            body: input.body,
            timestamp: startTime
          },

          metadata: {
            content_length: errorResponseSize,
            request_id: error.response.headers['x-request-id'] || 'unknown'
          }
        };
      }

      // Handle network or other errors (no response)
      return {
        success: false,
        status_code: 0,
        status_text: 'Request Failed',
        headers: {},
        body: null,
        response_time: executionTime,
        final_url: parameters.url,
        error: error.message,

        // Empty response for network errors
        response: {
          code: 0,
          status: 'Network Error',
          headers: {},
          data: null,
          size: 0,
          contentType: 'unknown'
        },

        // Request information for debugging
        request: {
          method: parameters.method.toUpperCase(),
          url: parameters.url,
          headers: config.headers || {},
          body: input.body,
          timestamp: startTime
        },

        metadata: {
          content_length: 0,
          request_id: 'unknown'
        }
      };
    }
  }

  /**
   * Build axios request configuration
   */
  private buildRequestConfig(parameters: IHttpRequestParameters): any {
    const config: any = {
      timeout: parameters.timeout || 30000,
      headers: parameters.headers || {},
      validateStatus: () => true // Don't throw on HTTP error status
    };

    // Add authentication
    if (parameters.auth) {
      switch (parameters.auth.type) {
        case 'BEARER':
          config.headers['Authorization'] = `Bearer ${parameters.auth.token}`;
          break;
        case 'BASIC':
          config.auth = {
            username: parameters.auth.username,
            password: parameters.auth.password
          };
          break;
        case 'API_KEY':
          if (parameters.auth.location === 'header' && parameters.auth.key && parameters.auth.value) {
            config.headers[parameters.auth.key] = parameters.auth.value;
          } else if (parameters.auth.location === 'query' && parameters.auth.key && parameters.auth.value) {
            config.params = config.params || {};
            config.params[parameters.auth.key] = parameters.auth.value;
          }
          break;
      }
    }

    // Add query parameters
    if (parameters.queryParams) {
      config.params = { ...config.params, ...parameters.queryParams };
    }

    return config;
  }

  /**
   * Perform HTTP request based on method
   */
  private async performRequest(
    method: string,
    url: string,
    body?: any,
    config?: any
  ): Promise<any> {
    switch (method.toUpperCase()) {
      case 'GET':
        return firstValueFrom(this.httpService.get(url, config));
      case 'POST':
        return firstValueFrom(this.httpService.post(url, body, config));
      case 'PUT':
        return firstValueFrom(this.httpService.put(url, body, config));
      case 'PATCH':
        return firstValueFrom(this.httpService.patch(url, body, config));
      case 'DELETE':
        return firstValueFrom(this.httpService.delete(url, config));
      case 'HEAD':
        return firstValueFrom(this.httpService.head(url, config));
      case 'OPTIONS':
        return firstValueFrom(this.httpService.request({ ...config, method: 'OPTIONS', url }));
      default:
        throw new Error(`Unsupported HTTP method: ${method}`);
    }
  }

  /**
   * Validate HTTP request parameters
   */
  validateParameters(parameters: IHttpRequestParameters): string[] {
    const errors: string[] = [];

    if (!parameters.url) {
      errors.push('URL is required');
    }

    if (!parameters.method) {
      errors.push('HTTP method is required');
    }

    // Validate URL format
    try {
      new URL(parameters.url);
    } catch {
      errors.push('Invalid URL format');
    }

    // Validate timeout
    if (parameters.timeout && (parameters.timeout < 1000 || parameters.timeout > 300000)) {
      errors.push('Timeout must be between 1000ms and 300000ms');
    }

    return errors;
  }

  /**
   * Test HTTP connection
   */
  async testConnection(url: string): Promise<boolean> {
    try {
      const response = await firstValueFrom(
        this.httpService.head(url, { timeout: 5000 })
      );
      return response.status >= 200 && response.status < 400;
    } catch {
      return false;
    }
  }
}
