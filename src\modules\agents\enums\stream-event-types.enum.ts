/**
 * Stream Event Types Enum
 * Defines all event types for Redis streaming architecture
 *
 * Based on REDIS-STREAMING-ARCHITECTURE.md - Section "Event Types Catalog"
 */
export enum StreamEventType {
  // ============================================================================
  // TEXT MESSAGE EVENTS (Streaming Support)
  // ============================================================================

  /**
   * Text message start - indicates new message creation
   * Used when a new assistant message begins
   */
  TEXT_MESSAGE_START = 'text_message_start',

  /**
   * Text message content - streaming text chunks
   * Used for real-time streaming of message content from LangGraph
   */
  TEXT_MESSAGE_CONTENT = 'text_message_content',

  /**
   * Text message end - final complete message
   * Used when message is complete with final content from on_chat_model_end
   */
  TEXT_MESSAGE_END = 'text_message_end',

  // ============================================================================
  // MEDIA MESSAGE EVENTS (Single Events)
  // ============================================================================

  /**
   * Image message - single event with image URL and metadata
   * Used for generated or uploaded images with attachment correlation
   */
  IMAGE_MESSAGE = 'image_message',

  /**
   * File message - single event with file URL and metadata
   * Used for generated or uploaded files with attachment correlation
   */
  FILE_MESSAGE = 'file_message',

  // ============================================================================
  // TOOL EXECUTION EVENTS
  // ============================================================================

  /**
   * Tool call start - tool call initiation
   * Used when LangGraph starts executing a tool, includes parent message ID
   */
  TOOL_CALL_START = 'tool_call_start',

  /**
   * Tool call arguments - streaming tool call arguments
   * Used for real-time streaming of tool call arguments from LangGraph
   */
  TOOL_CALL_ARGS = 'tool_call_args',

  /**
   * Tool call end - tool execution result
   * Used when LangGraph completes tool execution with results
   */
  TOOL_CALL_END = 'tool_call_end',

  /**
   * tool call interrupt - tool execution interrupted by human review
   * Used when human review is triggered during tool execution
   */
  TOOL_CALL_INTERRUPT = 'tool_call_interrupt',

  // ============================================================================
  // RUN LIFECYCLE EVENTS
  // ============================================================================

  /**
   * Run started - run execution has begun
   * Used when run starts processing with agent information
   */
  RUN_STARTED = 'run_started',

  /**
   * Run cancelled - user or system cancelled the run
   * Used when run is cancelled before completion
   */
  RUN_CANCELLED = 'run_cancelled',

  /**
   * Run complete - successful completion with cost information
   * Used when run completes successfully with total cost exposure
   */
  RUN_COMPLETE = 'run_complete',

  /**
   * Run error - structured error information
   * Used when run fails with detailed error information for debugging
   */
  RUN_ERROR = 'run_error',
}

/**
 * Get all stream event types as array
 * @returns Array of all stream event type values
 */
export function getAllStreamEventTypes(): StreamEventType[] {
  return Object.values(StreamEventType);
}

/**
 * Check if a string is a valid stream event type
 * @param value String to check
 * @returns True if valid stream event type, false otherwise
 */
export function isValidStreamEventType(
  value: string,
): value is StreamEventType {
  return Object.values(StreamEventType).includes(value as StreamEventType);
}

/**
 * Get event type category
 * @param eventType Stream event type
 * @returns Event category for grouping and processing
 */
export function getEventTypeCategory(eventType: StreamEventType): string {
  const categories: Record<StreamEventType, string> = {
    [StreamEventType.TEXT_MESSAGE_START]: 'text_message',
    [StreamEventType.TEXT_MESSAGE_CONTENT]: 'text_message',
    [StreamEventType.TEXT_MESSAGE_END]: 'text_message',
    [StreamEventType.IMAGE_MESSAGE]: 'media_message',
    [StreamEventType.FILE_MESSAGE]: 'media_message',
    [StreamEventType.TOOL_CALL_START]: 'tool_call',
    [StreamEventType.TOOL_CALL_ARGS]: 'tool_call',
    [StreamEventType.TOOL_CALL_END]: 'tool_call',
    [StreamEventType.TOOL_CALL_INTERRUPT]: 'tool_call',
    [StreamEventType.RUN_STARTED]: 'run_lifecycle',
    [StreamEventType.RUN_CANCELLED]: 'run_lifecycle',
    [StreamEventType.RUN_COMPLETE]: 'run_lifecycle',
    [StreamEventType.RUN_ERROR]: 'run_lifecycle',
  };

  return categories[eventType] || 'unknown';
}

/**
 * Check if event type supports streaming
 * @param eventType Stream event type
 * @returns True if event type supports streaming, false for single events
 */
export function isStreamingEventType(eventType: StreamEventType): boolean {
  const streamingTypes = [
    StreamEventType.TEXT_MESSAGE_START,
    StreamEventType.TEXT_MESSAGE_CONTENT,
    StreamEventType.TEXT_MESSAGE_END,
  ];

  return streamingTypes.includes(eventType);
}

/**
 * Check if event type is terminal (indicates completion)
 * @param eventType Stream event type
 * @returns True if event type indicates completion/termination
 */
export function isTerminalEventType(eventType: StreamEventType): boolean {
  const terminalTypes = [
    StreamEventType.TEXT_MESSAGE_END,
    StreamEventType.IMAGE_MESSAGE,
    StreamEventType.FILE_MESSAGE,
    StreamEventType.TOOL_CALL_END,
    StreamEventType.RUN_COMPLETE,
    StreamEventType.RUN_ERROR,
  ];

  return terminalTypes.includes(eventType);
}

export function isLifecycleEventType(eventType: StreamEventType): boolean {
  const lifecycleTypes = [
    StreamEventType.RUN_STARTED,
    StreamEventType.RUN_CANCELLED,
    StreamEventType.RUN_COMPLETE,
    StreamEventType.RUN_ERROR,
  ];

  return lifecycleTypes.includes(eventType);
}

/**
 * Get display name for event type
 * @param eventType Stream event type
 * @returns Human-readable event type name
 */
export function getEventTypeDisplayName(eventType: StreamEventType): string {
  const displayNames: Record<StreamEventType, string> = {
    [StreamEventType.TEXT_MESSAGE_START]: 'Text Message Started',
    [StreamEventType.TEXT_MESSAGE_CONTENT]: 'Text Message Content',
    [StreamEventType.TEXT_MESSAGE_END]: 'Text Message Completed',
    [StreamEventType.IMAGE_MESSAGE]: 'Image Message',
    [StreamEventType.FILE_MESSAGE]: 'File Message',
    [StreamEventType.TOOL_CALL_START]: 'Tool Call Started',
    [StreamEventType.TOOL_CALL_ARGS]: 'Tool Call Args',
    [StreamEventType.TOOL_CALL_END]: 'Tool Call Completed',
    [StreamEventType.TOOL_CALL_INTERRUPT]: 'Tool Call Interrupted',
    [StreamEventType.RUN_STARTED]: 'Run Started',
    [StreamEventType.RUN_CANCELLED]: 'Run Cancelled',
    [StreamEventType.RUN_COMPLETE]: 'Run Completed',
    [StreamEventType.RUN_ERROR]: 'Run Error',
  };

  return displayNames[eventType] || eventType;
}
