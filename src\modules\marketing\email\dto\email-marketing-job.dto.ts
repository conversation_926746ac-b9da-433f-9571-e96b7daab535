import { CampaignAudience } from '../../types/campaign.types';

/**
 * DTO cho job email marketing - Đ<PERSON> đư<PERSON>c xử lý hoàn toàn ở backend
 */
export interface EmailMarketingJobDto {
  /**
   * ID của campaign
   */
  campaignId: number;

  /**
   * Thông tin audience nhận email
   */
  audience: {
    name: string;
    email: string;
  };

  /**
   * Email người nhận (đã validate)
   */
  email: string;

  /**
   * Tiêu đề email (đã xử lý template variables)
   */
  subject: string;

  /**
   * Nội dung email HTML (đã xử lý template variables)
   */
  content: string;

  /**
   * Thông tin server gửi email (đã decrypt và validate)
   * C<PERSON> thể là SMTP hoặc Gmail
   */
  serverConfig: {
    host: string;
    port: number;
    secure: boolean;
    user: string;
    password: string;
    from: string;
    // Thêm fields cho Gmail
    serverType?: 'SMTP' | 'GMAIL';
    accessToken?: string;
    refreshToken?: string;
  };

  /**
   * ID tracking duy nhất cho email này
   */
  trackingId: string;

  /**
   * Thờ<PERSON> gian tạo job
   */
  createdAt: number;
}

/**
 * DTO cho recipient trong batch email job
 */
export interface EmailRecipientDto {
  /**
   * Thông tin audience nhận email
   */
  audience: CampaignAudience;

  /**
   * Email người nhận
   */
  email: string;
}

/**
 * DTO cho batch email marketing job (chứa nhiều email)
 */
export interface BatchEmailMarketingJobDto {
  /**
   * ID của campaign
   */
  campaignId: number;

  /**
   * ID của template email (worker sẽ lấy subject/content từ template)
   */
  templateId: number;

  /**
   * Template variables áp dụng cho tất cả recipients (worker sẽ kết hợp với custom fields của từng audience)
   */
  templateVariables: Record<string, any>;

  /**
   * Danh sách người nhận
   */
  recipients: EmailRecipientDto[];

  /**
   * Cấu hình SMTP server (optional)
   */
  server?: {
    host?: string;
    port?: number;
    secure?: boolean;
    user?: string;
    password?: string;
    from?: string;
  };

  /**
   * Timestamp tạo job
   */
  createdAt: number;
}

/**
 * Enum định nghĩa các tên job trong queue email marketing
 */
export enum EmailMarketingJobName {
  /**
   * Job gửi email marketing (single email)
   */
  SEND_EMAIL = 'send-email',

  /**
   * Job gửi batch email marketing (multiple emails)
   */
  SEND_BATCH_EMAIL = 'send-batch-email',

  /**
   * Job gửi admin email campaign (single email)
   */
  SEND_ADMIN_EMAIL = 'send-admin-email',

  /**
   * Job gửi batch admin email campaign (multiple emails)
   */
  SEND_BATCH_ADMIN_EMAIL = 'send-batch-admin-email',
}
