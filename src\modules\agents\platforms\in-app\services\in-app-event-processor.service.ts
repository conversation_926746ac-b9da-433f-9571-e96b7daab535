import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from 'src/infra/redis';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { InAppJobData } from '../interfaces';
import { StreamEvent as InternalStreamEvent } from '../../../shared/interfaces/stream-event.interface';
import { ExclusiveTags, MessageRole, StreamEventType } from '../../../shared/enums';
import { StreamEvent } from '@langchain/core/tracers/log_stream';
import { InternalConversationMessage } from 'src/modules/agents/domains/internal/entities';

/**
 * Event Processor Service
 *
 * Handles dynamic multi-message creation during LangGraph execution.
 * Transforms LangGraph events into Redis stream events for real-time frontend consumption.
 *
 * Key Features:
 * - Multi-message support (one run can create multiple assistant messages)
 * - Dynamic message creation on-demand during streaming
 * - Event transformation from LangGraph to StreamEventType
 * - Real-time Redis streaming with error handling
 *
 * Status: ✅ Complete (Task 4.2)
 * - LangGraph event processing with multi-message support
 * - Proper event transformation to StreamEventType
 * - Database integration via WorkerDatabaseService
 * - Redis streaming via RedisStreamingService
 */
@Injectable()
export class InAppEventProcessorService {
  private readonly logger = new Logger(InAppEventProcessorService.name);

  /**
   * Function map for LangGraph event handlers
   * Cleaner than switch statement, easier to maintain and extend
   */
  private readonly eventHandlers = new Map<
    string,
    (
      langGraphEvent: StreamEvent,
      jobData: InAppJobData,
      streamKey: string,
      currentMessageId: string | null,
    ) => Promise<{ newMessageId?: string | null; messageUpdated?: boolean }>
  >([
    ['on_chat_model_start', this.handleChatModelStart.bind(this)],
    ['on_chat_model_stream', this.handleChatModelStream.bind(this)],
    ['on_chat_model_end', this.handleChatModelEnd.bind(this)],
    ['on_tool_start', this.handleToolStart.bind(this)],
    ['on_tool_end', this.handleToolEnd.bind(this)],
    ['on_chain_stream', this.handleChainStream.bind(this)],
    // Run lifecycle events
    [StreamEventType.RUN_STARTED, this.handleRunStarted.bind(this)],
    [StreamEventType.RUN_COMPLETE, this.handleRunComplete.bind(this)],
    [StreamEventType.RUN_CANCELLED, this.handleRunCancelled.bind(this)],
    [StreamEventType.RUN_ERROR, this.handleRunError.bind(this)],
  ]);

  constructor(
    private readonly redisService: RedisService,
    @InjectRepository(InternalConversationMessage)
    private readonly messageRepository: Repository<InternalConversationMessage>,
  ) {}

  /**
   * Process LangGraph events and transform them into Redis stream events
   * Handles multi-message creation dynamically during streaming
   *
   * @param langGraphEvent - Raw LangGraph event
   * @param jobData - Flattened job data with all context
   * @param streamKey - Redis stream key for event publishing
   * @param currentMessageId - Current message ID (null if no active message)
   * @returns Object with newMessageId if a new message was created
   */
  async processLangGraphEvent(
    langGraphEvent: any,
    jobData: InAppJobData,
    streamKey: string,
    currentMessageId: string | null,
  ): Promise<{ newMessageId?: string | null; messageUpdated?: boolean }> {
    const eventType = langGraphEvent.event;

    this.logger.debug(`Processing LangGraph event: ${eventType}`, {
      eventType,
      currentMessageId,
      streamKey,
      runId: jobData.runId,
      conversationId: jobData.threadId,
    });

    try {
      // Use function map for cleaner event handling
      const handler = this.eventHandlers.get(eventType);

      if (handler) {
        return await handler(
          langGraphEvent,
          jobData,
          streamKey,
          currentMessageId,
        );
      } else {
        this.logger.debug(`Unhandled LangGraph event type: ${eventType}`);
        return {};
      }
    } catch (error) {
      this.logger.error(
        `Failed to process LangGraph event ${eventType}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Handle on_chat_model_start events
   * Creates new message and emits TEXT_MESSAGE_START event
   */
  private async handleChatModelStart(
    langGraphEvent: StreamEvent,
    jobData: InAppJobData,
    streamKey: string,
    _currentMessageId: string | null,
  ): Promise<{ newMessageId?: string | null; messageUpdated?: boolean }> {
    // Extract agentId from tags to determine which agent is starting
    const agentTag = langGraphEvent.tags?.find((t) => t.startsWith('agent:'));
    if (!agentTag) {
      this.logger.debug('No agent tag found in chat model start event');
      return {};
    }

    const tagParts = agentTag.split(':');
    if (tagParts.length < 2) {
      this.logger.warn('Invalid agent tag format in chat model start', { agentTag });
      return {};
    }

    const eventAgentId = tagParts[1];
    
    // Only create messages for the supervisor agent, not worker agents (subprocess)
    if (langGraphEvent.tags?.includes(ExclusiveTags.SUBPROCESS)) {
      this.logger.debug(`Skipping message creation for worker agent: ${eventAgentId}`);
      return {};
    }

    // Create new message for supervisor agent only
    const newMessageId = await this.createNewMessage(jobData);

    // Emit TEXT_MESSAGE_START event
    await this.publishEvent(streamKey, {
      type: StreamEventType.TEXT_MESSAGE_START,
      timestamp: Date.now(),
      messageId: newMessageId,
      isFromSubprocess:
        langGraphEvent.tags?.includes(ExclusiveTags.SUBPROCESS) || false,
    });

    this.logger.log(`New message created: ${newMessageId} for supervisor agent: ${eventAgentId}`);

    this.logger.debug(
      `Created new message ${newMessageId} and emitted TEXT_MESSAGE_START on chat model start for supervisor agent`,
    );
    return { newMessageId };
  }

  /**
   * Handle on_chat_model_stream events
   * Streams content to existing message (message should already exist from on_chat_model_start)
   */
  private async handleChatModelStream(
    langGraphEvent: StreamEvent,
    _jobData: InAppJobData,
    streamKey: string,
    currentMessageId: string | null,
  ): Promise<{ newMessageId?: string | null; messageUpdated?: boolean }> {
    // Stream content to existing message (message should already exist from on_chat_model_start)
    if (!currentMessageId) {
      this.logger.warn(
        'Received on_chat_model_stream but no current message ID - message should have been created in on_chat_model_start',
      );
      return {};
    }

    const textDelta = langGraphEvent.data?.chunk?.content;
    const toolCallArgsDelta = langGraphEvent.data?.chunk?.tool_call_chunks;

    if (textDelta) {
      await this.publishEvent(streamKey, {
        type: StreamEventType.TEXT_MESSAGE_CONTENT,
        timestamp: Date.now(),
        messageId: currentMessageId,
        delta: textDelta,
        isFromSubprocess:
          langGraphEvent.tags?.includes(ExclusiveTags.SUBPROCESS) || false,
      });

      this.logger.debug(
        `Streamed content to message ${currentMessageId}: "${textDelta.substring(0, 50)}${textDelta.length > 50 ? '...' : ''}"`,
      );
    } else if (toolCallArgsDelta?.length > 0) {
      await this.publishEvent(streamKey, {
        type: StreamEventType.TOOL_CALL_ARGS,
        timestamp: Date.now(),
        isFromSubprocess:
          langGraphEvent.tags?.includes(ExclusiveTags.SUBPROCESS) || false,
      });
    }

    return {};
  }

  /**
   * Handle on_chat_model_end events
   * Finalizes message with complete content and emits completion event
   */
  private async handleChatModelEnd(
    langGraphEvent: StreamEvent,
    _jobData: InAppJobData,
    streamKey: string,
    currentMessageId: string | null,
  ): Promise<{ newMessageId?: string | null; messageUpdated?: boolean }> {
    if (!currentMessageId) {
      this.logger.warn('Received on_chat_model_end but no current message ID');
      return {};
    }

    const finalContent = langGraphEvent.data?.output?.content || '';

    await this.messageRepository.update(currentMessageId, {
      text: finalContent,
    });

    // Emit TEXT_MESSAGE_END event
    await this.publishEvent(streamKey, {
      type: StreamEventType.TEXT_MESSAGE_END,
      timestamp: Date.now(),
      messageId: currentMessageId,
      finalContent: finalContent,
      isFromSubprocess:
        langGraphEvent.tags?.includes(ExclusiveTags.SUBPROCESS) || false,
    });

    this.logger.debug(
      `Finalized message ${currentMessageId} and emitted TEXT_MESSAGE_END`,
    );

    // Reset for next message
    return { newMessageId: null, messageUpdated: true };
  }

  /**
   * Handle on_tool_start events
   * Emits tool call start event with parent message ID
   */
  private async handleToolStart(
    langGraphEvent: StreamEvent,
    _jobData: InAppJobData,
    streamKey: string,
    _parentMessageId: string | null,
  ): Promise<{ newMessageId?: string | null; messageUpdated?: boolean }> {
    // ✅ Extract tool data based on actual LangGraph structure
    const toolName = langGraphEvent.name || 'unknown_tool';
    const toolArgs = langGraphEvent.data?.input || {};
    const toolId = langGraphEvent.run_id; // ✅ Use run_id for correlation (always available)

    await this.publishEvent(streamKey, {
      type: StreamEventType.TOOL_CALL_START,
      timestamp: Date.now(),
      toolName: toolName,
      toolId: toolId,
      toolArgs: toolArgs,
      isFromSubprocess:
        langGraphEvent.tags?.includes(ExclusiveTags.SUBPROCESS) || false,
    });

    this.logger.debug(
      `Emitted TOOL_CALL_START for tool: ${toolName} (${toolId})`,
    );
    return {};
  }

  /**
   * Handle on_tool_end events
   * Emits tool call end event with parent message ID
   */
  private async handleToolEnd(
    langGraphEvent: StreamEvent,
    _jobData: InAppJobData,
    streamKey: string,
    _parentMessageId: string | null,
  ): Promise<{ newMessageId?: string | null; messageUpdated?: boolean }> {
    // ✅ Extract tool data based on actual LangGraph structure
    const toolName = langGraphEvent.name || 'unknown_tool';
    const toolResult = langGraphEvent.data?.output || {};
    const toolId = langGraphEvent.run_id; // ✅ Use run_id for correlation (same as start event)

    await this.publishEvent(streamKey, {
      type: StreamEventType.TOOL_CALL_END,
      timestamp: Date.now(),
      toolName: toolName,
      toolId: toolId,
      toolResult: toolResult,
      isFromSubprocess:
        langGraphEvent.tags?.includes(ExclusiveTags.SUBPROCESS) || false,
    });

    this.logger.debug(
      `Emitted TOOL_CALL_END for tool: ${toolName} (${toolId})`,
    );
    return {};
  }

  // ============================================================================
  // RUN LIFECYCLE EVENT HANDLERS
  // ============================================================================

  /**
   * Handle run_started events
   * Emits RUN_STARTED event when run begins execution
   */
  private async handleRunStarted(
    langGraphEvent: StreamEvent,
    jobData: InAppJobData,
    streamKey: string,
    _currentMessageId: string | null,
  ): Promise<{ newMessageId?: string | null; messageUpdated?: boolean }> {
    await this.publishEvent(streamKey, {
      type: StreamEventType.RUN_STARTED,
      timestamp: Date.now(),
      runId: jobData.runId,
      isFromSubprocess:
        langGraphEvent.tags?.includes(ExclusiveTags.SUBPROCESS) || false,
    });

    this.logger.debug(`Emitted RUN_STARTED event for run: ${jobData.runId}`);
    return {};
  }

  /**
   * Handle run_complete events
   * Emits RUN_COMPLETE event when run completes successfully
   */
  private async handleRunComplete(
    langGraphEvent: any,
    jobData: InAppJobData,
    streamKey: string,
    _currentMessageId: string | null,
  ): Promise<{ newMessageId?: string | null; messageUpdated?: boolean }> {
    await this.publishEvent(streamKey, {
      type: StreamEventType.RUN_COMPLETE,
      timestamp: Date.now(),
      totalCost: langGraphEvent.totalCost || 0,
      isFromSubprocess:
        langGraphEvent.tags?.includes(ExclusiveTags.SUBPROCESS) || false,
    });

    this.logger.debug(`Emitted RUN_COMPLETE event for run: ${jobData.runId}`);
    return {};
  }

  /**
   * Handle run_cancelled events
   * Emits RUN_CANCELLED event when run is cancelled
   */
  private async handleRunCancelled(
    langGraphEvent: any,
    jobData: InAppJobData,
    streamKey: string,
    _currentMessageId: string | null,
  ): Promise<{ newMessageId?: string | null; messageUpdated?: boolean }> {
    await this.publishEvent(streamKey, {
      type: StreamEventType.RUN_CANCELLED,
      timestamp: Date.now(),

      reason: langGraphEvent.reason,
      initiatedBy: langGraphEvent.initiatedBy,
      isFromSubprocess:
        langGraphEvent.tags?.includes(ExclusiveTags.SUBPROCESS) || false,
    });

    this.logger.debug(`Emitted RUN_CANCELLED event for run: ${jobData.runId}`);
    return {};
  }

  private async handleChainStream(
    langGraphEvent: StreamEvent,
    jobData: InAppJobData,
    streamKey: string,
    _currentMessageId: string | null,
  ): Promise<{ newMessageId?: string | null; messageUpdated?: boolean }> {
    // check if this is interrupt event
    if (langGraphEvent.data?.chunk?.[1]?.['__interrupt__']) {
      this.logger.debug(
        `Emitted TOOL_CALL_INTERRUPT event for run: ${jobData.runId}`,
      );
      // Create tool call confirm message
      await this.createToolCallConfirmMessage(jobData);
      await this.publishEvent(streamKey, {
        type: StreamEventType.TOOL_CALL_INTERRUPT,
        timestamp: Date.now(),
        isFromSubprocess:
          langGraphEvent.tags?.includes(ExclusiveTags.SUBPROCESS) || false,
      });
    }
    return {};
  }

  private async createToolCallConfirmMessage(
    jobData: InAppJobData,
  ): Promise<void> {
    const internalMessage = this.messageRepository.create({
      text: '', // Empty content initially
      threadId: jobData.threadId,
      role: MessageRole.ASSISTANT,
      createdAt: `${Date.now()}`,
      // Set owner based on jobData.user (either userId or employeeId)
      userId: jobData.humanInfo.user?.userId || undefined, // 🔧 CHANGED FROM .id
      employeeId: jobData.humanInfo.employee?.employeeId || undefined,
      isToolCallConfirm: true,
    });
    await this.messageRepository.save(internalMessage);
  }

  /**
   * Handle run_error events
   * Emits RUN_ERROR event when run fails
   */
  private async handleRunError(
    langGraphEvent: any,
    jobData: InAppJobData,
    streamKey: string,
    _currentMessageId: string | null,
  ): Promise<{ newMessageId?: string | null; messageUpdated?: boolean }> {
    await this.publishEvent(streamKey, {
      type: StreamEventType.RUN_ERROR,
      timestamp: Date.now(),

      error: {
        type: langGraphEvent.error?.type || 'Error',
        message: langGraphEvent.error?.message || 'Unknown error',
        code: langGraphEvent.error?.code,
        stack: langGraphEvent.error?.stack,
      },
      isFromSubprocess:
        langGraphEvent.tags?.includes(ExclusiveTags.SUBPROCESS) || false,
    });

    this.logger.debug(`Emitted RUN_ERROR event for run: ${jobData.runId}`);
    return {};
  }

  /**
   *
   * @param jobData - Flattened job data with all context
   * @returns The created message ID
   */
  private async createNewMessage(jobData: InAppJobData): Promise<string> {
    const message = this.messageRepository.create({
      text: '',
      threadId: jobData.threadId,
      role: MessageRole.ASSISTANT,
      createdAt: `${Date.now()}`,
      userId: jobData.humanInfo.user?.userId || undefined,
      employeeId: jobData.humanInfo.employee?.employeeId || undefined,
    });

    const savedMessage = await this.messageRepository.save(message);
    const messageId = savedMessage.id;

    this.logger.debug(
      `Created new assistant message: ${messageId} for run: ${jobData.runId} in thread: ${jobData.threadId}`,
    );
    return messageId;
  }
  /**
   * Publish event to Redis stream with error handling
   * Uses infrastructure RedisService directly for stream operations
   * Simplified - no need for runId/threadId as UI already has context
   *
   * @param streamKey - Redis stream key
   * @param event - Stream event to publish
   */
  async publishEvent(
    streamKey: string,
    event: InternalStreamEvent,
  ): Promise<void> {
    try {
      // Use RedisService.xadd to publish event to stream
      await this.redisService.xadd(streamKey, {
        event: JSON.stringify(event),
      });

      this.logger.debug(`Published event ${event.type} to stream ${streamKey}`);
    } catch (streamingError) {
      this.logger.warn(
        `Redis streaming failed for event ${event.type}:`,
        streamingError,
      );
      // Don't throw - streaming failures shouldn't break the run
    }
  }
}
