import { Global, Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { QueueName } from './index';
import { BullBoardModule } from '@bull-board/nestjs';
import { ExpressAdapter } from '@bull-board/express';
import basicAuth from 'express-basic-auth';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';

function getQueueArray() {
  return Object.values(QueueName).map((name) => ({ name }));
}

function getQueueAdapterArray() {
  return Object.values(QueueName).map((name) => ({
    name,
    adapter: BullMQAdapter,
  }));
}

const queueArray = getQueueArray();
const queueAdapterArray = getQueueAdapterArray();

@Global()
@Module({
  imports: [
    BullModule.registerQueue({
      name: QueueName.EMAIL_SYSTEM,
    }),
    BullModule.registerQueue({
      name: QueueName.EMAIL_MARKETING,
    }),
    BullModule.registerQueue({
      name: QueueName.SMS,
    }),
    BullModule.registerQueue({
      name: QueueName.SMS_MARKETING,
    }),
    BullModule.registerQueue({
      name: QueueName.AFFILIATE_CLICK,
    }),
    BullModule.registerQueue({
      name: QueueName.FINE_TUNE,
    }),
    BullModule.registerQueue({
      name: QueueName.ZALO_WEBHOOK,
    }),
    // BullModule.registerQueue({
    //   name: QueueName.ZALO_AI_RESPONSE,
    // }),
    BullModule.registerQueue({
      name: QueueName.ZALO_ZNS,
    }),
    BullModule.registerQueue({
      name: QueueName.ZALO_VIDEO_TRACKING,
    }),
    BullModule.registerQueue({
      name: QueueName.INTEGRATION,
    }),
    BullModule.registerQueue({
      name: QueueName.ZALO_AUDIENCE_SYNC,
    }),
    BullModule.registerQueue({
      name: QueueName.WORKFLOW_EXECUTION,
    }),
    BullModule.registerQueue({
      name: QueueName.DATA_PROCESS,
    }),
    BullModule.registerQueue({
      name: QueueName.WEBHOOK,
    }),
    BullModule.registerQueue(...queueArray),
    BullBoardModule.forRoot({
      route: '/queues',
      adapter: ExpressAdapter as any,
      middleware: basicAuth({
        challenge: true,
        users: { admin: 'redai@123' },
      }),
    }),
    BullBoardModule.forFeature({
      name: QueueName.EMAIL_SYSTEM,
      adapter: BullMQAdapter,
    }),
    BullBoardModule.forFeature({
      name: QueueName.EMAIL_MARKETING,
      adapter: BullMQAdapter,
    }),
    BullBoardModule.forFeature({
      name: QueueName.SMS,
      adapter: BullMQAdapter,
    }),
    BullBoardModule.forFeature({
      name: QueueName.SMS_MARKETING,
      adapter: BullMQAdapter,
    }),
    BullBoardModule.forFeature({
      name: QueueName.AFFILIATE_CLICK,
      adapter: BullMQAdapter,
    }),
    BullBoardModule.forFeature({
      name: QueueName.FINE_TUNE,
      adapter: BullMQAdapter,
    }),
    BullBoardModule.forFeature({
      name: QueueName.ZALO_WEBHOOK,
      adapter: BullMQAdapter,
    }),
    // BullBoardModule.forFeature({
    //   name: QueueName.ZALO_AI_RESPONSE,
    //   adapter: BullMQAdapter,
    // }),
    BullBoardModule.forFeature({
      name: QueueName.ZALO_ZNS,
      adapter: BullMQAdapter,
    }),
    BullBoardModule.forFeature({
      name: QueueName.ZALO_VIDEO_TRACKING,
      adapter: BullMQAdapter,
    }),
    BullBoardModule.forFeature({
      name: QueueName.INTEGRATION,
      adapter: BullMQAdapter,
    }),
    BullBoardModule.forFeature({
      name: QueueName.DATA_PROCESS,
      adapter: BullMQAdapter,
    }),
    BullBoardModule.forFeature({
      name: QueueName.ZALO_AUDIENCE_SYNC,
      adapter: BullMQAdapter,
    }),
    BullBoardModule.forFeature({
      name: QueueName.WORKFLOW_EXECUTION,
      adapter: BullMQAdapter,
    }),
    BullBoardModule.forFeature({
      name: QueueName.WEBHOOK,
      adapter: BullMQAdapter,
    }),
    BullBoardModule.forFeature(...queueAdapterArray),
  ],
})
export class QueueModule { }
