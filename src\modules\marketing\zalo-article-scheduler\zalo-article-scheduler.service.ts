import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppException, ErrorCode } from '@common/exceptions';
import { ZaloArticleSchedulerJobData } from '../../../queue/queue.types';
import { ZaloService } from '../../../shared/services/zalo/zalo.service';

/**
 * Service xử lý lên lịch xuất bản bài viết Zalo
 */
@Injectable()
export class ZaloArticleSchedulerService {
  private readonly logger = new Logger(ZaloArticleSchedulerService.name);

  constructor(
    private readonly zaloService: ZaloService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Xuất bản bài viết đã lên lịch
   * @param jobData Dữ liệu job chứa thông tin bài viết
   */
  async publishScheduledArticle(jobData: ZaloArticleSchedulerJobData): Promise<void> {
    this.logger.log(
      `Bắt đầu xuất bản bài viết đã lên lịch: ${jobData.articleId} - Token: ${jobData.articleToken}`,
    );

    try {
      // 1. Lấy thông tin integration để có access token
      const integration = await this.getIntegration(jobData.integrationId);
      if (!integration) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          `Không tìm thấy integration với ID: ${jobData.integrationId}`,
        );
      }

      // 2. Lấy access token từ integration
      // TODO: Implement proper access token retrieval
      const accessToken = 'dummy_access_token'; // Placeholder
      if (!accessToken) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Không thể lấy access token từ integration',
        );
      }

      // 3. Cập nhật status bài viết trên Zalo từ 'hide' thành 'show'
      await this.updateZaloArticleStatus(accessToken, jobData.articleToken, 'show');

      // 4. Cập nhật status bài viết trong database
      await this.updateDatabaseArticleStatus(jobData.articleId, 'published');

      this.logger.log(
        `Đã xuất bản thành công bài viết: ${jobData.articleId} - Token: ${jobData.articleToken}`,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi xuất bản bài viết: ${jobData.articleId} - Error: ${error.message}`,
        error.stack,
      );

      // Cập nhật status thành failed trong database
      try {
        await this.updateDatabaseArticleStatus(jobData.articleId, 'failed');
      } catch (dbError) {
        this.logger.error(
          `Lỗi khi cập nhật status failed cho bài viết: ${jobData.articleId} - Error: ${dbError.message}`,
          dbError.stack,
        );
      }

      throw error;
    }
  }

  /**
   * Lấy thông tin integration từ database
   * @param integrationId ID của integration
   * @returns Thông tin integration
   */
  private async getIntegration(integrationId: string): Promise<any> {
    try {
      const query = `
        SELECT id, type_id, encrypted_config, metadata, status
        FROM integrations 
        WHERE id = $1 AND status = 'active'
      `;
      
      // TODO: Implement proper database query
      // const result = await this.databaseService.query(query, [integrationId]);
      // return result.rows[0] || null;
      return null; // Placeholder
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin integration: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.DATABASE_ERROR, 'Lỗi khi truy vấn integration');
    }
  }

  /**
   * Cập nhật status bài viết trên Zalo
   * @param accessToken Access token của Zalo OA
   * @param articleToken Token của bài viết
   * @param status Status mới ('show' hoặc 'hide')
   */
  private async updateZaloArticleStatus(
    accessToken: string,
    articleToken: string,
    status: 'show' | 'hide',
  ): Promise<void> {
    try {
      const baseApiUrl = this.configService.get<string>('ZALO_API_BASE_URL', 'https://openapi.zalo.me/v2.0/oa');
      
      const updateData = {
        token: articleToken,
        status: status,
      };

      await this.zaloService.post(
        `${baseApiUrl}/article/update`,
        accessToken,
        updateData,
      );

      this.logger.log(`Đã cập nhật status bài viết trên Zalo: ${articleToken} -> ${status}`);
    } catch (error) {
      this.logger.error(
        `Lỗi khi cập nhật status bài viết trên Zalo: ${articleToken} - Error: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi cập nhật bài viết trên Zalo: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật status bài viết trong database
   * @param articleId ID của bài viết
   * @param status Status mới
   */
  private async updateDatabaseArticleStatus(
    articleId: string,
    status: 'published' | 'failed',
  ): Promise<void> {
    try {
      const query = `
        UPDATE zalo_contents 
        SET status = $1, updated_at = $2
        WHERE id = $3
      `;
      
      // TODO: Implement proper database update
      // await this.databaseService.query(query, [status, Date.now(), articleId]);
      
      this.logger.log(`Đã cập nhật status bài viết trong database: ${articleId} -> ${status}`);
    } catch (error) {
      this.logger.error(
        `Lỗi khi cập nhật status bài viết trong database: ${articleId} - Error: ${error.message}`,
        error.stack,
      );
      throw new AppException(ErrorCode.DATABASE_ERROR, 'Lỗi khi cập nhật database');
    }
  }
}
