import { Readable } from 'stream';
import base64 from 'base64-stream';
import axios from 'axios';

export async function streamImageToBase64(
  imageUrl: string,
): Promise<{ base64String: string; mimeType: string }> {
  const response = await axios.get(imageUrl, {
    responseType: 'stream',
  });

  const mimeType =
    response.headers['content-type'] || 'application/octet-stream';
  const fileStream = response.data as Readable;

  const base64Encoder = new base64.Base64Encode();

  // Nối stream nguồn (download) vào stream chuyển đổi (encoder)
  const encodedStream = fileStream.pipe(base64Encoder);

  // 1. Convert the entire stream into an array of chunks.
  const chunks = await encodedStream.toArray();

  // 2. Join the array into the final string.
  const accumulatedBase64 = chunks.join('');

  return { base64String: accumulatedBase64, mimeType };
}
