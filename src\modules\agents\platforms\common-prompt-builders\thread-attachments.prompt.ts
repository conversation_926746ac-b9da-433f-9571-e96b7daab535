import { ThreadKnowledgeFileAttachments, ThreadMediaAttachments } from "src/modules/agents/interfaces";

/**
 * Thread Attachments Prompt Builder
 * Generates a compact XML representation of thread attachments for agent context
 * Uses attribute-heavy approach for minimal token usage
 */
export function buildThreadAttachmentsPrompt(
  threadMediaAttachments?: ThreadMediaAttachments,
  threadKnowledgeFileAttachments?: ThreadKnowledgeFileAttachments,
): string {
  const parts: string[] = [];

  // Check if we have any attachments at all
  const hasMedia = !!threadMediaAttachments?.length;
  const hasKnowledge = !!threadKnowledgeFileAttachments?.length;

  if (!hasMedia && !hasKnowledge) {
    return ''; // No attachments, return empty string
  }

  // Media attachments section
  if (hasMedia) {
    parts.push('<thread-media>');
    threadMediaAttachments!.forEach((media) => {
      const attributes: string[] = [
        `id="${media.id}"`,
        `name="${media.name}"`,
      ];

      // Optional description
      if (media.description) {
        attributes.push(`description="${media.description}"`);
      }

      // Optional tags (flatten to comma-separated string)
      if (media.tags && Object.keys(media.tags).length > 0) {
        const tagString = Object.keys(media.tags).join(',');
        attributes.push(`tags="${tagString}"`);
      }

      parts.push(`  <media ${attributes.join(' ')} />`);
    });
    parts.push('</thread-media>');
  }

  // Knowledge file attachments section
  if (hasKnowledge) {
    parts.push('<thread-knowledge>');
    threadKnowledgeFileAttachments!.forEach((knowledge) => {
      const attributes: string[] = [
        `id="${knowledge.id}"`,
        `name="${knowledge.name}"`,
        `file-id="${knowledge.fileId}"`,
      ];

      parts.push(`  <knowledge ${attributes.join(' ')} />`);
    });
    parts.push('</thread-knowledge>');
  }

  return parts.join('\n');
}
