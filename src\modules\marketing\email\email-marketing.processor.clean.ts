import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger, Injectable } from '@nestjs/common';
import { Job } from 'bullmq';
import * as nodemailer from 'nodemailer';

import { QueueName } from '../../../queue';
import {
  EmailMarketingJobDto,
  EmailMarketingJobName,
} from './dto';
import {
  AdminEmailCampaignJobDto,
  BatchAdminEmailCampaignJobDto,
} from './dto/admin-email-campaign-job.dto';
import {
  EmailTemplateService,
  EmailTrackingService,
} from './services';
import { GmailEmailService, GmailMessage, GmailCredentials } from './services/gmail-email.service';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AdminEmailCampaign } from '../entities/admin-email-campaign.entity';

import { env } from '../../../config/env';

/**
 * Processor xử lý queue email marketing - <PERSON><PERSON>n trúc mới: Individual jobs đã được process sẵn ở backend
 */
@Injectable()
@Processor(QueueName.EMAIL_MARKETING, { concurrency: 10 })
export class EmailMarketingProcessor extends WorkerHost {
  private readonly logger = new Logger(EmailMarketingProcessor.name);

  constructor(
    private readonly emailTemplateService: EmailTemplateService,
    private readonly emailTrackingService: EmailTrackingService,
    private readonly gmailEmailService: GmailEmailService,
    @InjectRepository(AdminEmailCampaign)
    private readonly adminEmailCampaignRepository: Repository<AdminEmailCampaign>,
  ) {
    super();
  }

  /**
   * Xử lý job gửi email marketing - Xử lý cả user và admin jobs
   * @param job Job từ queue
   */
  async process(job: Job<EmailMarketingJobDto | AdminEmailCampaignJobDto | BatchAdminEmailCampaignJobDto>): Promise<void> {
    // Xử lý các loại jobs khác nhau
    if (job.name === EmailMarketingJobName.SEND_EMAIL) {
      await this.processSingleEmail(job as Job<EmailMarketingJobDto>);
    } else if (job.name === EmailMarketingJobName.SEND_ADMIN_EMAIL) {
      await this.processAdminEmail(job as Job<AdminEmailCampaignJobDto>);
    } else if (job.name === EmailMarketingJobName.SEND_BATCH_ADMIN_EMAIL) {
      await this.processBatchAdminEmail(job as Job<BatchAdminEmailCampaignJobDto>);
    } else {
      this.logger.warn(`Unknown job type: ${job.name}`);
    }
  }

  /**
   * Xử lý job gửi admin email campaign (single email)
   * @param job Job từ queue
   */
  async processAdminEmail(job: Job<AdminEmailCampaignJobDto>): Promise<void> {
    const { campaignId, audience, email, subject, content, serverConfig, trackingId } = job.data;

    try {
      this.logger.log(`Processing admin email for campaign ${campaignId}, email: ${email}`);

      // Validate admin email server config
      if (!serverConfig || !serverConfig.host || !serverConfig.password) {
        throw new Error('Incomplete admin email server configuration: missing host or password');
      }

      if (!serverConfig.username && !serverConfig.user) {
        throw new Error('Incomplete admin email server configuration: missing username/user');
      }

      // Tạo transporter từ server config - Map từ backend format
      const transporterConfig = {
        host: serverConfig.host,
        port: serverConfig.port,
        secure: serverConfig.port === 465, // true for 465, false for other ports
        requireTLS: serverConfig.port === 587, // true for 587 (STARTTLS)
        auth: {
          user: serverConfig.user || serverConfig.username,
          pass: serverConfig.password,
        },
      };

      this.logger.debug(`Admin email transporter config: ${JSON.stringify({
        host: transporterConfig.host,
        port: transporterConfig.port,
        secure: transporterConfig.secure,
        requireTLS: transporterConfig.requireTLS,
        user: transporterConfig.auth.user
      })}`);

      const transporter = nodemailer.createTransport(transporterConfig);

      // Chuẩn bị email content
      const mailOptions = {
        from: serverConfig.from || serverConfig.senderEmail || serverConfig.username || serverConfig.user,
        to: email,
        subject: subject,
        html: content?.html,
        text: content?.text,
      };

      // Gửi email
      const result = await transporter.sendMail(mailOptions);

      this.logger.log(`Admin email sent successfully for campaign ${campaignId}, messageId: ${result.messageId}`);

      // Track email sent
      await this.trackAdminEmailSent(campaignId, audience, trackingId);

    } catch (error) {
      this.logger.error(`Failed to send admin email for campaign ${campaignId}:`, error);

      // Track email failed
      await this.trackAdminEmailFailed(campaignId, audience, trackingId, error.message);

      throw error;
    }
  }

  /**
   * Xử lý job gửi batch admin email campaign
   * @param job Job từ queue
   */
  async processBatchAdminEmail(job: Job<BatchAdminEmailCampaignJobDto>): Promise<void> {
    const { campaignId, recipients, subject, content, emailServerConfig } = job.data;

    try {
      this.logger.log(`Processing batch admin email for campaign ${campaignId}, recipients: ${recipients.length}`);

      if (!emailServerConfig) {
        throw new Error('Email server configuration is required');
      }

      // Validate admin email server config
      if (!emailServerConfig.host || !emailServerConfig.password) {
        throw new Error('Incomplete admin email server configuration: missing host or password');
      }

      if (!emailServerConfig.username && !emailServerConfig.user) {
        throw new Error('Incomplete admin email server configuration: missing username/user');
      }

      // Tạo transporter từ server config
      const transporterConfig = {
        host: emailServerConfig.host,
        port: emailServerConfig.port,
        secure: emailServerConfig.port === 465, // true for 465, false for other ports
        requireTLS: emailServerConfig.port === 587, // true for 587 (STARTTLS)
        auth: {
          user: emailServerConfig.username || emailServerConfig.user,
          pass: emailServerConfig.password,
        },
      };

      this.logger.debug(`Batch admin email transporter config: ${JSON.stringify({
        host: transporterConfig.host,
        port: transporterConfig.port,
        secure: transporterConfig.secure,
        requireTLS: transporterConfig.requireTLS,
        user: transporterConfig.auth.user
      })}`);

      const transporter = nodemailer.createTransport(transporterConfig);

      // Gửi email cho từng recipient
      for (const recipient of recipients) {
        try {
          const mailOptions = {
            from: emailServerConfig.from || emailServerConfig.senderEmail || emailServerConfig.username || emailServerConfig.user,
            to: recipient.email,
            subject: subject,
            html: content?.html,
            text: content?.text,
          };

          const result = await transporter.sendMail(mailOptions);
          this.logger.log(`Admin email sent to ${recipient.email}, messageId: ${result.messageId}`);

          // Track individual email success
          await this.trackAdminEmailSent(campaignId, { name: recipient.name, email: recipient.email }, recipient.trackingId);

        } catch (error) {
          this.logger.error(`Failed to send admin email to ${recipient.email}:`, error);

          // Track individual email failure
          await this.trackAdminEmailFailed(campaignId, { name: recipient.name, email: recipient.email }, recipient.trackingId, error.message);
        }
      }

      this.logger.log(`Batch admin email processing completed for campaign ${campaignId}`);

    } catch (error) {
      this.logger.error(`Failed to process batch admin email for campaign ${campaignId}:`, error);
      throw error;
    }
  }

  /**
   * Xử lý job gửi email đơn lẻ - Data đã được process sẵn ở backend
   * @param job Job từ queue
   */
  private async processSingleEmail(job: Job<EmailMarketingJobDto>): Promise<void> {
    const jobData = job.data;

    this.logger.log(
      `Processing single email job: ${job.id} for campaign ${jobData.campaignId} to ${jobData.email}`,
    );

    try {
      // Validate job data
      if (!this.validateJobData(jobData)) {
        throw new Error('Invalid job data');
      }

      // Data đã được process sẵn ở backend, chỉ cần thêm tracking
      // Inject link tracking vào content
      const contentWithLinkTracking =
        this.emailTemplateService.injectLinkTracking(
          jobData.content, // Đã process template variables
          jobData.trackingId,
          env.app.BASE_URL || 'http://localhost:3000',
        );

      // Inject tracking pixel vào content
      const contentWithTracking = this.emailTemplateService.injectTrackingPixel(
        contentWithLinkTracking,
        jobData.trackingId,
        env.app.BASE_URL || 'http://localhost:3000',
      );

      // Kiểm tra server type và gửi email tương ứng
      if (jobData.serverConfig.serverType === 'GMAIL') {
        // Gửi qua Gmail API
        await this.sendViaGmail(jobData, contentWithTracking);
      } else {
        // Gửi qua SMTP (default)
        await this.sendViaSmtp(jobData, contentWithTracking);
      }

      // ✅ Tracking sẽ được xử lý ở app main thông qua job completion callback
    } catch (error) {
      this.logger.error(
        `Single email job failed: ${job.id}, error: ${error.message}`,
      );

      // ✅ Tracking sẽ được xử lý ở app main thông qua job failure callback

      throw error; // Re-throw để Bull có thể retry
    }
  }

  /**
   * Validate job data
   * @param jobData Dữ liệu job
   * @returns True nếu valid
   */
  private validateJobData(jobData: EmailMarketingJobDto): boolean {
    if (!jobData.campaignId || !jobData.audience) {
      this.logger.error('Missing campaignId or audience');
      return false;
    }

    if (!jobData.email || !jobData.subject || !jobData.content) {
      this.logger.error('Missing email, subject, or content');
      return false;
    }

    // Validate server config - BẮT BUỘC cho email marketing
    if (!jobData.serverConfig) {
      this.logger.error('Missing server configuration for email marketing');
      return false;
    }

    if (
      !jobData.serverConfig.host ||
      !jobData.serverConfig.user ||
      !jobData.serverConfig.password
    ) {
      this.logger.error(
        'Incomplete server configuration: missing host, user, or password',
      );
      return false;
    }

    return true;
  }

  /**
   * Gửi email qua Gmail API
   * @param jobData Job data
   * @param content Nội dung email đã xử lý tracking
   */
  private async sendViaGmail(
    jobData: EmailMarketingJobDto,
    content: string,
  ): Promise<void> {
    try {
      const credentials: GmailCredentials = {
        accessToken: jobData.serverConfig.accessToken!,
        refreshToken: jobData.serverConfig.refreshToken,
      };

      const message: GmailMessage = {
        to: jobData.email,
        subject: jobData.subject,
        body: content,
        from: jobData.serverConfig.from,
        isHtml: true,
      };

      const result = await this.gmailEmailService.sendEmail(credentials, message);

      this.logger.debug(
        `Email sent successfully via Gmail API: ${result.messageId} to ${jobData.email}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to send email via Gmail API to ${jobData.email}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Gửi email qua SMTP
   * @param jobData Job data
   * @param content Nội dung email đã xử lý tracking
   */
  private async sendViaSmtp(
    jobData: EmailMarketingJobDto,
    content: string,
  ): Promise<void> {
    try {
      // Tạo email transporter từ server config đã decrypt
      const transporter = this.createTransporter(jobData.serverConfig);

      // Chuẩn bị email options
      const mailOptions = {
        from: jobData.serverConfig.from,
        to: jobData.email,
        subject: jobData.subject,
        html: content,
      };

      // Gửi email
      const info = await transporter.sendMail(mailOptions);

      this.logger.debug(
        `Email sent successfully via SMTP: ${info.messageId} to ${jobData.email}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to send email via SMTP to ${jobData.email}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Tạo email transporter từ server config đã decrypt
   * @param serverConfig Cấu hình server đã decrypt và validate
   * @returns Nodemailer transporter
   */
  private createTransporter(
    serverConfig: EmailMarketingJobDto['serverConfig'],
  ): nodemailer.Transporter {
    // Server config đã được decrypt và validate ở backend
    const config = {
      host: serverConfig.host,
      port: serverConfig.port,
      auth: {
        user: serverConfig.user,
        pass: serverConfig.password,
      },
    };

    return nodemailer.createTransport(config);
  }

  /**
   * Xử lý khi job failed
   * @param job Job bị failed
   * @param err Lỗi
   */
  async onFailed(job: Job<EmailMarketingJobDto>, err: Error): Promise<void> {
    this.logger.error(`Job ${job.id} failed: ${err.message}`, err.stack);
    // ✅ Tracking sẽ được xử lý ở app main thông qua job event listeners
  }

  /**
   * Xử lý khi job completed
   * @param job Job đã hoàn thành
   */
  async onCompleted(job: Job<EmailMarketingJobDto>): Promise<void> {
    const jobData = job.data;
    this.logger.debug(
      `Single email job ${job.id} completed for campaign ${jobData.campaignId}`,
    );
  }

  /**
   * Xử lý khi job active
   * @param job Job đang xử lý
   */
  async onActive(job: Job<EmailMarketingJobDto>): Promise<void> {
    const jobData = job.data;
    this.logger.debug(
      `Single email job ${job.id} started for campaign ${jobData.campaignId}`,
    );
  }

  /**
   * Track admin email sent successfully
   */
  private async trackAdminEmailSent(
    campaignId: number,
    audience: { name: string; email: string },
    trackingId: string,
  ): Promise<void> {
    try {
      // TODO: Implement admin email tracking
      // Có thể tạo AdminEmailTrackingService riêng hoặc extend EmailTrackingService
      this.logger.log(`Admin email tracked as sent: campaign=${campaignId}, email=${audience.email}, trackingId=${trackingId}`);

      // Update campaign status if needed
      // await this.adminEmailCampaignRepository.updateStatus(campaignId, AdminEmailCampaignStatus.SENDING, 0);
    } catch (error) {
      this.logger.error(`Failed to track admin email sent:`, error);
    }
  }

  /**
   * Track admin email failed
   */
  private async trackAdminEmailFailed(
    campaignId: number,
    audience: { name: string; email: string },
    trackingId: string,
    errorMessage: string,
  ): Promise<void> {
    try {
      // TODO: Implement admin email tracking
      this.logger.log(`Admin email tracked as failed: campaign=${campaignId}, email=${audience.email}, trackingId=${trackingId}, error=${errorMessage}`);

      // Update campaign status if needed
      // await this.adminEmailCampaignRepository.markAsFailed(campaignId, 0);
    } catch (error) {
      this.logger.error(`Failed to track admin email failure:`, error);
    }
  }
}
