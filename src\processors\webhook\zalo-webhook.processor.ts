import { Processor, WorkerHost, OnWorkerEvent } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { QueueName, WebhookJobName } from '../../queue';
import { ZaloWebhookJobName } from './zalo-job-names.enum';
import { UserAudienceRepository } from '../../modules/marketing/repositories/user-audience.repository';
import { ZaloOATokenAdapterService } from '../../shared/services/zalo/zalo-oa-token-adapter.service';
import { ZaloUserManagementService } from '../../shared/services/zalo/zalo-user-management.service';
// import { ZaloWebhookEventType } from 'redai-zalo-sdk'; // Tạm comment để tránh lỗi build

/**
 * Interface cho Zalo webhook job data - Updated for new queue system
 */
interface ZaloWebhookJobData {
  id: string;
  type: string; // ZaloQueueType
  jobName: string; // ZaloQueueJobName
  eventName: string;
  event: any; // ZaloWebhookEventUnion from SDK
  context: {
    oaId: string;
    userId?: string;
    integrationId?: string;
  };
  priority: number;
  retryCount: number;
  metadata: {
    oaId: string;
    userId?: string;
    integrationId?: string;
    timestamp: number;
    source: 'webhook' | 'retry' | 'manual';
    handlerName: string;
    originalEventId?: string;
    correlationId?: string;
  };
}

/**
 * Legacy interface for backward compatibility
 */
interface LegacyZaloWebhookJobData {
  zaloEvent: any;
  oaId: string;
  integrationId?: string;
  userId?: number;
  metadata?: {
    eventId?: string;
    timestamp: number;
  };
}

/**
 * Processor xử lý Zalo webhook events trong worker - Updated for new queue system
 */
@Processor(QueueName.WEBHOOK, {
  concurrency: 10, // Xử lý tối đa 10 jobs đồng thời
})
export class ZaloWebhookProcessor extends WorkerHost {
  private readonly logger = new Logger(ZaloWebhookProcessor.name);

  constructor(
    private readonly userAudienceRepository: UserAudienceRepository,
    private readonly zaloOATokenAdapterService: ZaloOATokenAdapterService,
    private readonly zaloUserManagementService: ZaloUserManagementService,
  ) {
    super();
  }

  /**
   * Xử lý Zalo webhook job - Support both new and legacy formats
   */
  async process(
    job: Job<ZaloWebhookJobData | LegacyZaloWebhookJobData>,
  ): Promise<any> {
    // Skip non-Zalo jobs immediately - chỉ xử lý Zalo jobs
    if (job.name !== WebhookJobName.PROCESS_ZALO_WEBHOOK) {
      this.logger.debug(
        `⏭️ Skipping non-Zalo job: ${job.name} (ID: ${job.id})`,
      );
      return; // Skip job này
    }

    // Check if this is a new format job
    if (this.isNewFormatJob(job.data)) {
      return this.processNewFormatJob(job as Job<ZaloWebhookJobData>);
    }

    // Handle legacy format
    if (job.name !== WebhookJobName.PROCESS_ZALO_WEBHOOK) {
      this.logger.debug(
        `⏭️ Skipping non-legacy Zalo job: ${job.name} (ID: ${job.id})`,
      );
      return; // Skip job này
    }

    return this.processLegacyFormatJob(job as Job<LegacyZaloWebhookJobData>);
  }

  /**
   * Check if job data is new format
   */
  private isNewFormatJob(data: any): data is ZaloWebhookJobData {
    return (
      data &&
      typeof data === 'object' &&
      'jobName' in data &&
      'eventName' in data &&
      'context' in data
    );
  }

  /**
   * Process new format job with queue system
   */
  private async processNewFormatJob(
    job: Job<ZaloWebhookJobData>,
  ): Promise<any> {
    const { event, context, metadata, jobName, eventName } = job.data;

    this.logger.log(
      `Processing NEW FORMAT Zalo webhook job ${job.id}: ${eventName} for OA ${context.oaId}, JobName: ${jobName}`,
    );

    try {
      // Route to appropriate processor based on job name
      const result = await this.routeToProcessor(jobName, event, context);

      this.logger.log(
        `Successfully processed NEW FORMAT job ${job.id}: ${eventName}, Handler: ${metadata.handlerName}`,
      );

      return {
        success: true,
        eventType: eventName,
        jobName,
        oaId: context.oaId,
        handlerName: metadata.handlerName,
        processingTime: Date.now() - metadata.timestamp,
        result,
      };
    } catch (error) {
      this.logger.error(
        `Failed to process NEW FORMAT job ${job.id}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Process legacy format job (backward compatibility)
   */
  private async processLegacyFormatJob(
    job: Job<LegacyZaloWebhookJobData>,
  ): Promise<any> {
    const { zaloEvent, oaId, integrationId, userId, metadata } = job.data;

    this.logger.debug(
      `Processing LEGACY FORMAT Zalo webhook job ${job.id}: ${zaloEvent.event_name} for OA ${oaId}`,
    );

    try {
      // Xử lý theo loại event (legacy method)
      const result = await this.processZaloEvent(
        zaloEvent,
        oaId,
        integrationId,
        userId,
      );

      this.logger.log(
        `Successfully processed LEGACY FORMAT job ${job.id}: ${zaloEvent.event_name}`,
      );

      return {
        success: true,
        eventType: zaloEvent.event_name,
        oaId,
        processingTime: Date.now() - (metadata?.timestamp || Date.now()),
        result,
        format: 'legacy',
      };
    } catch (error) {
      this.logger.error(
        `Failed to process LEGACY FORMAT job ${job.id}: ${error.message}`,
        error.stack,
      );

      throw error; // BullMQ sẽ retry job nếu có lỗi
    }
  }

  /**
   * Route to appropriate processor based on job name (NEW QUEUE SYSTEM)
   * @param jobName - Job name từ ZaloWebhookJobName enum
   * @param event - Zalo webhook event data
   * @param context - Context chứa oaId, userId, integrationId
   * @returns Promise với kết quả xử lý
   */
  private async routeToProcessor(
    jobName: string,
    event: any,
    context: any,
  ): Promise<any> {
    switch (jobName) {
      // =============================================================================
      // USER MESSAGE JOBS (High Priority)
      // =============================================================================
      case ZaloWebhookJobName.PROCESS_USER_TEXT_MESSAGE:
        return this.processUserTextMessageJob(event, context);

      case ZaloWebhookJobName.PROCESS_USER_MEDIA_MESSAGE:
        return this.processUserMediaMessageJob(event, context);

      case ZaloWebhookJobName.PROCESS_USER_SPECIAL_MESSAGE:
        return this.processUserSpecialMessageJob(event, context);

      case ZaloWebhookJobName.PROCESS_USER_BUSINESS_CARD:
        return this.processUserBusinessCardJob(event, context);

      // =============================================================================
      // USER GROUP MESSAGE JOBS (High Priority)
      // =============================================================================
      case ZaloWebhookJobName.PROCESS_USER_GROUP_TEXT:
        return this.processUserGroupTextJob(event, context);

      case ZaloWebhookJobName.PROCESS_USER_GROUP_MEDIA:
        return this.processUserGroupMediaJob(event, context);

      case ZaloWebhookJobName.PROCESS_USER_GROUP_SPECIAL:
        return this.processUserGroupSpecialJob(event, context);

      // =============================================================================
      // OA MESSAGE JOBS (Medium Priority)
      // =============================================================================
      case ZaloWebhookJobName.PROCESS_OA_DIRECT_MESSAGE:
        return this.processOADirectMessageJob(event, context);

      case ZaloWebhookJobName.PROCESS_OA_GROUP_MESSAGE:
        return this.processOAGroupMessageJob(event, context);

      // =============================================================================
      // ANONYMOUS MESSAGE JOBS (Medium Priority)
      // =============================================================================
      case ZaloWebhookJobName.PROCESS_ANONYMOUS_USER_MESSAGE:
        return this.processAnonymousUserMessageJob(event, context);

      case ZaloWebhookJobName.PROCESS_ANONYMOUS_OA_MESSAGE:
        return this.processAnonymousOAMessageJob(event, context);

      // =============================================================================
      // USER INTERACTION JOBS (High Priority)
      // =============================================================================
      case ZaloWebhookJobName.PROCESS_USER_INTERACTION:
        return this.processUserInteractionJob(event, context);

      case ZaloWebhookJobName.PROCESS_MESSAGE_STATUS:
        return this.processMessageStatusJob(event, context);

      case ZaloWebhookJobName.PROCESS_FOLLOW_EVENT:
        return this.processFollowEventJob(event, context);

      // =============================================================================
      // BUSINESS LOGIC JOBS (Medium Priority)
      // =============================================================================
      case ZaloWebhookJobName.PROCESS_ORDER:
        return this.processOrderJob(event, context);

      case ZaloWebhookJobName.PROCESS_USER_INFO:
        return this.processUserInfoJob(event, context);

      case ZaloWebhookJobName.PROCESS_FEEDBACK:
        return this.processFeedbackJob(event, context);

      case ZaloWebhookJobName.PROCESS_CALL_EVENT:
        return this.processCallEventJob(event, context);

      case ZaloWebhookJobName.PROCESS_CONSENT:
        return this.processConsentJob(event, context);

      case ZaloWebhookJobName.PROCESS_TEMPLATE_MESSAGE:
        return this.processTemplateMessageJob(event, context);

      // =============================================================================
      // GROUP MANAGEMENT JOBS (Medium Priority)
      // =============================================================================
      case ZaloWebhookJobName.PROCESS_GROUP_BASIC_MANAGEMENT:
        return this.processGroupBasicManagementJob(event, context);

      case ZaloWebhookJobName.PROCESS_GROUP_MEMBER_MANAGEMENT:
        return this.processGroupMemberManagementJob(event, context);

      case ZaloWebhookJobName.PROCESS_GROUP_ADMIN_MANAGEMENT:
        return this.processGroupAdminManagementJob(event, context);

      // =============================================================================
      // TEMPLATE & ZNS JOBS (Low Priority)
      // =============================================================================
      case ZaloWebhookJobName.PROCESS_TEMPLATE_CHANGES:
        return this.processTemplateChangesJob(event, context);

      case ZaloWebhookJobName.PROCESS_TEMPLATE_TAGS:
        return this.processTemplateTagsJob(event, context);

      case ZaloWebhookJobName.PROCESS_ZNS_DELIVERY:
        return this.processZNSDeliveryJob(event, context);

      // =============================================================================
      // JOURNEY & BUSINESS FLOW JOBS (Low Priority)
      // =============================================================================
      case ZaloWebhookJobName.PROCESS_JOURNEY_EVENT:
        return this.processJourneyEventJob(event, context);

      // =============================================================================
      // SYSTEM & WIDGET JOBS (Lowest Priority)
      // =============================================================================
      case ZaloWebhookJobName.PROCESS_WIDGET_EVENT:
        return this.processWidgetEventJob(event, context);

      case ZaloWebhookJobName.PROCESS_PERMISSION_EVENT:
        return this.processPermissionEventJob(event, context);

      // =============================================================================
      // ANALYTICS & TRACKING JOBS (Lowest Priority)
      // =============================================================================
      case ZaloWebhookJobName.PROCESS_USER_TAG:
        return this.processUserTagJob(event, context);

      // =============================================================================
      // LEGACY JOBS (Backward Compatibility)
      // =============================================================================
      case ZaloWebhookJobName.PROCESS_USER_MESSAGE:
        return this.processUserMessageJob(event, context);

      case ZaloWebhookJobName.TRACK_MESSAGE_STATUS:
        return this.trackMessageStatusJob(event, context);

      case ZaloWebhookJobName.TRACK_OA_MESSAGE:
        return this.trackOAMessageJob(event, context);

      case ZaloWebhookJobName.TRACK_INTERACTION:
        return this.trackInteractionJob(event, context);

      case ZaloWebhookJobName.PROCESS_TEMPLATE_EVENT:
        return this.processTemplateEventJob(event, context);

      case ZaloWebhookJobName.PROCESS_SYSTEM_EVENT:
        return this.processSystemEventJob(event, context);

      case ZaloWebhookJobName.PROCESS_GROUP_MANAGEMENT:
        return this.processGroupManagementJob(event, context);

      // Fallback to legacy processing
      default:
        this.logger.warn(
          `Unknown job name: ${jobName}, falling back to legacy processing`,
        );
        return this.processZaloEvent(
          event,
          context.oaId,
          context.integrationId,
          context.userId,
        );
    }
  }

  /**
   * Xử lý Zalo event theo loại
   */
  private async processZaloEvent(
    zaloEvent: any,
    oaId: string,
    integrationId?: string,
    userId?: number,
  ): Promise<any> {
    const eventType = zaloEvent.event_name;

    switch (eventType) {
      // User message events
      case 'user_send_text':
        return this.processUserSendText(zaloEvent, oaId);

      case 'user_send_image':
        return this.processUserSendImage(zaloEvent, oaId);

      case 'user_send_file':
        return this.processUserSendFile(zaloEvent, oaId);

      case 'user_send_sticker':
        return this.processUserSendSticker(zaloEvent, oaId);

      // Follow/Unfollow events
      case 'follow':
        return this.processUserFollow(zaloEvent, oaId);

      case 'unfollow':
        return this.processUserUnfollow(zaloEvent, oaId);

      // Message status events
      case 'user_received_message':
        return this.processUserReceivedMessage(zaloEvent, oaId);

      case 'user_seen_message':
        return this.processUserSeenMessage(zaloEvent, oaId);

      // Group events
      case 'create_group':
        return this.processCreateGroup(zaloEvent, oaId);

      case 'user_join_group':
        return this.processUserJoinGroup(zaloEvent, oaId);

      // ZNS events
      case 'user_received_zns_message':
        return this.processUserReceivedZNS(zaloEvent, oaId);

      case 'change_oa_daily_quota':
        return this.processQuotaChange(zaloEvent, oaId);

      // Default case
      default:
        this.logger.warn(`Unhandled Zalo event type: ${eventType}`);
        return this.processGenericEvent(zaloEvent, oaId);
    }
  }

  /**
   * Xử lý user send text event
   */
  private async processUserSendText(
    zaloEvent: any,
    oaId: string,
  ): Promise<any> {
    this.logger.debug(`Processing user_send_text for OA ${oaId}`);

    // TODO: Implement business logic
    // - Lưu message vào database
    // - Trigger AI response nếu cần
    // - Update conversation state
    // - Send analytics event

    return {
      action: 'user_send_text_processed',
      messageId: zaloEvent.message?.msg_id,
      text: zaloEvent.message?.text,
      senderId: zaloEvent.sender?.id,
    };
  }

  /**
   * Xử lý user send image event
   */
  private async processUserSendImage(
    zaloEvent: any,
    oaId: string,
  ): Promise<any> {
    this.logger.debug(`Processing user_send_image for OA ${oaId}`);

    // TODO: Implement business logic
    // - Download và lưu image
    // - Extract metadata
    // - Trigger image analysis nếu cần

    return {
      action: 'user_send_image_processed',
      messageId: zaloEvent.message?.msg_id,
      attachments: zaloEvent.message?.attachments,
      senderId: zaloEvent.sender?.id,
    };
  }

  /**
   * Xử lý user send file event
   */
  private async processUserSendFile(
    zaloEvent: any,
    oaId: string,
  ): Promise<any> {
    this.logger.debug(`Processing user_send_file for OA ${oaId}`);

    // TODO: Implement business logic
    // - Download và lưu file
    // - Virus scan
    // - Extract metadata

    return {
      action: 'user_send_file_processed',
      messageId: zaloEvent.message?.msg_id,
      attachments: zaloEvent.message?.attachments,
      senderId: zaloEvent.sender?.id,
    };
  }

  /**
   * Xử lý user send sticker event
   */
  private async processUserSendSticker(
    zaloEvent: any,
    oaId: string,
  ): Promise<any> {
    this.logger.debug(`Processing user_send_sticker for OA ${oaId}`);

    return {
      action: 'user_send_sticker_processed',
      messageId: zaloEvent.message?.msg_id,
      attachments: zaloEvent.message?.attachments,
      senderId: zaloEvent.sender?.id,
    };
  }

  /**
   * Xử lý user follow event
   */
  private async processUserFollow(zaloEvent: any, oaId: string): Promise<any> {
    this.logger.debug(`Processing follow for OA ${oaId}`);

    // TODO: Implement business logic
    // - Lưu follower vào database
    // - Send welcome message
    // - Add to marketing segments
    // - Update analytics

    return {
      action: 'user_follow_processed',
      followerId: zaloEvent.follower?.id,
      userIdByApp: zaloEvent.user_id_by_app,
      source: zaloEvent.source,
    };
  }

  /**
   * Xử lý user unfollow event
   */
  private async processUserUnfollow(
    zaloEvent: any,
    oaId: string,
  ): Promise<any> {
    this.logger.debug(`Processing unfollow for OA ${oaId}`);

    // TODO: Implement business logic
    // - Update follower status
    // - Remove from marketing segments
    // - Update analytics

    return {
      action: 'user_unfollow_processed',
      followerId: zaloEvent.follower?.id,
      userIdByApp: zaloEvent.user_id_by_app,
      source: zaloEvent.source,
    };
  }

  /**
   * Xử lý user received message event
   */
  private async processUserReceivedMessage(
    zaloEvent: any,
    oaId: string,
  ): Promise<any> {
    this.logger.debug(`Processing user_received_message for OA ${oaId}`);

    // TODO: Update message delivery status

    return {
      action: 'user_received_message_processed',
      messageId: zaloEvent.message?.msg_id,
    };
  }

  /**
   * Xử lý user seen message event
   */
  private async processUserSeenMessage(
    zaloEvent: any,
    oaId: string,
  ): Promise<any> {
    this.logger.debug(`Processing user_seen_message for OA ${oaId}`);

    // TODO: Update message read status

    return {
      action: 'user_seen_message_processed',
      messageIds: zaloEvent.message?.msg_ids,
    };
  }

  /**
   * Xử lý create group event
   */
  private async processCreateGroup(zaloEvent: any, oaId: string): Promise<any> {
    this.logger.debug(`Processing create_group for OA ${oaId}`);

    // TODO: Lưu group info vào database

    return {
      action: 'create_group_processed',
      groupId: zaloEvent.group_id,
    };
  }

  /**
   * Xử lý user join group event
   */
  private async processUserJoinGroup(
    zaloEvent: any,
    oaId: string,
  ): Promise<any> {
    this.logger.debug(`Processing user_join_group for OA ${oaId}`);

    // TODO: Update group membership

    return {
      action: 'user_join_group_processed',
      groupId: zaloEvent.group_id,
      users: zaloEvent.users,
    };
  }

  /**
   * Xử lý user received ZNS event
   */
  private async processUserReceivedZNS(
    zaloEvent: any,
    oaId: string,
  ): Promise<any> {
    this.logger.debug(`Processing user_received_zns_message for OA ${oaId}`);

    // TODO: Update ZNS delivery status

    return {
      action: 'user_received_zns_processed',
      messageId: zaloEvent.message?.msg_id,
      deliveryTime: zaloEvent.message?.delivery_time,
      trackingId: zaloEvent.message?.tracking_id,
    };
  }

  /**
   * Xử lý quota change event
   */
  private async processQuotaChange(zaloEvent: any, oaId: string): Promise<any> {
    this.logger.debug(`Processing change_oa_daily_quota for OA ${oaId}`);

    // TODO: Update quota info in database

    return {
      action: 'quota_change_processed',
      quota: zaloEvent.quota,
    };
  }

  /**
   * Xử lý generic event (fallback)
   */
  private async processGenericEvent(
    zaloEvent: any,
    oaId: string,
  ): Promise<any> {
    this.logger.debug(
      `Processing generic event ${zaloEvent.event_name} for OA ${oaId}`,
    );

    // TODO: Log event for analysis

    return {
      action: 'generic_event_processed',
      eventType: zaloEvent.event_name,
      data: zaloEvent,
    };
  }

  // =============================================================================
  // NEW QUEUE SYSTEM PROCESSORS
  // =============================================================================

  /**
   * Process user message jobs (Real-time priority)
   */
  private async processUserMessageJob(event: any, context: any): Promise<any> {
    const eventType = event.event_name;
    this.logger.log(
      `Processing user message job: ${eventType} for OA ${context.oaId}`,
    );

    switch (eventType) {
      case 'user_send_text':
        return this.processUserSendText(event, context.oaId);
      case 'user_send_image':
        return this.processUserSendImage(event, context.oaId);
      case 'user_send_audio':
        return this.processUserSendAudio(event, context.oaId);
      case 'user_send_video':
        return this.processUserSendVideo(event, context.oaId);
      case 'user_send_file':
        return this.processUserSendFile(event, context.oaId);
      case 'user_send_sticker':
        return this.processUserSendSticker(event, context.oaId);
      case 'user_send_gif':
        return this.processUserSendGif(event, context.oaId);
      case 'user_send_location':
        return this.processUserSendLocation(event, context.oaId);
      case 'user_send_link':
        return this.processUserSendLink(event, context.oaId);
      case 'user_send_business_card':
        return this.processUserSendBusinessCard(event, context.oaId);
      default:
        return this.processGenericEvent(event, context.oaId);
    }
  }

  /**
   * Process user interaction jobs (Real-time priority)
   */
  private async processUserInteractionJob(
    event: any,
    context: any,
  ): Promise<any> {
    const eventType = event.event_name;
    this.logger.log(
      `Processing user interaction job: ${eventType} for OA ${context.oaId}`,
    );

    switch (eventType) {
      case 'user_reacted_message':
        return this.processUserReactedMessage(event, context.oaId);
      case 'user_click_chatnow':
        return this.processUserClickChatNow(event, context.oaId);
      case 'oa_reacted_message':
        return this.processOAReactedMessage(event, context.oaId);
      default:
        return this.processGenericEvent(event, context.oaId);
    }
  }

  /**
   * Process follow event jobs (Real-time priority)
   */
  private async processFollowEventJob(event: any, context: any): Promise<any> {
    const eventType = event.event_name;
    this.logger.log(
      `Processing follow event job: ${eventType} for OA ${context.oaId}`,
    );

    switch (eventType) {
      case 'follow':
        return this.processUserFollow(event, context.oaId);
      case 'unfollow':
        return this.processUserUnfollow(event, context.oaId);
      default:
        return this.processGenericEvent(event, context.oaId);
    }
  }

  /**
   * Process order jobs (Business logic priority)
   */
  private async processOrderJob(event: any, context: any): Promise<any> {
    this.logger.log(
      `Processing order job: ${event.event_name} for OA ${context.oaId}`,
    );

    // TODO: Implement order processing logic
    // - Process order details
    // - Update inventory
    // - Send confirmations
    // - Update customer data

    return {
      action: 'order_job_processed',
      eventType: event.event_name,
      customerId: event.customer_id,
      oaId: context.oaId,
    };
  }

  /**
   * Process user info jobs (Business logic priority)
   */
  private async processUserInfoJob(event: any, context: any): Promise<any> {
    this.logger.log(
      `Processing user info job: ${event.event_name} for OA ${context.oaId}`,
    );

    try {
      // Route to specific handler based on event type
      switch (event.event_name) {
        case 'user_submit_info':
          return await this.handleUserSubmitInfo(event, context);

        case 'update_user_info':
          return await this.handleUpdateUserInfo(event, context);

        default:
          this.logger.warn(`Unhandled user info event: ${event.event_name}`);
          return {
            action: 'user_info_job_processed',
            eventType: event.event_name,
            userIdByApp: event.user_id_by_app,
            oaId: context.oaId,
            status: 'unhandled',
          };
      }
    } catch (error) {
      this.logger.error(
        `Error processing user info job: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Process feedback jobs (Business logic priority)
   */
  private async processFeedbackJob(event: any, context: any): Promise<any> {
    this.logger.log(
      `Processing feedback job: ${event.event_name} for OA ${context.oaId}`,
    );

    // TODO: Implement feedback processing logic
    // - Save feedback
    // - Update satisfaction metrics
    // - Notify customer service

    return {
      action: 'feedback_job_processed',
      eventType: event.event_name,
      rate: event.rate,
      note: event.note,
      oaId: context.oaId,
    };
  }

  /**
   * Process call event jobs (Business logic priority)
   */
  private async processCallEventJob(event: any, context: any): Promise<any> {
    this.logger.log(
      `Processing call event job: ${event.event_name} for OA ${context.oaId}`,
    );

    // TODO: Implement call event processing logic
    // - Log call details
    // - Update call analytics
    // - Route to agents

    return {
      action: 'call_event_job_processed',
      eventType: event.event_name,
      callId: event.call_id,
      phone: event.phone,
      oaId: context.oaId,
    };
  }

  /**
   * Process consent jobs (Business logic priority)
   */
  private async processConsentJob(event: any, context: any): Promise<any> {
    this.logger.log(
      `Processing consent job: ${event.event_name} for OA ${context.oaId}`,
    );

    // TODO: Implement consent processing logic
    // - Update consent status
    // - Grant/revoke permissions
    // - Compliance tracking

    return {
      action: 'consent_job_processed',
      eventType: event.event_name,
      phone: event.phone,
      oaId: context.oaId,
    };
  }

  /**
   * Track message status jobs (Analytics priority)
   */
  private async trackMessageStatusJob(event: any, context: any): Promise<any> {
    this.logger.debug(
      `Tracking message status job: ${event.event_name} for OA ${context.oaId}`,
    );

    // TODO: Implement message status tracking
    // - Update delivery status
    // - Calculate read rates
    // - Analytics

    return {
      action: 'message_status_tracked',
      eventType: event.event_name,
      messageId: event.message?.msg_id,
      oaId: context.oaId,
    };
  }

  /**
   * Track OA message jobs (Analytics priority)
   */
  private async trackOAMessageJob(event: any, context: any): Promise<any> {
    this.logger.debug(
      `Tracking OA message job: ${event.event_name} for OA ${context.oaId}`,
    );

    // TODO: Implement OA message tracking
    // - Track outbound messages
    // - Update metrics
    // - Billing tracking

    return {
      action: 'oa_message_tracked',
      eventType: event.event_name,
      messageId: event.message?.msg_id,
      oaId: context.oaId,
    };
  }

  /**
   * Track interaction jobs (Analytics priority)
   */
  private async trackInteractionJob(event: any, context: any): Promise<any> {
    this.logger.debug(
      `Tracking interaction job: ${event.event_name} for OA ${context.oaId}`,
    );

    // TODO: Implement interaction tracking
    // - Track user interactions
    // - Update engagement metrics
    // - Analytics

    return {
      action: 'interaction_tracked',
      eventType: event.event_name,
      oaId: context.oaId,
    };
  }

  /**
   * Process template event jobs (Background priority)
   */
  private async processTemplateEventJob(
    event: any,
    context: any,
  ): Promise<any> {
    this.logger.debug(
      `Processing template event job: ${event.event_name} for OA ${context.oaId}`,
    );

    // TODO: Implement template event processing
    // - Update template status
    // - Sync with Zalo
    // - Notify administrators

    return {
      action: 'template_event_processed',
      eventType: event.event_name,
      oaId: context.oaId,
    };
  }

  /**
   * Process system event jobs (Background priority)
   */
  private async processSystemEventJob(event: any, context: any): Promise<any> {
    this.logger.debug(
      `Processing system event job: ${event.event_name} for OA ${context.oaId}`,
    );

    // TODO: Implement system event processing
    // - Update system status
    // - Handle permissions
    // - Widget management

    return {
      action: 'system_event_processed',
      eventType: event.event_name,
      oaId: context.oaId,
    };
  }

  /**
   * Process group management jobs (Background priority)
   */
  private async processGroupManagementJob(
    event: any,
    context: any,
  ): Promise<any> {
    this.logger.debug(
      `Processing group management job: ${event.event_name} for OA ${context.oaId}`,
    );

    // TODO: Implement group management processing
    // - Update group status
    // - Manage members
    // - Group analytics

    return {
      action: 'group_management_processed',
      eventType: event.event_name,
      groupId: event.group_id,
      oaId: context.oaId,
    };
  }

  // =============================================================================
  // NEW EVENT PROCESSORS (Missing from legacy)
  // =============================================================================

  private async processUserSendAudio(event: any, oaId: string): Promise<any> {
    this.logger.debug(`Processing user_send_audio for OA ${oaId}`);

    // TODO: Implement audio processing
    // - Download và lưu audio
    // - Speech-to-text conversion
    // - Audio analysis

    return {
      action: 'user_send_audio_processed',
      messageId: event.message?.msg_id,
      attachments: event.message?.attachments,
      senderId: event.sender?.id,
    };
  }

  private async processUserSendVideo(event: any, oaId: string): Promise<any> {
    this.logger.debug(`Processing user_send_video for OA ${oaId}`);

    // TODO: Implement video processing
    // - Download và lưu video
    // - Extract metadata
    // - Video processing/transcoding

    return {
      action: 'user_send_video_processed',
      messageId: event.message?.msg_id,
      attachments: event.message?.attachments,
      senderId: event.sender?.id,
    };
  }

  private async processUserSendGif(event: any, oaId: string): Promise<any> {
    this.logger.debug(`Processing user_send_gif for OA ${oaId}`);

    return {
      action: 'user_send_gif_processed',
      messageId: event.message?.msg_id,
      attachments: event.message?.attachments,
      senderId: event.sender?.id,
    };
  }

  private async processUserSendLocation(
    event: any,
    oaId: string,
  ): Promise<any> {
    this.logger.debug(`Processing user_send_location for OA ${oaId}`);

    return {
      action: 'user_send_location_processed',
      messageId: event.message?.msg_id,
      coordinates: event.message?.coordinates,
      senderId: event.sender?.id,
    };
  }

  private async processUserSendLink(event: any, oaId: string): Promise<any> {
    this.logger.debug(`Processing user_send_link for OA ${oaId}`);

    return {
      action: 'user_send_link_processed',
      messageId: event.message?.msg_id,
      links: event.message?.links,
      senderId: event.sender?.id,
    };
  }

  private async processUserSendBusinessCard(
    event: any,
    oaId: string,
  ): Promise<any> {
    this.logger.debug(`Processing user_send_business_card for OA ${oaId}`);

    return {
      action: 'user_send_business_card_processed',
      messageId: event.message?.msg_id,
      businessCard: event.message?.business_card,
      senderId: event.sender?.id,
    };
  }

  private async processUserReactedMessage(
    event: any,
    oaId: string,
  ): Promise<any> {
    this.logger.debug(`Processing user_reacted_message for OA ${oaId}`);

    return {
      action: 'user_reacted_message_processed',
      messageId: event.message?.msg_id,
      reaction: event.message?.react_icon,
      senderId: event.sender?.id,
    };
  }

  private async processUserClickChatNow(
    event: any,
    oaId: string,
  ): Promise<any> {
    this.logger.debug(`Processing user_click_chatnow for OA ${oaId}`);

    return {
      action: 'user_click_chatnow_processed',
      userIdByApp: event.user_id_by_app,
      oaId: event.oa_id,
    };
  }

  private async processOAReactedMessage(
    event: any,
    oaId: string,
  ): Promise<any> {
    this.logger.debug(`Processing oa_reacted_message for OA ${oaId}`);

    return {
      action: 'oa_reacted_message_processed',
      messageId: event.message?.msg_id,
      reaction: event.message?.react_icon,
      senderId: event.sender?.id,
    };
  }

  // =============================================================================
  // NEW HANDLER METHODS FOR DETAILED EVENT PROCESSING
  // =============================================================================

  /**
   * Process user text messages (user_send_text)
   */
  private async processUserTextMessageJob(
    event: any,
    context: any,
  ): Promise<any> {
    this.logger.log(
      `Processing user text message: ${event.event_name} for OA ${context.oaId}`,
    );

    return {
      action: 'user_text_message_processed',
      eventType: event.event_name,
      messageId: event.message?.msg_id,
      text: event.message?.text,
      senderId: event.sender?.id,
      oaId: context.oaId,
      timestamp: Date.now(),
    };
  }

  /**
   * Process user media messages (image, video, audio, gif, file)
   */
  private async processUserMediaMessageJob(
    event: any,
    context: any,
  ): Promise<any> {
    this.logger.log(
      `Processing user media message: ${event.event_name} for OA ${context.oaId}`,
    );

    const attachment = event.message?.attachments?.[0];

    return {
      action: 'user_media_message_processed',
      eventType: event.event_name,
      messageId: event.message?.msg_id,
      mediaType: attachment?.type,
      mediaUrl: attachment?.payload?.url,
      senderId: event.sender?.id,
      oaId: context.oaId,
      timestamp: Date.now(),
    };
  }

  /**
   * Process user special messages (location, link, sticker)
   */
  private async processUserSpecialMessageJob(
    event: any,
    context: any,
  ): Promise<any> {
    this.logger.log(
      `Processing user special message: ${event.event_name} for OA ${context.oaId}`,
    );

    const attachment = event.message?.attachments?.[0];

    return {
      action: 'user_special_message_processed',
      eventType: event.event_name,
      messageId: event.message?.msg_id,
      specialType: attachment?.type,
      payload: attachment?.payload,
      senderId: event.sender?.id,
      oaId: context.oaId,
      timestamp: Date.now(),
    };
  }

  /**
   * Process user business card (user_send_business_card)
   */
  private async processUserBusinessCardJob(
    event: any,
    context: any,
  ): Promise<any> {
    this.logger.log(
      `Processing user business card: ${event.event_name} for OA ${context.oaId}`,
    );

    return {
      action: 'user_business_card_processed',
      eventType: event.event_name,
      messageId: event.message?.msg_id,
      businessCard: event.message?.attachments?.[0]?.payload,
      senderId: event.sender?.id,
      oaId: context.oaId,
      timestamp: Date.now(),
    };
  }

  /**
   * Process user group text messages (user_send_group_text)
   */
  private async processUserGroupTextJob(
    event: any,
    context: any,
  ): Promise<any> {
    this.logger.log(
      `Processing user group text: ${event.event_name} for OA ${context.oaId}`,
    );

    return {
      action: 'user_group_text_processed',
      eventType: event.event_name,
      messageId: event.message?.msg_id,
      text: event.message?.text,
      senderId: event.sender?.id,
      groupId: event.group_id,
      oaId: context.oaId,
      timestamp: Date.now(),
    };
  }

  /**
   * Process user group media messages
   */
  private async processUserGroupMediaJob(
    event: any,
    context: any,
  ): Promise<any> {
    this.logger.log(
      `Processing user group media: ${event.event_name} for OA ${context.oaId}`,
    );

    const attachment = event.message?.attachments?.[0];

    return {
      action: 'user_group_media_processed',
      eventType: event.event_name,
      messageId: event.message?.msg_id,
      mediaType: attachment?.type,
      mediaUrl: attachment?.payload?.url,
      senderId: event.sender?.id,
      groupId: event.group_id,
      oaId: context.oaId,
      timestamp: Date.now(),
    };
  }

  /**
   * Process user group special messages
   */
  private async processUserGroupSpecialJob(
    event: any,
    context: any,
  ): Promise<any> {
    this.logger.log(
      `Processing user group special: ${event.event_name} for OA ${context.oaId}`,
    );

    const attachment = event.message?.attachments?.[0];

    return {
      action: 'user_group_special_processed',
      eventType: event.event_name,
      messageId: event.message?.msg_id,
      specialType: attachment?.type,
      payload: attachment?.payload,
      senderId: event.sender?.id,
      groupId: event.group_id,
      oaId: context.oaId,
      timestamp: Date.now(),
    };
  }

  /**
   * Process OA direct messages
   */
  private async processOADirectMessageJob(
    event: any,
    context: any,
  ): Promise<any> {
    this.logger.log(
      `Processing OA direct message: ${event.event_name} for OA ${context.oaId}`,
    );

    return {
      action: 'oa_direct_message_processed',
      eventType: event.event_name,
      messageId: event.message?.msg_id,
      text: event.message?.text,
      recipientId: event.recipient?.id,
      oaId: context.oaId,
      timestamp: Date.now(),
    };
  }

  /**
   * Process OA group messages
   */
  private async processOAGroupMessageJob(
    event: any,
    context: any,
  ): Promise<any> {
    this.logger.log(
      `Processing OA group message: ${event.event_name} for OA ${context.oaId}`,
    );

    return {
      action: 'oa_group_message_processed',
      eventType: event.event_name,
      messageId: event.message?.msg_id,
      text: event.message?.text,
      recipientId: event.recipient?.id,
      groupId: event.group_id,
      oaId: context.oaId,
      timestamp: Date.now(),
    };
  }

  /**
   * Process anonymous user messages
   */
  private async processAnonymousUserMessageJob(
    event: any,
    context: any,
  ): Promise<any> {
    this.logger.log(
      `Processing anonymous user message: ${event.event_name} for OA ${context.oaId}`,
    );

    return {
      action: 'anonymous_user_message_processed',
      eventType: event.event_name,
      messageId: event.message?.msg_id,
      text: event.message?.text,
      conversationId: event.message?.conversation_id,
      senderId: event.sender?.id,
      oaId: context.oaId,
      timestamp: Date.now(),
    };
  }

  /**
   * Process anonymous OA messages
   */
  private async processAnonymousOAMessageJob(
    event: any,
    context: any,
  ): Promise<any> {
    this.logger.log(
      `Processing anonymous OA message: ${event.event_name} for OA ${context.oaId}`,
    );

    return {
      action: 'anonymous_oa_message_processed',
      eventType: event.event_name,
      messageId: event.message?.msg_id,
      text: event.message?.text,
      conversationId: event.message?.conversation_id,
      recipientId: event.recipient?.id,
      oaId: context.oaId,
      timestamp: Date.now(),
    };
  }

  /**
   * Process message status events (received, seen)
   */
  private async processMessageStatusJob(
    event: any,
    context: any,
  ): Promise<any> {
    this.logger.log(
      `Processing message status: ${event.event_name} for OA ${context.oaId}`,
    );

    return {
      action: 'message_status_processed',
      eventType: event.event_name,
      messageId: event.message?.msg_id,
      messageIds: event.message?.msg_ids,
      senderId: event.sender?.id,
      oaId: context.oaId,
      timestamp: Date.now(),
    };
  }

  /**
   * Process template messages (oa_send_template)
   */
  private async processTemplateMessageJob(
    event: any,
    context: any,
  ): Promise<any> {
    this.logger.log(
      `Processing template message: ${event.event_name} for OA ${context.oaId}`,
    );

    return {
      action: 'template_message_processed',
      eventType: event.event_name,
      messageId: event.message?.msg_id,
      templatePayload: event.message?.payload,
      recipientId: event.recipient?.id,
      oaId: context.oaId,
      timestamp: Date.now(),
    };
  }

  // =============================================================================
  // GROUP MANAGEMENT HANDLER METHODS
  // =============================================================================

  /**
   * Process group basic management (create, update, disperse)
   */
  private async processGroupBasicManagementJob(
    event: any,
    context: any,
  ): Promise<any> {
    this.logger.log(
      `Processing group basic management: ${event.event_name} for OA ${context.oaId}`,
    );

    return {
      action: 'group_basic_management_processed',
      eventType: event.event_name,
      groupId: event.group_id,
      oaId: context.oaId,
      timestamp: Date.now(),
    };
  }

  /**
   * Process group member management (join, request, approve, reject, leave)
   */
  private async processGroupMemberManagementJob(
    event: any,
    context: any,
  ): Promise<any> {
    this.logger.log(
      `Processing group member management: ${event.event_name} for OA ${context.oaId}`,
    );

    return {
      action: 'group_member_management_processed',
      eventType: event.event_name,
      groupId: event.group_id,
      users: event.users,
      oaId: context.oaId,
      timestamp: Date.now(),
    };
  }

  /**
   * Process group admin management (add, remove admin)
   */
  private async processGroupAdminManagementJob(
    event: any,
    context: any,
  ): Promise<any> {
    this.logger.log(
      `Processing group admin management: ${event.event_name} for OA ${context.oaId}`,
    );

    return {
      action: 'group_admin_management_processed',
      eventType: event.event_name,
      groupId: event.group_id,
      users: event.users,
      oaId: context.oaId,
      timestamp: Date.now(),
    };
  }

  // =============================================================================
  // TEMPLATE & ZNS HANDLER METHODS
  // =============================================================================

  /**
   * Process template changes (quota, quality, status)
   */
  private async processTemplateChangesJob(
    event: any,
    context: any,
  ): Promise<any> {
    this.logger.log(
      `Processing template changes: ${event.event_name} for OA ${context.oaId}`,
    );

    return {
      action: 'template_changes_processed',
      eventType: event.event_name,
      templateId: event.template_id,
      quota: event.quota,
      quality: event.quality,
      status: event.status,
      reason: event.reason,
      oaId: context.oaId,
      timestamp: Date.now(),
    };
  }

  /**
   * Process template tags changes
   */
  private async processTemplateTagsJob(event: any, context: any): Promise<any> {
    this.logger.log(
      `Processing template tags: ${event.event_name} for OA ${context.oaId}`,
    );

    return {
      action: 'template_tags_processed',
      eventType: event.event_name,
      tagLevel: event.tag_level,
      oaId: context.oaId,
      timestamp: Date.now(),
    };
  }

  /**
   * Process ZNS delivery events
   */
  private async processZNSDeliveryJob(event: any, context: any): Promise<any> {
    this.logger.log(
      `Processing ZNS delivery: ${event.event_name} for OA ${context.oaId}`,
    );

    return {
      action: 'zns_delivery_processed',
      eventType: event.event_name,
      messageId: event.message?.msg_id,
      deliveryTime: event.message?.delivery_time,
      trackingId: event.message?.tracking_id,
      oaId: context.oaId,
      timestamp: Date.now(),
    };
  }

  // =============================================================================
  // JOURNEY & BUSINESS FLOW HANDLER METHODS
  // =============================================================================

  /**
   * Process journey events (timeout, acknowledged)
   */
  private async processJourneyEventJob(event: any, context: any): Promise<any> {
    this.logger.log(
      `Processing journey event: ${event.event_name} for OA ${context.oaId}`,
    );

    return {
      action: 'journey_event_processed',
      eventType: event.event_name,
      journeyId: event.journey_id,
      messageId: event.msg_id,
      oaId: context.oaId,
      timestamp: Date.now(),
    };
  }

  // =============================================================================
  // SYSTEM & WIDGET HANDLER METHODS
  // =============================================================================

  /**
   * Process widget events (interaction accepted, sync failed)
   */
  private async processWidgetEventJob(event: any, context: any): Promise<any> {
    this.logger.log(
      `Processing widget event: ${event.event_name} for OA ${context.oaId}`,
    );

    return {
      action: 'widget_event_processed',
      eventType: event.event_name,
      data: event.data,
      oaId: context.oaId,
      timestamp: Date.now(),
    };
  }

  /**
   * Process permission events (revoked, extension purchased)
   */
  private async processPermissionEventJob(
    event: any,
    context: any,
  ): Promise<any> {
    this.logger.log(
      `Processing permission event: ${event.event_name} for OA ${context.oaId}`,
    );

    return {
      action: 'permission_event_processed',
      eventType: event.event_name,
      extensionId: event.extension_id,
      extensionSubInfo: event.extension_sub_info,
      data: event.data,
      oaId: context.oaId,
      timestamp: Date.now(),
    };
  }

  // =============================================================================
  // ANALYTICS & TRACKING HANDLER METHODS
  // =============================================================================

  /**
   * Process user tag events (add user to tag)
   */
  private async processUserTagJob(event: any, context: any): Promise<any> {
    this.logger.log(
      `Processing user tag: ${event.event_name} for OA ${context.oaId}`,
    );

    return {
      action: 'user_tag_processed',
      eventType: event.event_name,
      userIdByApp: event.user_id_by_app,
      oaId: context.oaId,
      timestamp: Date.now(),
    };
  }

  // =============================================================================
  // USER INFO HANDLER METHODS
  // =============================================================================

  /**
   * Handle user submit info event
   */
  private async handleUserSubmitInfo(event: any, context: any): Promise<any> {
    this.logger.log(
      `Handling user_submit_info for user ${event.sender?.id} in OA ${context.oaId}`,
    );

    try {
      // Extract data from webhook event
      const userIdByApp = event.sender?.id;
      const oaId = context.oaId;
      const submittedInfo = event.info || {};

      if (!userIdByApp || !oaId) {
        throw new Error('Missing required fields: userIdByApp or oaId');
      }

      // 1. Find integration and get userId + access token
      const integration = await this.zaloOATokenAdapterService.findOne({
        where: { oaId },
      });

      if (!integration) {
        throw new Error(`Integration not found for OA ${oaId}`);
      }

      const userId = integration.userId;
      if (!userId) {
        throw new Error(`UserId not found in integration for OA ${oaId}`);
      }

      // 2. Get access token
      const tokens = await this.zaloOATokenAdapterService.getDecryptedTokens(
        integration.id,
      );
      const accessToken = tokens.accessToken;

      // 3. Get detailed user info from Zalo API
      let zaloUserDetail: any = null;
      try {
        zaloUserDetail = await this.zaloUserManagementService.getUserDetail(
          accessToken,
          userIdByApp,
        );
        this.logger.debug(`Retrieved Zalo user detail:`, zaloUserDetail);
      } catch (apiError) {
        this.logger.warn(
          `Failed to get user detail from Zalo API: ${apiError.message}`,
        );
        // Continue without API data
      }

      // 4. Find existing audience by zaloSocialId
      let audience = await this.userAudienceRepository.findByZaloSocialId(
        userIdByApp,
        userId,
      );

      const now = Date.now();

      if (audience) {
        // Update existing audience
        this.logger.log(
          `Updating existing audience ${audience.id} for user ${userIdByApp}`,
        );

        // Merge submitted info with existing data
        audience.name =
          submittedInfo.name || zaloUserDetail?.display_name || audience.name;
        audience.phoneNumber =
          submittedInfo.phone ||
          zaloUserDetail?.shared_info?.phone ||
          audience.phoneNumber;
        // audience.address =
        //   this.buildFullAddress(submittedInfo) || audience.address;

        // Update from Zalo API if available
        if (zaloUserDetail) {
          audience.zaloUserIsFollower =
            zaloUserDetail.user_is_follower ?? audience.zaloUserIsFollower;
          audience.avatarsExternal = this.extractZaloAvatars(zaloUserDetail);

          if (zaloUserDetail.user_last_interaction_date) {
            audience.userLastInteractionDate = this.convertZaloDateToTimestamp(
              zaloUserDetail.user_last_interaction_date,
            );
          }
        }

        audience.updatedAt = now;
        audience = await this.userAudienceRepository.save(audience);

        return {
          action: 'user_submit_info_processed',
          eventType: event.event_name,
          userIdByApp,
          oaId,
          audienceId: audience.id,
          status: 'updated',
          submittedInfo,
          timestamp: now,
        };
      } else {
        // Create new audience
        this.logger.log(`Creating new audience for user ${userIdByApp}`);

        const newAudienceData = {
          userId,
          name:
            submittedInfo.name || zaloUserDetail?.display_name || 'Zalo User',
          phoneNumber:
            submittedInfo.phone || zaloUserDetail?.shared_info?.phone || null,
          address: this.buildFullAddress(submittedInfo) || null,
          zaloSocialId: userIdByApp,
          integrationId: integration.id,
          avatarsExternal: zaloUserDetail
            ? this.extractZaloAvatars(zaloUserDetail)
            : null,
          importResource: 'ZALO',
          zaloUserIsFollower: zaloUserDetail?.user_is_follower ?? null,
          userLastInteractionDate: zaloUserDetail?.user_last_interaction_date
            ? this.convertZaloDateToTimestamp(
                zaloUserDetail.user_last_interaction_date,
              )
            : null,
          createdAt: now,
          updatedAt: now,
        };

        audience =
          await this.userAudienceRepository.createAudience(newAudienceData);

        return {
          action: 'user_submit_info_processed',
          eventType: event.event_name,
          userIdByApp,
          oaId,
          audienceId: audience.id,
          status: 'created',
          submittedInfo,
          timestamp: now,
        };
      }
    } catch (error) {
      this.logger.error(
        `Error handling user_submit_info: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Handle update user info event
   */
  private async handleUpdateUserInfo(event: any, context: any): Promise<any> {
    this.logger.log(
      `Handling update_user_info for user ${event.sender?.id} in OA ${context.oaId}`,
    );

    // Similar logic to handleUserSubmitInfo but for update events
    // For now, delegate to the same handler
    return await this.handleUserSubmitInfo(event, context);
  }

  /**
   * Build full address from submitted info
   */
  private buildFullAddress(submittedInfo: any): string | null {
    if (!submittedInfo) return null;

    const parts = [
      submittedInfo.address,
      submittedInfo.ward,
      submittedInfo.district,
      submittedInfo.city,
    ].filter((part) => part && part.trim());

    return parts.length > 0 ? parts.join(', ') : null;
  }

  /**
   * Extract Zalo avatars from user detail
   */
  private extractZaloAvatars(userDetail: any): string[] | null {
    const avatars: string[] = [];

    if (userDetail.avatar) {
      avatars.push(userDetail.avatar);
    }

    if (
      userDetail.avatars_external &&
      Array.isArray(userDetail.avatars_external)
    ) {
      avatars.push(...userDetail.avatars_external);
    }

    return avatars.length > 0 ? avatars : null;
  }

  /**
   * Convert Zalo date format (dd/MM/yyyy) to timestamp
   */
  private convertZaloDateToTimestamp(dateStr: string): number | null {
    if (!dateStr) return null;

    try {
      const [day, month, year] = dateStr.split('/');
      const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
      return Math.floor(date.getTime() / 1000) * 1000; // Convert to milliseconds
    } catch (error) {
      this.logger.warn(`Failed to convert Zalo date: ${dateStr}`, error);
      return null;
    }
  }

  /**
   * Event handlers
   */
  @OnWorkerEvent('completed')
  onCompleted(job: Job<ZaloWebhookJobData | LegacyZaloWebhookJobData>) {
    this.logger.debug(`Zalo webhook job ${job.id} completed`);
  }

  @OnWorkerEvent('failed')
  onFailed(
    job: Job<ZaloWebhookJobData | LegacyZaloWebhookJobData>,
    error: Error,
  ) {
    this.logger.error(
      `Zalo webhook job ${job.id} failed: ${error.message}`,
      error.stack,
    );
  }

  @OnWorkerEvent('progress')
  onProgress(
    job: Job<ZaloWebhookJobData | LegacyZaloWebhookJobData>,
    progress: number,
  ) {
    this.logger.debug(`Zalo webhook job ${job.id} progress: ${progress}%`);
  }
}
