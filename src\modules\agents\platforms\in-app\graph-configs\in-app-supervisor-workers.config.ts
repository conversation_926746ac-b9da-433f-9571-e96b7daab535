import { SupervisorWorkersGraphConfigType } from 'src/modules/agents/graphs/multi-agents-graphs/supervisor-workers.graph';
import { Platform } from 'src/modules/agents/enums';
import { EmployeeInfo, UserInfo } from 'src/modules/agents/interfaces';

export interface InAppSupervisorWorkersConfig
  extends SupervisorWorkersGraphConfigType {
  platform: Platform.IN_APP;
  currentUser: {
    user?: UserInfo | undefined;
    employee?: EmployeeInfo | undefined;
  };
}
