import { SupervisorWorkersGraphConfigType } from '../../../shared/graphs/supervisor-workers.graph';
import { Platform } from '../../../shared/enums';
import { EmployeeInfo, UserInfo } from '../../../shared/interfaces';

export interface InAppSupervisorWorkersConfig
  extends SupervisorWorkersGraphConfigType {
  platform: Platform.IN_APP;
  currentUser: {
    user?: UserInfo | undefined;
    employee?: EmployeeInfo | undefined;
  };
}
