import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AIMessage } from '@langchain/core/messages';
import { SupervisorWorkersGraph } from '../../../graphs/multi-agents-graphs/supervisor-workers.graph';
import { TokenUsageCollector } from '../../../utils';
import { UserBillingService } from '../../../services';
import { RunStatusService } from '../../../../../shared/services';
import { RunStatusType } from '../../../../../shared/run-status';
import { RedisService } from 'src/infra/redis';
import { InternalMessageOperationsService } from './internal-message-operations.service';
import { InternalConversationMessage } from '../../../entities/internals';
import { MessageRole } from '../../../enums';
import { InAppJobData } from '../interfaces';

@Injectable()
export class InAppPostProcessingService {
  private readonly logger = new Logger(InAppPostProcessingService.name);

  constructor(
    private readonly userBilling: UserBillingService,
    private readonly redisService: RedisService,
    private readonly messageOperations: InternalMessageOperationsService,
    private readonly supervisorWorkersGraph: SupervisorWorkersGraph,
    private readonly runStatusService: RunStatusService,
    @InjectRepository(InternalConversationMessage)
    private readonly messageRepo: Repository<InternalConversationMessage>,
  ) {}

  /**
   * Perform all post-stream operations
   * Always executes regardless of success/failure/cancellation
   */
  async performPostStreamOperations(
    jobData: InAppJobData,
    tokenUsageCollector: TokenUsageCollector,
    currentMessageId: string | null,
  ): Promise<void> {
    const usageSummary = tokenUsageCollector.getUsageSummary();
    this.logger.debug(
      `Starting post-stream operations for run: ${jobData.runId}`,
      {
        runId: jobData.runId,
        currentMessageId,
        totalAccumulatedTextLength: usageSummary.totalAccumulatedTextLength,
        accumulatedTextByAgent: usageSummary.accumulatedTextByAgent,
      },
    );

    // 1. Process token usage and update user balance
    await this.processTokenUsageWithErrorHandling(jobData, tokenUsageCollector);

    // 2. Finalize partially accumulated text from supervisor
    await this.finalizePartiallyAccumulatedText(
      jobData,
      tokenUsageCollector,
      currentMessageId,
    );

    // 3. Clean up Redis keys and temporary data
    await this.cleanupTemporaryData(jobData);

    this.logger.debug(
      `Completed post-stream operations for run: ${jobData.runId}`,
    );
  }

  /**
   * Process token usage with error handling - billing failures shouldn't crash
   */
  async processTokenUsageWithErrorHandling(
    jobData: InAppJobData,
    tokenUsageCollector: TokenUsageCollector,
  ): Promise<void> {
    try {
      await this.userBilling.processTokenUsage(
        jobData.runId,
        tokenUsageCollector,
        { userId: jobData?.humanInfo?.user?.userId },
        jobData.platform,
      );
      this.logger.debug(
        `Successfully processed token usage for run: ${jobData.runId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to process token usage for run ${jobData.runId}:`,
        {
          runId: jobData.runId,
          userId: jobData?.humanInfo?.user?.userId,
          error: error.message,
          stack: error.stack,
        },
      );
      // Don't throw - billing failure shouldn't fail the run
    }
  }

  /**
   * Finalize partially accumulated text from supervisor agent
   * Updates the current message with any accumulated text that wasn't finalized during streaming
   */
  async finalizePartiallyAccumulatedText(
    jobData: InAppJobData,
    tokenUsageCollector: TokenUsageCollector,
    currentMessageId: string | null,
  ): Promise<void> {
    if (!currentMessageId) {
      this.logger.debug(
        `No current message ID to finalize for run: ${jobData.runId}`,
      );
      return;
    }

    try {
      // Get accumulated text from supervisor agent only
      const supervisorAccumulatedText = tokenUsageCollector.getAccumulatedText(
        jobData.mainAgentId,
      );

      this.logger.debug(
        `Finalizing partially accumulated text for message ${currentMessageId}`,
        {
          runId: jobData.runId,
          messageId: currentMessageId,
          agentId: jobData.mainAgentId,
          textLength: supervisorAccumulatedText.length,
          textPreview: supervisorAccumulatedText,
        },
      );

      if (
        !supervisorAccumulatedText ||
        supervisorAccumulatedText.trim().length === 0
      ) {
        this.logger.debug(
          `No accumulated text from supervisor agent ${jobData.mainAgentId} for run: ${jobData.runId}. Will be cleaned up in finally block.`,
          {
            messageId: currentMessageId,
            runId: jobData.runId,
            agentId: jobData.mainAgentId,
          },
        );
        return;
      }

      // Update the message with the accumulated text using MessageOperationsService
      const updateSuccess =
        await this.messageOperations.updateMessageTextWithValidation(
          currentMessageId,
          supervisorAccumulatedText,
          {
            runId: jobData.runId,
            agentId: jobData.mainAgentId,
            operation: 'finalize_partial_text',
          },
        );

      if (!updateSuccess) {
        this.logger.warn(
          `Failed to update message ${currentMessageId} with accumulated text`,
          {
            runId: jobData.runId,
            messageId: currentMessageId,
            agentId: jobData.mainAgentId,
            textLength: supervisorAccumulatedText.length,
          },
        );
        return;
      }

      // update langgraph state with finalized message
      const aiMessage = new AIMessage({
        content: supervisorAccumulatedText.trim(),
        id: currentMessageId,
      });

      const workflow = this.supervisorWorkersGraph.getWorkflow();

      await workflow.updateState(
        {
          configurable: {
            thread_id: `${jobData.platform}:${jobData.threadId}`,
          },
        },
        {
          messages: [aiMessage],
        },
      );

      this.logger.debug(
        `Successfully finalized partially accumulated text for message ${currentMessageId}`,
        {
          runId: jobData.runId,
          messageId: currentMessageId,
          agentId: jobData.mainAgentId,
          textLength: supervisorAccumulatedText.length,
          textPreview:
            supervisorAccumulatedText.substring(0, 100) +
            (supervisorAccumulatedText.length > 100 ? '...' : ''),
        },
      );

      // Clear the accumulated text for this agent after finalizing
      tokenUsageCollector.clearAccumulatedText(jobData.mainAgentId);
    } catch (error) {
      this.logger.error(
        `Failed to finalize accumulated text for message ${currentMessageId}:`,
        {
          runId: jobData.runId,
          messageId: currentMessageId,
          agentId: jobData.mainAgentId,
          error: error.message,
          stack: error.stack,
        },
      );
      // Don't throw - text finalization failure shouldn't fail the run
    }
  }

  /**
   * Clean up empty assistant messages in the thread
   * Removes assistant messages that have empty text, no attachments, and are not tool call confirmations
   */
  async cleanupEmptyAssistantMessages(threadId: string): Promise<number> {
    try {
      // Find all empty assistant messages in this thread
      const emptyMessages = await this.messageRepo.find({
        where: {
          threadId: threadId,
          role: MessageRole.ASSISTANT,
          text: '', // Empty text
          hasAttachments: false, // No attachments
          isToolCallConfirm: false, // Not a tool call confirmation
        },
        select: ['id'], // Only need IDs for deletion
      });

      if (emptyMessages.length === 0) {
        this.logger.debug(
          `No empty assistant messages found in thread ${threadId}`,
        );
        return 0;
      }

      // Delete all empty messages
      const messageIds = emptyMessages.map((msg) => msg.id);
      const deleteResult = await this.messageRepo.delete(messageIds);

      this.logger.debug(
        `Cleaned up ${emptyMessages.length} empty assistant messages in thread ${threadId}`,
        {
          threadId,
          deletedMessageIds: messageIds,
          deleteResult: deleteResult.affected,
        },
      );

      return emptyMessages.length;
    } catch (error) {
      this.logger.error(
        `Failed to cleanup empty assistant messages in thread ${threadId}:`,
        {
          threadId,
          error: error.message,
          stack: error.stack,
        },
      );
      return 0; // Don't throw - cleanup failure shouldn't fail the run
    }
  }

  /**
   * 🔧 UPDATED: Now uses RunStatusService for status management and cleanup
   * Clean up temporary Redis keys and data
   * Only cleans up keys that are ACTUALLY used in the codebase
   */
  async cleanupTemporaryData(jobData: InAppJobData): Promise<void> {
    const { runStatusKey, streamKey, platformThreadId } = jobData.keys;
    const threadId = jobData.threadId;

    try {
      // ✅ NEW: Check current run status before cleanup
      const currentStatus =
        await this.runStatusService.getRunStatus(platformThreadId);

      if (currentStatus) {
        this.logger.debug('Current run status before cleanup', {
          threadId,
          runId: jobData.runId,
          status: currentStatus.status,
          runStatusKey,
        });

        // ✅ NEW: Force complete active runs if needed (safety net)
        if (currentStatus.status === RunStatusType.ACTIVE) {
          this.logger.warn('Force completing active run during cleanup', {
            threadId,
            runId: jobData.runId,
          });
          await this.runStatusService.completeRun(
            platformThreadId,
            jobData.runId,
            {
              forcedCompletion: true,
              cleanupPhase: true,
            },
          );
        }
        try {
          await this.runStatusService.clearRunStatus(platformThreadId);
          this.logger.debug('Delayed run status cleanup completed', {
            threadId,
            runId: jobData.runId,
          });
        } catch (error) {
          this.logger.error(
            'Failed to clear run status during delayed cleanup',
            {
              threadId,
              error: error.message,
            },
          );
        }
      }

      // 2. Set TTL on stream key (don't delete - frontend may still be reading)
      const ttlSeconds = 10 * 60; // 10 minutes
      await this.redisService.expire(streamKey, ttlSeconds);

      // 3. Clean up empty assistant messages in this thread
      const deletedMessageCount =
        await this.cleanupEmptyAssistantMessages(threadId);

      this.logger.debug(`Cleaned up temporary data for run: ${jobData.runId}`, {
        runId: jobData.runId,
        threadId,
        streamKey,
        runStatusKey,
        ttlSeconds,
        deletedEmptyMessages: deletedMessageCount,
        currentStatus: currentStatus?.status,
      });
    } catch (error) {
      this.logger.error(
        `Failed to cleanup temporary data for run ${jobData.runId}:`,
        {
          runId: jobData.runId,
          threadId,
          streamKey,
          runStatusKey,
          error: error.message,
          stack: error.stack,
        },
      );
    }
  }
}
