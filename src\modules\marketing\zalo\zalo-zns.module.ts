import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { ConfigModule } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';
import { ScheduleModule } from '@nestjs/schedule';
import { TypeOrmModule } from '@nestjs/typeorm';
import { QueueName } from '../../../queue';
import { ZaloZnsProcessor } from './zalo-zns.processor';
import { ZaloTokenRefresherService } from './zalo-token-refresher.service';
import { CampaignStatusUpdateService } from '../../../shared/services/campaign-status-update.service';
import { ZaloModule } from '../../../shared/services/zalo/zalo.module';
import { ZaloZnsMessage } from '../entities/zalo-zns-message.entity';

/**
 * Module cho Zalo ZNS Worker
 * Xử lý các job gửi tin nhắn Zalo <PERSON>NS
 */
@Module({
  imports: [
    ConfigModule,
    TypeOrmModule.forFeature([ZaloZnsMessage]),
    BullModule.registerQueue({
      name: QueueName.ZALO_ZNS,
    }),
    HttpModule.register({
      timeout: 30000,
      maxRedirects: 5,
    }),
    ScheduleModule.forRoot(),
    ZaloModule,
  ],
  providers: [
    ZaloZnsProcessor,
    ZaloTokenRefresherService,
    CampaignStatusUpdateService,
  ],
  exports: [
    ZaloZnsProcessor,
    ZaloTokenRefresherService,
    CampaignStatusUpdateService,
  ],
})
export class ZaloZnsModule {}
