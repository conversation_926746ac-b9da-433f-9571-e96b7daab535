/**
 * Redis Event Types cho Workflow System
 * Định nghĩa các event types được publish qua Redis
 */

/**
 * Base interface cho tất cả Redis events
 */
export interface BaseRedisEvent {
  type: string;
  executionId: string;
  userId: number;
  timestamp: number;
}

// ===== WORKFLOW LIFECYCLE EVENTS =====

/**
 * Enhanced Base interface cho workflow lifecycle events
 */
export interface BaseWorkflowLifecycleEvent extends BaseRedisEvent {
  workflowId: string;
  timestampISO: string;
}

/**
 * Enhanced Base interface cho node lifecycle events
 */
export interface BaseNodeLifecycleEvent extends BaseWorkflowLifecycleEvent {
  nodeId: string;
}

/**
 * Node Started Event - khi node bắt đầu thực thi
 */
export interface NodeStartedLifecycleEvent extends BaseNodeLifecycleEvent {
  type: 'NODE_STARTED';
  data?: any;
  context?: Record<string, any>;
}

/**
 * Node Processing Event - khi node đang xử lý
 */
export interface NodeProcessingLifecycleEvent extends BaseNodeLifecycleEvent {
  type: 'NODE_PROCESSING';
  status: 'processing';
  data?: any;
}

/**
 * Node Completed Event - khi node hoàn thành
 */
export interface NodeCompletedLifecycleEvent extends BaseNodeLifecycleEvent {
  type: 'NODE_COMPLETED';
  result?: any;
  executionTime?: number;
  status: 'completed';
}

/**
 * Node Failed Event - khi node thất bại
 */
export interface NodeFailedLifecycleEvent extends BaseNodeLifecycleEvent {
  type: 'NODE_FAILED';
  error: {
    message: string;
    code: string;
    stack?: string;
  };
  status: 'failed';
}

/**
 * Workflow Started Event - khi workflow bắt đầu
 */
export interface WorkflowStartedLifecycleEvent extends BaseWorkflowLifecycleEvent {
  type: 'WORKFLOW_STARTED';
  startNode?: string;
  isPartialExecution?: boolean;
  data?: any;
}

/**
 * Workflow Completed Event - khi workflow hoàn thành
 */
export interface WorkflowCompletedLifecycleEvent extends BaseWorkflowLifecycleEvent {
  type: 'WORKFLOW_COMPLETED';
  result: any;
  startNode?: string;
  totalNodes: number;
  executionTime: number;
  isPartialExecution?: boolean;
  status: 'completed';
}

/**
 * Workflow Failed Event - khi workflow thất bại
 */
export interface WorkflowFailedLifecycleEvent extends BaseWorkflowLifecycleEvent {
  type: 'WORKFLOW_FAILED';
  error: {
    message: string;
    code: string;
    stack?: string;
  };
  startNode?: string;
  isPartialExecution?: boolean;
  status: 'failed';
}

/**
 * Union type cho node lifecycle events
 */
export type NodeLifecycleEvent =
  | NodeStartedLifecycleEvent
  | NodeProcessingLifecycleEvent
  | NodeCompletedLifecycleEvent
  | NodeFailedLifecycleEvent;

/**
 * Union type cho workflow lifecycle events
 */
export type WorkflowLifecycleEvent =
  | WorkflowStartedLifecycleEvent
  | WorkflowCompletedLifecycleEvent
  | WorkflowFailedLifecycleEvent;

/**
 * Union type cho tất cả lifecycle events
 */
export type LifecycleEvent = NodeLifecycleEvent | WorkflowLifecycleEvent;

/**
 * Union type cho tất cả Redis events
 */
export type RedisWorkflowEvent = LifecycleEvent;

/**
 * TTL values
 */
export const REDIS_TTL = {
  TEST_DATA: 3600, // 1 hour
  EXECUTION_DATA: 86400, // 24 hours
  PROGRESS_DATA: 1800, // 30 minutes
  SUMMARY_DATA: 7200, // 2 hours
} as const;