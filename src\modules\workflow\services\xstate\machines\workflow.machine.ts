import { assign, createMachine } from 'xstate';
import {
  DetailedNodeExecutionResult,
  WorkflowContext
} from '../types';
import { nodeExecutionMachine } from './node-execution.machine';
import {
  IConditionalDependencyGraph,
  IEnhancedConnection
} from '../../../interfaces/conditional-connection.interface';
import { conditionalWorkflowActions, conditionalWorkflowGuards } from './conditional-actions';

/**
 * Workflow machine states
 */
export type WorkflowState =
  | 'idle'
  | 'loading'
  | 'validating'
  | 'ready'
  | 'executing'
  | 'paused'
  | 'completed'
  | 'failed'
  | 'cancelled'
  | 'cleanup';

/**
 * Workflow machine events
 */
export type WorkflowMachineEvent =
  | { type: 'LOAD_WORKFLOW'; workflowId: string; executionId: string; triggerData: any }
  | { type: 'START_EXECUTION'; triggerData?: any }
  | { type: 'PAUSE_EXECUTION'; reason?: string }
  | { type: 'RESUME_EXECUTION' }
  | { type: 'CANCEL_EXECUTION'; reason?: string }
  | { type: 'RETRY_WORKFLOW'; fromCheckpoint?: string }
  | { type: 'NODE_COMPLETED'; nodeId: string; result: DetailedNodeExecutionResult }
  | { type: 'NODE_FAILED'; nodeId: string; error: Error }
  | { type: 'WORKFLOW_COMPLETED'; finalData: any }
  | { type: 'WORKFLOW_FAILED'; error: Error }
  | { type: 'VALIDATION_FAILED'; errors: string[] }
  | { type: 'DEPENDENCIES_UPDATED' }
  | { type: 'TIMEOUT' }
  | { type: 'CLEANUP_COMPLETED' };

/**
 * Main workflow state machine
 */
export const workflowMachine = createMachine({
  id: 'workflow',
  initial: 'idle',
  context: {} as WorkflowContext,
  types: {
    context: {} as WorkflowContext,
    events: {} as WorkflowMachineEvent,
  },

  states: {
    idle: {
      on: {
        LOAD_WORKFLOW: {
          target: 'loading',
          actions: assign({
            workflowId: ({ event }) => event.workflowId,
            executionId: ({ event }) => event.executionId,
            triggerData: ({ event }) => event.triggerData,
            metadata: ({ context, event }) => ({
              ...context.metadata,
              startTime: Date.now(),
              triggerType: 'manual' as const,
              userId: context.metadata?.userId || 0,
              retryCount: 0,
              totalNodes: 0,
              completedNodes: 0,
              failedNodes: 0,
            }),
          }),
        },
      },
    },

    loading: {
      invoke: {
        id: 'loadWorkflow',
        src: 'loadWorkflowService',
        input: ({ context }) => ({
          workflowId: context.workflowId,
          executionId: context.executionId,
        }),
        onDone: {
          target: 'validating',
          actions: assign({
            nodes: ({ event }) => {
              if ('output' in event && event.output && typeof event.output === 'object' && 'nodes' in event.output) {
                return event.output.nodes;
              }
              return new Map();
            },
            connections: ({ event }) => {
              if ('output' in event && event.output && typeof event.output === 'object' && 'connections' in event.output) {
                return event.output.connections;
              }
              return [];
            },
            dependencyGraph: ({ event }) => {
              if ('output' in event && event.output && typeof event.output === 'object' && 'dependencyGraph' in event.output) {
                return event.output.dependencyGraph;
              }
              return { dependencies: new Map(), dependents: new Map(), rootNodes: [], leafNodes: [] };
            },
            readyNodes: ({ event }) => {
              if ('output' in event && event.output && typeof event.output === 'object' && 'readyNodes' in event.output) {
                return event.output.readyNodes;
              }
              return [];
            },
            waitingNodes: ({ event }) => {
              if ('output' in event && event.output && typeof event.output === 'object' && 'waitingNodes' in event.output) {
                return event.output.waitingNodes;
              }
              return [];
            },
            workflowSettings: ({ event }) => {
              if ('output' in event && event.output && typeof event.output === 'object' && 'workflowSettings' in event.output) {
                return event.output.workflowSettings;
              }
              return {};
            },
          }),
        },
        onError: {
          target: 'failed',
          actions: assign({
            errors: ({ context, event }) => {
              const newErrors = new Map(context.errors);
              newErrors.set('workflow_load', event.error instanceof Error ? event.error : new Error(String(event.error)));
              return newErrors;
            },
          }),
        },
      },

      on: {
        CANCEL_EXECUTION: 'cancelled',
      },
    },

    validating: {
      invoke: {
        id: 'validateWorkflow',
        src: 'validateWorkflowService',
        input: ({ context }) => context,
        onDone: [
          {
            target: 'ready',
            guard: 'isValidationSuccessful',
            actions: assign({
              metadata: ({ context }) => ({
                ...context.metadata,
                validatedAt: Date.now(),
              }),
            }),
          },
          {
            target: 'failed',
            actions: assign({
              errors: ({ context, event }) => {
                const newErrors = new Map(context.errors);
                if ('output' in event && event.output && typeof event.output === 'object' && 'errors' in event.output && Array.isArray(event.output.errors)) {
                  newErrors.set('validation', new Error(event.output.errors.join(', ')));
                } else {
                  newErrors.set('validation', new Error('Validation failed'));
                }
                return newErrors;
              },
            }),
          },
        ],
        onError: {
          target: 'failed',
          actions: assign({
            errors: ({ context, event }) => {
              const newErrors = new Map(context.errors);
              newErrors.set('validation_error', event.error instanceof Error ? event.error : new Error(String(event.error)));
              return newErrors;
            },
          }),
        },
      },

      on: {
        CANCEL_EXECUTION: 'cancelled',
      },
    },

    ready: {
      on: {
        START_EXECUTION: {
          target: 'executing',
          actions: assign({
            metadata: ({ context }) => ({
              ...context.metadata,
              executionStartTime: Date.now(),
            }),
          }),
        },
        CANCEL_EXECUTION: 'cancelled',
      },
    },

    executing: {
      type: 'parallel',

      states: {
        nodeExecution: {
          initial: 'checkingReadyNodes',

          states: {
            checkingReadyNodes: {
              always: [
                {
                  target: 'spawningNodes',
                  guard: 'hasReadyNodes',
                },
                {
                  target: 'waitingForNodes',
                  guard: 'hasRunningNodes',
                },
                {
                  target: 'allNodesCompleted',
                  guard: 'allNodesCompleted',
                },
              ],
            },

            spawningNodes: {
              entry: 'spawnReadyNodes',
              always: 'waitingForNodes',
            },

            waitingForNodes: {
              on: {
                NODE_COMPLETED: {
                  actions: [
                    'handleNodeCompleted',
                    'handleConditionalNodeCompleted',
                    'updateDependencies',
                    'updateConditionalDependencies',
                    'logConditionalRouting',
                  ],
                  target: 'checkingReadyNodes',
                },
                NODE_FAILED: {
                  actions: [
                    'handleNodeFailed',
                    'updateDependencies',
                  ],
                  target: 'checkingReadyNodes',
                },
              },
            },

            allNodesCompleted: {
              type: 'final',
            },
          },

          onDone: {
            target: '#workflow.completed',
            actions: 'prepareWorkflowResult',
          },
        },

        monitoring: {
          initial: 'active',

          states: {
            active: {
              invoke: {
                id: 'monitorExecution',
                src: 'monitorExecutionService',
                input: ({ context }) => context,
              },

              on: {
                TIMEOUT: {
                  target: '#workflow.failed',
                  actions: assign({
                    errors: ({ context }) => {
                      const newErrors = new Map(context.errors);
                      newErrors.set('timeout', new Error('Workflow execution timeout'));
                      return newErrors;
                    },
                  }),
                },
              },
            },
          },
        },
      },

      on: {
        PAUSE_EXECUTION: {
          target: 'paused',
          actions: [
            'pauseRunningNodes',
            assign({
              metadata: ({ context }) => ({
                ...context.metadata,
                pausedAt: Date.now(),
              }),
            }),
          ],
        },
        CANCEL_EXECUTION: {
          target: 'cancelled',
          actions: 'cancelRunningNodes',
        },
        WORKFLOW_FAILED: {
          target: 'failed',
          actions: assign({
            errors: ({ context, event }) => {
              const newErrors = new Map(context.errors);
              newErrors.set('workflow_execution', event.error);
              return newErrors;
            },
          }),
        },
      },
    },

    paused: {
      on: {
        RESUME_EXECUTION: {
          target: 'executing',
          actions: [
            'resumeRunningNodes',
            assign({
              metadata: ({ context }) => ({
                ...context.metadata,
                resumedAt: Date.now(),
              }),
            }),
          ],
        },
        CANCEL_EXECUTION: 'cancelled',
      },
    },

    completed: {
      entry: [
        'notifyWorkflowCompleted',
        assign({
          metadata: ({ context }) => ({
            ...context.metadata,
            completedAt: Date.now(),
            executionTime: Date.now() - context.metadata.startTime,
          }),
        }),
      ],

      always: 'cleanup',
    },

    failed: {
      entry: [
        'notifyWorkflowFailed',
        assign({
          metadata: ({ context }) => ({
            ...context.metadata,
            failedAt: Date.now(),
            executionTime: Date.now() - context.metadata.startTime,
          }),
        }),
      ],

      on: {
        RETRY_WORKFLOW: {
          target: 'loading',
          actions: [
            'resetWorkflowState',
            assign({
              metadata: ({ context }) => ({
                ...context.metadata,
                retryCount: (context.metadata.retryCount || 0) + 1,
                startTime: Date.now(),
              }),
            }),
          ],
        },
      },

      always: 'cleanup',
    },

    cancelled: {
      entry: [
        'cancelAllNodes',
        'notifyWorkflowCancelled',
        assign({
          metadata: ({ context }) => ({
            ...context.metadata,
            cancelledAt: Date.now(),
            executionTime: Date.now() - context.metadata.startTime,
          }),
        }),
      ],

      always: 'cleanup',
    },

    cleanup: {
      invoke: {
        id: 'cleanupWorkflow',
        src: 'cleanupWorkflowService',
        input: ({ context }) => context,
        onDone: 'final',
        onError: 'final',
      },
    },

    final: {
      type: 'final',
    },
  },
}, {
  // Guards
  guards: {
    isValidationSuccessful: ({ event }) => {
      // Type guard to check if event has output property
      return Boolean('output' in event && event.output && typeof event.output === 'object' && 'isValid' in event.output && event.output.isValid);
    },

    hasReadyNodes: ({ context }) => {
      return Boolean(context.readyNodes && context.readyNodes.length > 0);
    },

    hasRunningNodes: ({ context }) => {
      return Boolean(context.runningNodes && context.runningNodes.length > 0);
    },

    allNodesCompleted: ({ context }) => {
      const totalNodes = context.nodes ? context.nodes.size : 0;
      const completedNodes = context.metadata?.completedNodes || 0;
      const failedNodes = context.metadata?.failedNodes || 0;

      return Boolean(totalNodes > 0 && (completedNodes + failedNodes) >= totalNodes);
    },

    // Import conditional guards (temporarily commented out due to type issues)
    // ...conditionalWorkflowGuards,
  },

  // Actions
  actions: {
    spawnReadyNodes: assign({
      runningNodes: ({ context, spawn }) => {
        const newRunningNodes = [...context.runningNodes];

        for (const nodeId of context.readyNodes) {
          const nodeState = context.nodes.get(nodeId);
          if (nodeState && nodeState.status === 'pending') {
            // Spawn node execution machine
            const nodeActor = spawn(nodeExecutionMachine, {
              id: `node-${nodeId}`,
              input: {
                nodeId,
                context: context,
              },
            });

            // Update node state
            nodeState.status = 'running';
            nodeState.startTime = Date.now();

            newRunningNodes.push(nodeId);

            // Store actor reference for later use
            console.log(`Spawned node actor: ${nodeActor.id}`);
          }
        }

        return newRunningNodes;
      },
      readyNodes: () => [], // Clear ready nodes after spawning
    }),

    handleNodeCompleted: assign({
      runningNodes: ({ context, event }) => {
        if (event.type === 'NODE_COMPLETED') {
          return context.runningNodes.filter(id => id !== event.nodeId);
        }
        return context.runningNodes;
      },
      metadata: ({ context }) => ({
        ...context.metadata,
        completedNodes: (context.metadata.completedNodes || 0) + 1,
      }),
    }),

    handleNodeFailed: assign({
      runningNodes: ({ context, event }) => {
        if (event.type === 'NODE_FAILED') {
          return context.runningNodes.filter(id => id !== event.nodeId);
        }
        return context.runningNodes;
      },
      errors: ({ context, event }) => {
        const newErrors = new Map(context.errors);
        if (event.type === 'NODE_FAILED') {
          newErrors.set(event.nodeId, event.error);
        }
        return newErrors;
      },
      metadata: ({ context }) => ({
        ...context.metadata,
        failedNodes: (context.metadata.failedNodes || 0) + 1,
      }),
    }),

    updateDependencies: assign({
      readyNodes: ({ context }) => {
        // Recalculate ready nodes based on completed nodes
        const completedNodes = new Set();

        for (const [nodeId, nodeState] of context.nodes.entries()) {
          if (nodeState.status === 'completed') {
            completedNodes.add(nodeId);
          }
        }

        const readyNodes: string[] = [];

        for (const [nodeId, nodeState] of context.nodes.entries()) {
          if (nodeState.status === 'pending') {
            const dependencies = context.dependencyGraph.dependencies.get(nodeId) || [];
            const allDependenciesCompleted = dependencies.every(depId =>
              completedNodes.has(depId)
            );

            if (allDependenciesCompleted) {
              readyNodes.push(nodeId);
            }
          }
        }

        return readyNodes;
      },
    }),

    prepareWorkflowResult: assign({
      metadata: ({ context }) => ({
        ...context.metadata,
        finalOutputData: Object.fromEntries(context.executionData.entries()),
      }),
    }),

    pauseRunningNodes: ({ context }) => {
      // Send pause events to all running node actors
      for (const nodeId of context.runningNodes) {
        // TODO: Implementation would send pause event to node actors
        console.log(`Pausing node: ${nodeId}`);
      }
    },

    resumeRunningNodes: ({ context }) => {
      // Send resume events to all paused node actors
      for (const nodeId of context.runningNodes) {
        // TODO: Implementation would send resume event to node actors
        console.log(`Resuming node: ${nodeId}`);
      }
    },

    cancelRunningNodes: ({ context }) => {
      // Send cancel events to all running node actors
      for (const nodeId of context.runningNodes) {
        // TODO: Implementation would send cancel event to node actors
        console.log(`Cancelling node: ${nodeId}`);
      }
    },

    cancelAllNodes: ({ context }) => {
      // Cancel all nodes regardless of state
      for (const [nodeId, nodeState] of context.nodes.entries()) {
        if (nodeState.status === 'running' || nodeState.status === 'pending') {
          nodeState.status = 'failed'; // Use 'failed' instead of 'cancelled' as it's a valid status
          console.log(`Cancelled node: ${nodeId}`);
        }
      }
    },

    resetWorkflowState: assign({
      errors: () => new Map(),
      runningNodes: () => [],
      executionData: () => new Map(),
      nodes: ({ context }) => {
        // Reset all node states to pending
        const resetNodes = new Map(context.nodes);
        for (const [nodeId, nodeState] of resetNodes.entries()) {
          if (nodeState.status !== 'completed') {
            nodeState.status = 'pending';
            nodeState.startTime = undefined;
            nodeState.endTime = undefined;
            nodeState.error = undefined;
            nodeState.retryCount = 0;
            console.log(`Reset node: ${nodeId}`);
          }
        }
        return resetNodes;
      },
    }),

    notifyWorkflowCompleted: ({ context }) => {
      // Emit workflow completed event
      console.log(`Workflow ${context.workflowId} completed successfully`);
    },

    notifyWorkflowFailed: ({ context }) => {
      // Emit workflow failed event
      console.log(`Workflow ${context.workflowId} failed`);
    },

    notifyWorkflowCancelled: ({ context }) => {
      // Emit workflow cancelled event
      console.log(`Workflow ${context.workflowId} cancelled`);
    },

    // Import conditional actions (temporarily commented out due to type issues)
    // ...conditionalWorkflowActions,
  },


});