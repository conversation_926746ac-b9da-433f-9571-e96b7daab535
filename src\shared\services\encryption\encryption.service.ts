import { AppException, ErrorCode } from 'src/common/exceptions/app.exception';
import { Injectable, Logger } from '@nestjs/common';
import * as crypto from 'crypto';

/**
 * Service cung cấp mã hóa/giải mã với public và private key.
 * Sử dụng thuật toán AES-256-CBC kết hợp với HMAC để đảm bảo tính toàn vẹn.
 */
@Injectable()
export class EncryptionService {
  private readonly logger = new Logger(EncryptionService.name);
  private readonly algorithm = 'aes-256-cbc';
  private readonly hmacAlgorithm = 'sha256';
  private readonly ivLength = 16; // 16 bytes cho IV

  constructor() {
    this.logger.log('EncryptionService initialized');
  }

  /**
   * Tạo encryption key từ public và private key
   * @param secretKeyPublic Public key
   * @param secretKeyPrivate Private key
   * @returns Buffer chứa encryption key
   */
  private generateEncryptionKey(secretKeyPublic: string, secretKeyPrivate: string): Buffer {
    // K<PERSON>t hợp public và private key
    const combinedKey = `${secretKeyPrivate}:${secretKeyPublic}`;

    // Tạo key 32 bytes (256 bits) từ combined key bằng SHA-256
    return crypto
      .createHash('sha256')
      .update(combinedKey)
      .digest();
  }

  /**
   * Tạo HMAC key từ public và private key
   * @param secretKeyPublic Public key
   * @param secretKeyPrivate Private key
   * @returns Buffer chứa HMAC key
   */
  private generateHmacKey(secretKeyPublic: string, secretKeyPrivate: string): Buffer {
    // Tạo HMAC key khác với encryption key
    const combinedKey = `${secretKeyPublic}:${secretKeyPrivate}:hmac`;

    return crypto
      .createHash('sha256')
      .update(combinedKey)
      .digest();
  }

  /**
   * Mã hóa nội dung với public và private key
   * @param params Tham số mã hóa
   * @returns Kết quả mã hóa
   */
  encrypt<T>(secretKeyPublic: string, secretKeyPrivate: string, payload: T): string {
    try {
      // Validate input
      if (!secretKeyPublic || !secretKeyPrivate || !payload) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Invalid input'
        );
      }

      // Tạo encryption key và HMAC key
      const encryptionKey = this.generateEncryptionKey(secretKeyPublic, secretKeyPrivate);
      const hmacKey = this.generateHmacKey(secretKeyPublic, secretKeyPrivate);

      // Tạo IV ngẫu nhiên
      const iv = crypto.randomBytes(this.ivLength);

      // Tạo cipher với key và iv
      const cipher = crypto.createCipheriv(this.algorithm, encryptionKey, iv);

      // Mã hóa dữ liệu
      let encrypted = cipher.update(JSON.stringify(payload), 'utf8', 'base64');
      encrypted += cipher.final('base64');

      // Tạo HMAC để đảm bảo tính toàn vẹn
      const hmac = crypto
        .createHmac(this.hmacAlgorithm, hmacKey)
        .update(iv.toString('base64') + encrypted)
        .digest('base64');

      // Kết hợp IV, encrypted data và HMAC
      const result = `${iv.toString('base64')}:${encrypted}:${hmac}`;

      this.logger.debug('Encryption successful');

      return result;

    } catch (error) {
      this.logger.error(`Encryption error: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Encryption error'
      );
    }
  }

  /**
   * Giải mã nội dung với public và private key
   * @param params Tham số giải mã
   * @returns Kết quả giải mã
   */
  decrypt<T>(secretKeyPublic: string, secretKeyPrivate: string, encryptedContent: string): T {
    try {
      // Validate input
      if (!secretKeyPublic || !secretKeyPrivate || !encryptedContent) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Invalid input'
        );
      }

      // Tách IV, encrypted data và HMAC
      const parts = encryptedContent.split(':');
      if (parts.length !== 3) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Invalid input'
        );
      }

      const [ivBase64, encryptedData, receivedHmac] = parts;

      // Tạo encryption key và HMAC key
      const encryptionKey = this.generateEncryptionKey(secretKeyPublic, secretKeyPrivate);
      const hmacKey = this.generateHmacKey(secretKeyPublic, secretKeyPrivate);

      // Verify HMAC để đảm bảo tính toàn vẹn
      const expectedHmac = crypto
        .createHmac(this.hmacAlgorithm, hmacKey)
        .update(ivBase64 + encryptedData)
        .digest('base64');

      if (expectedHmac !== receivedHmac) {
        this.logger.error(
          `expected ${expectedHmac}, got ${receivedHmac}`
        )
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Invalid HMAC'
        );
      }

      // Chuyển đổi IV từ base64
      const iv = Buffer.from(ivBase64, 'base64');

      // Tạo decipher với key và iv
      const decipher = crypto.createDecipheriv(this.algorithm, encryptionKey, iv);

      // Giải mã dữ liệu
      let decrypted = decipher.update(encryptedData, 'base64', 'utf8');
      decrypted += decipher.final('utf8');

      this.logger.debug('Decryption successful');

      return JSON.parse(decrypted) as T;

    } catch (error) {
      this.logger.error(`Decryption error: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Decryption error'
      );
    }
  }

  /**
   * Tạo secret key ngẫu nhiên
   * @returns Secret key dạng hex string
   */
  generateSecretKey(): string {
    return crypto.randomBytes(32).toString('hex');
  }
}
