import { Injectable, Logger } from '@nestjs/common';
import { AgentConfig } from '../../../shared/interfaces/agent-config.interface';
import { AuthContext } from '../../../shared/interfaces';
import { AgentConfigBuilderService } from '../../../shared/services';
import { ZaloPlatformStrategy } from './zalo-platform.strategy';

/**
 * Zalo Agent Config Builder Service (Facade)
 *
 * Provides a Zalo-specific interface for building agent configurations
 * while delegating the actual work to the generic AgentConfigBuilderService.
 * Handles Zalo-specific parameter mapping for OA owners and Zalo users.
 */
@Injectable()
export class ZaloAgentConfigBuilderService {
  private readonly logger = new Logger(ZaloAgentConfigBuilderService.name);

  constructor(
    private readonly baseService: AgentConfigBuilderService,
    private readonly platformStrategy: ZaloPlatformStrategy,
  ) {}

  /**
   * Build agent configurations for Zalo platform
   *
   * @param agentIds Array of agent IDs to build configurations for
   * @param promptBuilderMap Map of agent IDs to prompt builder functions
   * @param zaloUserId Zalo user ID for tracking (not used for integrations)
   * @param zaloOwnerId Zalo OA owner's user ID (determines which API keys to use)
   * @param zaloData Zalo context and user information
   * @returns Map of agent IDs to complete AgentConfigInterface objects
   */
  async buildAgentConfigs(param: {
    agentIds: Array<{ id: string; prompt?: string }>;
    promptBuilderMap: Record<string, Array<() => string | Promise<string>>>;
    zaloUserId?: string;
    zaloOwnerId?: number;
    zaloData?: any;
  }): Promise<Record<string, AgentConfig>> {
    const {
      agentIds,
      promptBuilderMap,
      zaloUserId,
      zaloOwnerId,
      zaloData,
    } = param;

    this.logger.debug(
      `Building agent configs for ${agentIds.length} agents (Zalo platform)`,
      {
        agentIds: agentIds.map((agent) => agent.id),
        zaloUserId,
        zaloOwnerId,
        zaloDataKeys: zaloData ? Object.keys(zaloData) : [],
      },
    );

    // Convert Zalo-specific parameters to generic format
    const authContext: AuthContext = {
      zaloUserId,
      zaloOwnerId,
      zaloData,
    };

    // Delegate to the generic base service
    return this.baseService.buildAgentConfigs({
      agentIds,
      promptBuilderMap,
      platformStrategy: this.platformStrategy,
      authContext,
    });
  }
}