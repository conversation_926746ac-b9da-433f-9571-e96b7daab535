import { Injectable, Logger } from '@nestjs/common';
import { NodeExecutionContext } from '../types';
import { Node } from '../../../entities/node.entity';

/**
 * Agent execution context
 */
export interface AgentExecutionContext {
  /** Unique execution ID */
  executionId: string;
  
  /** Agent ID */
  agentId: string;
  
  /** Thread ID cho conversation continuity */
  threadId: string;
  
  /** Session metadata */
  session: {
    /** Session ID */
    id: string;
    
    /** User ID */
    userId: number;
    
    /** Workflow context */
    workflowId: string;
    
    /** Node context */
    nodeId: string;
    
    /** Created timestamp */
    createdAt: number;
    
    /** Last activity timestamp */
    lastActivity: number;
    
    /** Session status */
    status: 'active' | 'paused' | 'completed' | 'failed' | 'expired';
  };
  
  /** Conversation state */
  conversation: {
    /** Message history */
    messages: any[];
    
    /** Current turn count */
    turnCount: number;
    
    /** Conversation metadata */
    metadata: Record<string, any>;
  };
  
  /** Agent configuration */
  agentConfig: {
    /** Model settings */
    model: {
      name: string;
      temperature: number;
      maxTokens: number;
      topP?: number;
      frequencyPenalty?: number;
      presencePenalty?: number;
    };
    
    /** Available tools */
    tools: string[];
    
    /** System prompt */
    systemPrompt?: string;
    
    /** Custom instructions */
    instructions?: string[];
    
    /** Execution settings */
    settings: {
      timeout: number;
      maxRetries: number;
      enableStreaming: boolean;
      enableCheckpoints: boolean;
      checkpointInterval: number;
    };
  };
  
  /** Checkpoint management */
  checkpoints: {
    /** Current checkpoint ID */
    currentId?: string;
    
    /** Checkpoint history */
    history: Array<{
      id: string;
      timestamp: number;
      state: any;
      metadata?: Record<string, any>;
    }>;
    
    /** Auto-save settings */
    autoSave: {
      enabled: boolean;
      interval: number;
      maxCheckpoints: number;
    };
  };
  
  /** Execution state */
  execution: {
    /** Current status */
    status: 'pending' | 'running' | 'paused' | 'completed' | 'failed';
    
    /** Start time */
    startTime: number;
    
    /** End time */
    endTime?: number;
    
    /** Progress information */
    progress: {
      currentStep: number;
      totalSteps: number;
      percentage: number;
      description?: string;
    };
    
    /** Resource usage */
    resources: {
      tokenUsage: {
        input: number;
        output: number;
        total: number;
      };
      
      executionTime: number;
      memoryUsage?: number;
    };
  };
  
  /** Error handling */
  errorHandling: {
    /** Retry count */
    retryCount: number;
    
    /** Max retries allowed */
    maxRetries: number;
    
    /** Last error */
    lastError?: Error;
    
    /** Error history */
    errorHistory: Array<{
      timestamp: number;
      error: string;
      retryAttempt: number;
    }>;
  };
}

/**
 * Context creation options
 */
export interface ContextCreationOptions {
  /** Custom thread ID */
  threadId?: string;
  
  /** Initial messages */
  initialMessages?: any[];
  
  /** Custom agent config overrides */
  agentConfigOverrides?: Partial<AgentExecutionContext['agentConfig']>;
  
  /** Session timeout (ms) */
  sessionTimeout?: number;
  
  /** Enable auto-checkpoints */
  enableAutoCheckpoints?: boolean;
  
  /** Checkpoint interval (ms) */
  checkpointInterval?: number;
  
  /** Custom metadata */
  metadata?: Record<string, any>;
}

/**
 * Service để manage agent execution context và thread management
 */
@Injectable()
export class AgentExecutionContextService {
  private readonly logger = new Logger(AgentExecutionContextService.name);
  
  // In-memory storage cho active contexts (trong production có thể dùng Redis)
  private readonly activeContexts = new Map<string, AgentExecutionContext>();
  
  // Thread ID mapping
  private readonly threadMapping = new Map<string, string>(); // threadId -> executionId
  
  /**
   * Create new agent execution context
   */
  async createAgentContext(
    nodeContext: NodeExecutionContext,
    options: ContextCreationOptions = {}
  ): Promise<AgentExecutionContext> {
    const executionId = `agent_exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const threadId = options.threadId || `thread_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const sessionId = `session_${nodeContext.executionId}_${nodeContext.node.id}`;
    
    this.logger.debug(`Creating agent context: ${executionId} for agent: ${nodeContext.node.agentId}`);
    
    // Extract agent config từ node parameters
    const agentConfig = this.extractAgentConfig(nodeContext.node, options.agentConfigOverrides);
    
    const context: AgentExecutionContext = {
      executionId,
      agentId: nodeContext.node.agentId!,
      threadId,
      session: {
        id: sessionId,
        userId: nodeContext.userId,
        workflowId: nodeContext.workflowId,
        nodeId: nodeContext.node.id,
        createdAt: Date.now(),
        lastActivity: Date.now(),
        status: 'active',
      },
      conversation: {
        messages: options.initialMessages || [],
        turnCount: 0,
        metadata: options.metadata || {},
      },
      agentConfig,
      checkpoints: {
        history: [],
        autoSave: {
          enabled: options.enableAutoCheckpoints !== false,
          interval: options.checkpointInterval || 30000,
          maxCheckpoints: 10,
        },
      },
      execution: {
        status: 'pending',
        startTime: Date.now(),
        progress: {
          currentStep: 0,
          totalSteps: 1,
          percentage: 0,
        },
        resources: {
          tokenUsage: {
            input: 0,
            output: 0,
            total: 0,
          },
          executionTime: 0,
        },
      },
      errorHandling: {
        retryCount: 0,
        maxRetries: agentConfig.settings.maxRetries,
        errorHistory: [],
      },
    };
    
    // Store context
    this.activeContexts.set(executionId, context);
    this.threadMapping.set(threadId, executionId);
    
    // Setup auto-cleanup
    this.scheduleContextCleanup(executionId, options.sessionTimeout || 3600000); // 1 hour default
    
    this.logger.log(`Created agent context: ${executionId} with thread: ${threadId}`);
    
    return context;
  }
  
  /**
   * Get agent context by execution ID
   */
  getAgentContext(executionId: string): AgentExecutionContext | undefined {
    return this.activeContexts.get(executionId);
  }
  
  /**
   * Get agent context by thread ID
   */
  getAgentContextByThread(threadId: string): AgentExecutionContext | undefined {
    const executionId = this.threadMapping.get(threadId);
    return executionId ? this.activeContexts.get(executionId) : undefined;
  }
  
  /**
   * Update agent context
   */
  async updateAgentContext(
    executionId: string,
    updates: Partial<AgentExecutionContext>
  ): Promise<boolean> {
    const context = this.activeContexts.get(executionId);
    
    if (!context) {
      this.logger.warn(`Agent context not found: ${executionId}`);
      return false;
    }
    
    // Merge updates
    Object.assign(context, updates);
    context.session.lastActivity = Date.now();
    
    this.logger.debug(`Updated agent context: ${executionId}`);
    
    return true;
  }
  
  /**
   * Add message to conversation
   */
  async addMessage(
    executionId: string,
    message: {
      role: 'user' | 'assistant' | 'system' | 'tool';
      content: string;
      metadata?: Record<string, any>;
    }
  ): Promise<boolean> {
    const context = this.activeContexts.get(executionId);
    
    if (!context) {
      this.logger.warn(`Agent context not found: ${executionId}`);
      return false;
    }
    
    const messageWithTimestamp = {
      ...message,
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
    };
    
    context.conversation.messages.push(messageWithTimestamp);
    context.conversation.turnCount++;
    context.session.lastActivity = Date.now();
    
    this.logger.debug(`Added message to context ${executionId}: ${message.role}`);
    
    return true;
  }
  
  /**
   * Create checkpoint
   */
  async createCheckpoint(
    executionId: string,
    state: any,
    metadata?: Record<string, any>
  ): Promise<string> {
    const context = this.activeContexts.get(executionId);
    
    if (!context) {
      throw new Error(`Agent context not found: ${executionId}`);
    }
    
    const checkpointId = `checkpoint_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const checkpoint = {
      id: checkpointId,
      timestamp: Date.now(),
      state,
      metadata,
    };
    
    context.checkpoints.history.push(checkpoint);
    context.checkpoints.currentId = checkpointId;
    
    // Cleanup old checkpoints
    if (context.checkpoints.history.length > context.checkpoints.autoSave.maxCheckpoints) {
      context.checkpoints.history = context.checkpoints.history.slice(-context.checkpoints.autoSave.maxCheckpoints);
    }
    
    this.logger.debug(`Created checkpoint ${checkpointId} for context ${executionId}`);
    
    return checkpointId;
  }
  
  /**
   * Restore từ checkpoint
   */
  async restoreFromCheckpoint(
    executionId: string,
    checkpointId: string
  ): Promise<any> {
    const context = this.activeContexts.get(executionId);
    
    if (!context) {
      throw new Error(`Agent context not found: ${executionId}`);
    }
    
    const checkpoint = context.checkpoints.history.find(cp => cp.id === checkpointId);
    
    if (!checkpoint) {
      throw new Error(`Checkpoint not found: ${checkpointId}`);
    }
    
    context.checkpoints.currentId = checkpointId;
    
    this.logger.log(`Restored context ${executionId} from checkpoint ${checkpointId}`);
    
    return checkpoint.state;
  }
  
  /**
   * Update execution progress
   */
  async updateProgress(
    executionId: string,
    currentStep: number,
    totalSteps: number,
    description?: string
  ): Promise<boolean> {
    const context = this.activeContexts.get(executionId);
    
    if (!context) {
      return false;
    }
    
    context.execution.progress = {
      currentStep,
      totalSteps,
      percentage: Math.round((currentStep / totalSteps) * 100),
      description,
    };
    
    context.session.lastActivity = Date.now();
    
    return true;
  }
  
  /**
   * Update resource usage
   */
  async updateResourceUsage(
    executionId: string,
    tokenUsage?: { input: number; output: number },
    executionTime?: number,
    memoryUsage?: number
  ): Promise<boolean> {
    const context = this.activeContexts.get(executionId);
    
    if (!context) {
      return false;
    }
    
    if (tokenUsage) {
      context.execution.resources.tokenUsage.input += tokenUsage.input;
      context.execution.resources.tokenUsage.output += tokenUsage.output;
      context.execution.resources.tokenUsage.total = 
        context.execution.resources.tokenUsage.input + 
        context.execution.resources.tokenUsage.output;
    }
    
    if (executionTime !== undefined) {
      context.execution.resources.executionTime = executionTime;
    }
    
    if (memoryUsage !== undefined) {
      context.execution.resources.memoryUsage = memoryUsage;
    }
    
    return true;
  }
  
  /**
   * Handle execution error
   */
  async handleExecutionError(
    executionId: string,
    error: Error
  ): Promise<boolean> {
    const context = this.activeContexts.get(executionId);
    
    if (!context) {
      return false;
    }
    
    context.errorHandling.retryCount++;
    context.errorHandling.lastError = error;
    context.errorHandling.errorHistory.push({
      timestamp: Date.now(),
      error: error.message,
      retryAttempt: context.errorHandling.retryCount,
    });
    
    // Update execution status
    if (context.errorHandling.retryCount >= context.errorHandling.maxRetries) {
      context.execution.status = 'failed';
      context.session.status = 'failed';
    }
    
    this.logger.error(`Error in agent context ${executionId} (attempt ${context.errorHandling.retryCount}):`, error);
    
    return true;
  }
  
  /**
   * Complete execution
   */
  async completeExecution(executionId: string): Promise<boolean> {
    const context = this.activeContexts.get(executionId);
    
    if (!context) {
      return false;
    }
    
    context.execution.status = 'completed';
    context.execution.endTime = Date.now();
    context.execution.resources.executionTime = context.execution.endTime - context.execution.startTime;
    context.session.status = 'completed';
    
    this.logger.log(`Completed agent execution: ${executionId}`);
    
    return true;
  }
  
  /**
   * Cleanup agent context
   */
  async cleanupContext(executionId: string): Promise<boolean> {
    const context = this.activeContexts.get(executionId);
    
    if (!context) {
      return false;
    }
    
    // Remove from mappings
    this.threadMapping.delete(context.threadId);
    this.activeContexts.delete(executionId);
    
    this.logger.debug(`Cleaned up agent context: ${executionId}`);
    
    return true;
  }
  
  /**
   * Get all active contexts
   */
  getActiveContexts(): AgentExecutionContext[] {
    return Array.from(this.activeContexts.values());
  }
  
  /**
   * Get context statistics
   */
  getContextStatistics(): {
    totalActive: number;
    byStatus: Record<string, number>;
    byAgent: Record<string, number>;
    averageExecutionTime: number;
    totalTokenUsage: number;
  } {
    const contexts = this.getActiveContexts();
    
    const stats = {
      totalActive: contexts.length,
      byStatus: {} as Record<string, number>,
      byAgent: {} as Record<string, number>,
      averageExecutionTime: 0,
      totalTokenUsage: 0,
    };
    
    let totalExecutionTime = 0;
    
    for (const context of contexts) {
      // Count by status
      const status = context.execution.status;
      stats.byStatus[status] = (stats.byStatus[status] || 0) + 1;
      
      // Count by agent
      const agentId = context.agentId;
      stats.byAgent[agentId] = (stats.byAgent[agentId] || 0) + 1;
      
      // Sum execution time and token usage
      totalExecutionTime += context.execution.resources.executionTime;
      stats.totalTokenUsage += context.execution.resources.tokenUsage.total;
    }
    
    stats.averageExecutionTime = contexts.length > 0 ? totalExecutionTime / contexts.length : 0;
    
    return stats;
  }
  
  // Private helper methods
  
  private extractAgentConfig(
    node: Node,
    overrides?: Partial<AgentExecutionContext['agentConfig']>
  ): AgentExecutionContext['agentConfig'] {
    const params = (node.parameters as any) || {};
    
    const defaultConfig: AgentExecutionContext['agentConfig'] = {
      model: {
        name: params.model?.name || params.modelName || 'gpt-3.5-turbo',
        temperature: params.model?.temperature || params.temperature || 0.7,
        maxTokens: params.model?.maxTokens || params.maxTokens || 1000,
        topP: params.model?.topP || params.topP,
        frequencyPenalty: params.model?.frequencyPenalty || params.frequencyPenalty,
        presencePenalty: params.model?.presencePenalty || params.presencePenalty,
      },
      tools: params.tools || [],
      systemPrompt: params.systemPrompt || params.prompt,
      instructions: params.instructions || [],
      settings: {
        timeout: params.timeout || 120000,
        maxRetries: params.maxRetries || 3,
        enableStreaming: params.streaming || false,
        enableCheckpoints: params.enableCheckpoints !== false,
        checkpointInterval: params.checkpointInterval || 30000,
      },
    };
    
    // Apply overrides
    if (overrides) {
      return {
        ...defaultConfig,
        ...overrides,
        model: { ...defaultConfig.model, ...overrides.model },
        settings: { ...defaultConfig.settings, ...overrides.settings },
      };
    }
    
    return defaultConfig;
  }
  
  private scheduleContextCleanup(executionId: string, timeout: number): void {
    setTimeout(() => {
      const context = this.activeContexts.get(executionId);
      
      if (context && context.session.status === 'active') {
        // Check if still active
        const timeSinceLastActivity = Date.now() - context.session.lastActivity;
        
        if (timeSinceLastActivity >= timeout) {
          context.session.status = 'expired';
          this.cleanupContext(executionId);
          this.logger.debug(`Auto-cleaned up expired context: ${executionId}`);
        } else {
          // Reschedule cleanup
          this.scheduleContextCleanup(executionId, timeout - timeSinceLastActivity);
        }
      }
    }, timeout);
  }
}
