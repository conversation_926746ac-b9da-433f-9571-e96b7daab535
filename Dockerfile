# ---- Builder Stage ----
# This stage installs all dependencies and builds the application
FROM node:22.17.1-alpine3.21 AS builder
WORKDIR /app

COPY package*.json ./
# Use a cache mount to speed up dependency installation
# Note: Using 'npm ci' is recommended if you have a package-lock.json file.
RUN --mount=type=cache,target=/root/.npm npm install

COPY . .
RUN npm run build


# ---- Production Stage ----
# This stage creates the final, lean image for production
FROM node:22.17.1-alpine3.21
WORKDIR /app

ENV NODE_ENV=production

# 1. Create a dedicated non-root user for better security
RUN addgroup -S appgroup && adduser -S appuser -G appgroup

# 2. Copy files with the correct ownership from the start
COPY --chown=appuser:appgroup package*.json ./

# 3. Install only production dependencies with a cache mount
RUN --mount=type=cache,target=/root/.npm npm install --omit=dev

# 4. Copy the built application with the correct ownership
COPY --from=builder --chown=appuser:appgroup /app/dist ./dist

# 5. Switch to the non-root user
USER appuser

EXPOSE 3008

# Start the application directly with Node.js
# Replace 'dist/main.js' if your entrypoint is different.
CMD ["node", "dist/main.js"]