import { Entity, PrimaryColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng user_tools_agents trong cơ sở dữ liệu
 * Bảng liên kết ánh xạ tool với agent
 */
@Entity('agent_user_tools')
export class AgentUserTools {
  /**
   * ID của agent, tham chiếu đến agents_user
   *
   * <PERSON><PERSON> một phần của khóa chính
   */
  @PrimaryColumn({ name: 'user_agent_id', type: 'uuid' }) userAgentId: string;

  /**
   * ID của tool, tham chiếu đến user_tools_custom
   *
   * Là một phần của khóa chính
   */
  @PrimaryColumn({ name: 'custom_tool_id', type: 'uuid' }) customToolId: string;
}
