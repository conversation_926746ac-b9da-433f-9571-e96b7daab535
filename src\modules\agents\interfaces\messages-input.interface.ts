import { Command } from '@langchain/langgraph';
import { HumanMessage } from '@langchain/core/messages';
import { KnowledgeFile, MediaData } from 'src/modules/agents/entities/data';

/**
 * Attachment type discriminator enum
 */
export enum AttachmentType {
  IMAGE = 'IMAGE',
  KNOWLEDGE_FILE = 'KNOWLEDGE_FILE'
}

/**
 * Interface for media attachments (images)
 * Based on MediaData entity fields - bare minimum
 */
export interface MediaAttachment {
  /**
   * Unique identifier for the attachment
   */
  id: string;

  /**
   * Display name of the attachment
   */
  name: string;

  /**
   * Description of the media
   */
  description?: string;

  /**
   * Tags associated with the media (from JSONB field)
   */
  tags?: Record<string, any>;

  /**
   * Storage key for accessing the media file
   */
  storageKey: string;

  /**
   * Type of attachment - IMAGE only
   */
  type: AttachmentType.IMAGE;
}

/**
 * Interface for knowledge file attachments
 * Based on KnowledgeFile entity fields - bare minimum
 */
export interface KnowledgeFileAttachment {
  /**
   * Unique identifier for the attachment
   */
  id: string;

  /**
   * Display name of the attachment
   */
  name: string;

  /**
   * OpenAI file ID for RAG processing
   */
  fileId: string;

  /**
   * Type of attachment
   */
  type: AttachmentType.KNOWLEDGE_FILE;
}

/**
 * Container for media attachments in a thread
 * Key: media ID, Value: media attachment data
 */
export type ThreadMediaAttachments = MediaAttachment[];

/**
 * Container for knowledge file attachments in a thread
 * Key: knowledge file ID, Value: knowledge file attachment data
 */
export type ThreadKnowledgeFileAttachments = KnowledgeFileAttachment[];


/**
 * Database query result types with message associations
 */
export type MediaDataWithMessageId = MediaData & { messageId: string };
export type KnowledgeFileWithMessageId = KnowledgeFile & { messageId: string };

/**
 * Thread attachments collection from database queries
 */
export interface ThreadAttachmentsWithMessages {
  images: MediaDataWithMessageId[];
  knowledgeFiles: KnowledgeFileWithMessageId[];
}

export interface ReplyToMessageAttachments {
  images?: MediaAttachment[];
  knowledgeFiles?: KnowledgeFileAttachment[];
}

export interface ReplyToMessageData {
  text: string;
  attachments: ReplyToMessageAttachments;
}

export type ReplyToMessagesContext = ReplyToMessageData[];

/**
 * Message-specific attachment processing results
 */
// TODO: add more media types if needed
export interface MessageSpecificAttachments {
  messageSpecificImages: MediaDataWithMessageId[];
  messageSpecificKnowledgeFiles: KnowledgeFileWithMessageId[];
  replyToSpecificImages: MediaDataWithMessageId[];
  replyToSpecificKnowledgeFiles: KnowledgeFileWithMessageId[];
}

/**
 * Input preparation result for LangChain processing
 */
export interface LangChainInput {
  messages: HumanMessage[];
}

/**
 * Tool call decision command for LangGraph
 */
export type ToolCallDecisionCommand = Command<{
  choice: 'yes' | 'no' | 'always';
}>;

/**
 * Execution input union type
 */
export type ExecutionInput = LangChainInput | ToolCallDecisionCommand;
