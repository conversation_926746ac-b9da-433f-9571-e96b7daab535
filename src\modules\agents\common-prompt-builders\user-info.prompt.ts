import { Repository } from 'typeorm';
import { env } from 'src/config';
import { UserInfo } from '../shared/interfaces';
import { UserMemories } from '../platforms/in-app';

/**
 * User Info Only Prompt Builder (without memory)
 * Generates XML representation of user context for personalized agent responses (without memory)
 * Uses attribute-heavy approach for minimal token usage
 */
export function buildUserInfoOnlyPrompt(userInfo?: UserInfo): string {
  if (!userInfo) {
    return ''; // No user info available
  }

  const parts: string[] = ['<user-context>'];

  // Identity section
  const identityAttrs: string[] = [];
  if (userInfo.fullName) identityAttrs.push(`name="${userInfo.fullName}"`);
  if (userInfo.gender) identityAttrs.push(`gender="${userInfo.gender}"`);
  if (userInfo.type) identityAttrs.push(`type="${userInfo.type}"`);

  if (identityAttrs.length > 0) {
    parts.push(`  <identity ${identityAttrs.join(' ')} />`);
  }

  // Demographics section
  const demoAttrs: string[] = [];
  if (userInfo.dateOfBirth) {
    const birthYear = new Date(userInfo.dateOfBirth.toString()).getFullYear();
    const age = new Date().getFullYear() - birthYear;
    demoAttrs.push(`age="${age}"`);
  }
  if (userInfo.countryCode)
    demoAttrs.push(`country-code="${userInfo.countryCode}"`);
  if (userInfo.address) demoAttrs.push(`location="${userInfo.address}"`);
  if (userInfo.timezone) demoAttrs.push(`timezone="${userInfo.timezone}"`);
  if (userInfo.currency) demoAttrs.push(`currency="${userInfo.currency}"`);

  if (demoAttrs.length > 0) {
    parts.push(`  <demographics ${demoAttrs.join(' ')} />`);
  }

  // Account section
  const accountAttrs: string[] = [];
  if (userInfo.email) {
    // Extract domain for context (business vs personal)
    const domain = userInfo.email.split('@')[1];
    accountAttrs.push(`email-domain="${domain}"`);
  }
  if (userInfo.pointsBalance !== undefined)
    accountAttrs.push(`points="${userInfo.pointsBalance}"`);
  if (userInfo.isVerifyPhone !== undefined)
    accountAttrs.push(`verified="${userInfo.isVerifyPhone}"`);

  if (accountAttrs.length > 0) {
    parts.push(`  <account ${accountAttrs.join(' ')} />`);
  }

  parts.push('</user-context>');

  return parts.join('\n');
}

export const buildUserMemoryPrompt = async (
  userId: number,
  userMemoriesRepository: Repository<UserMemories>,
): Promise<string> => {
  if (!userId) {
    return ''; // No user ID, no memories
  }

  try {
    // Fetch recent user memories
    const memories = await userMemoriesRepository
      .createQueryBuilder('memory')
      .where('memory.userId = :userId', { userId })
      .orderBy('memory.createdAt', 'DESC')
      .limit(env.memory.MEMORY_PROMPT_LIMIT)
      .getMany();

    if (!memories.length) {
      return ''; // No memories found
    }

    const parts: string[] = ['<user-memories>'];

    memories.forEach((memory) => {
      const attributes: string[] = [`id="${memory.id}"`];

      // Add created date if available (convert timestamp to date)
      if (
        memory.createdAt &&
        typeof memory.createdAt === 'number' &&
        memory.createdAt > 0
      ) {
        try {
          const date = new Date(memory.createdAt);
          if (!isNaN(date.getTime())) {
            const dateString = date.toISOString().split('T')[0];
            attributes.push(`created="${dateString}"`);
          }
        } catch (error) {
          // Skip invalid timestamp, continue without date
          console.warn(
            `Invalid timestamp for memory ${memory.id}: ${memory.createdAt}`,
          );
        }
      }

      parts.push(`  <memory ${attributes.join(' ')}>`);

      // Content field
      if (memory.content) {
        parts.push(`    <content>${memory.content}</content>`);
      }

      // Metadata (if present)
      if (memory.metadata && Object.keys(memory.metadata).length > 0) {
        const metadataEntries = Object.entries(memory.metadata)
          .map(([key, value]) => `${key}="${value}"`)
          .join(' ');
        parts.push(`    <metadata ${metadataEntries} />`);
      }

      parts.push('  </memory>');
    });

    parts.push('</user-memories>');
    return parts.join('\n');
  } catch (error) {
    console.error('Error fetching user memories:', error);
    return ''; // Return empty on error to avoid breaking the prompt
  }
};
