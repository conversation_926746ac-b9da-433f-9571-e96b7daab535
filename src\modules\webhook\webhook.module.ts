import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bullmq';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { QueueName } from '../../queue';
import { UsageDeductionProcessor } from '../../processors/webhook/usage-deduction.processor';
import { ZaloWebhookProcessor } from '../../processors/webhook/zalo-webhook.processor';
import { UserAddonUsage } from '../../shared/entities/user-addon-usage.entity';
import { Addon } from '../../shared/entities/addon.entity';
import { UserAudience } from '../marketing/entities/user-audience.entity';
import { Integration } from '../../shared/entities/integration.entity';
import { IntegrationProvider } from '../../shared/entities/integration-provider.entity';
import { UserAudienceRepository } from '../marketing/repositories/user-audience.repository';
import { ZaloOATokenAdapterService } from '../../shared/services/zalo/zalo-oa-token-adapter.service';
import { ZaloUserManagementService } from '../../shared/services/zalo/zalo-user-management.service';
import { ZaloService } from '../../shared/services/zalo/zalo.service';
import { KeyPairEncryptionService } from '../../shared/services/encryption/key-pair-encryption.service';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';

/**
 * Module xử lý webhook events trong worker
 * Chủ yếu để handle usage deduction jobs
 */
@Module({
  imports: [
    HttpModule,
    ConfigModule,

    // Event emitter for WebSocket events
    EventEmitterModule.forRoot(),

    // TypeORM entities
    TypeOrmModule.forFeature([
      UserAddonUsage,
      Addon,
      UserAudience,
      Integration,
      IntegrationProvider,
    ]),
    BullModule.registerQueue({
      name: QueueName.WEBHOOK,
    }),
  ],
  providers: [
    UsageDeductionProcessor,
    ZaloWebhookProcessor,
    UserAudienceRepository,
    ZaloOATokenAdapterService,
    ZaloUserManagementService,
    ZaloService,
    KeyPairEncryptionService,
  ],
  exports: [
    UsageDeductionProcessor,
    ZaloWebhookProcessor,
    UserAudienceRepository,
    ZaloOATokenAdapterService,
    ZaloUserManagementService,
    ZaloService,
    KeyPairEncryptionService,
  ],
})
export class WebhookModule { }
