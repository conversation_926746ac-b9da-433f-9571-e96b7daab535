import { Injectable, Logger } from '@nestjs/common';
import { StructuredTool, ToolRunnableConfig } from '@langchain/core/tools';
import { CallbackManagerForToolRun } from '@langchain/core/callbacks/manager';
import { z } from 'zod';
import { RagEngineService } from 'src/infra';
import { InjectRepository } from '@nestjs/typeorm';
import {
  KnowledgeFile,
  KnowledgeFileStatus,
  OwnerType,
} from '../../entities/data';
import { Repository } from 'typeorm';
import { InAppSupervisorWorkersConfig } from '../../platforms/in-app/graph-configs/in-app-supervisor-workers.config';
import { AssistantSpendingType } from '../../enums';
import { UserBillingService } from '../../services';

@Injectable()
export class UserKnowledgeRAGTool extends StructuredTool {
  name: string;
  description: string;
  schema: z.ZodSchema;
  private readonly rPointEmbeddingRate = 2;
  private readonly logger = new Logger(UserKnowledgeRAGTool.name);

  constructor(
    private readonly ragEngineService: RagEngineService,
    @InjectRepository(KnowledgeFile)
    private readonly knowledgeFileRepository: Repository<KnowledgeFile>,
    private readonly userBilling: UserBillingService,
  ) {
    super();
    this.logger.log('Initializing UserKnowledgeRAGTool');
    this.name = 'search_knowledge_files';
    this.description =
      'Search through uploaded knowledge files to find relevant information based on queries. ' +
      'Use this tool when the user has uploaded files and you need to find specific information from them. ' +
      "Generate appropriate search queries based on the user's question and the context of their files.";

    this.schema = z.object({
      query: z
        .string()
        .describe(
          'The search query to find relevant information in the knowledge files',
        ),
      file_ids: z
        .array(z.string())
        .optional()
        .describe('Optional array of specific file IDs to search within'),
      max_results: z
        .number()
        .min(1)
        .max(100)
        .optional()
        .describe('Optional maximum number of results to return'),
    });
  }

  protected async _call(
    arg: {
      query: string;
      file_ids?: string[];
      max_results?: number;
    },
    runManager?: CallbackManagerForToolRun,
    parentConfig?: ToolRunnableConfig<InAppSupervisorWorkersConfig>,
  ): Promise<string> {
    try {
      const userId = parentConfig?.configurable?.currentUser?.user?.userId;
      if (!userId) {
        return 'internal error: User ID is required to search knowledge files. Stop using this tool.';
      }
      const fileIds = arg.file_ids
        ? arg.file_ids
        : await this.getAllFileIdsByUserId(userId);
      // Call the RAG engine service to search files with default settings
      const searchResult = await this.ragEngineService.searchFiles({
        query: arg.query,
        file_ids: fileIds,
        limit: arg.max_results || 10, // Default to 10 if not provided
      });
      const usageToken = searchResult.token_usage?.embedding_tokens || 0;
      this.logger.debug(
        `User ${userId} used ${usageToken} points for search query "${arg.query}"`,
      );
      const usageRPoint = usageToken * this.rPointEmbeddingRate;
      this.logger.debug(
        `User ${userId} will be charged ${usageRPoint} R-Points for search query "${arg.query}"`,
      );
      await this.userBilling.updateUserPointBalance(
        userId,
        usageRPoint,
        AssistantSpendingType.IN_APP,
      );
      await this.userBilling.createSpendingRecords(
        [
          {
            agentId: parentConfig?.configurable?.executorAgent?.id as string,
            model: 'jina-embedding-v4',
            inputTokens: usageToken,
            outputTokens: 0,
            totalTokens: usageToken,
            pointCost: usageRPoint,
          },
        ],
        userId,
        AssistantSpendingType.IN_APP,
        parentConfig?.configurable?.run_id || '',
      );

      // Format the results for the LLM
      if (!searchResult.success || searchResult.results.length === 0) {
        return `No relevant information found for query: "${arg.query}"`;
      }

      const formattedResults = searchResult.results
        .map((result, index) => {
          let resultText = `--- Result ${index + 1} ---\n`;
          resultText += `Similarity Score: ${result.similarity_score.toFixed(3)}\n`;

          if (result.rerank_score) {
            resultText += `Rerank Score: ${result.rerank_score.toFixed(3)}\n`;
          }

          if (result.filename) {
            resultText += `Source: ${result.filename}\n`;
          }

          resultText += `Content: ${result.content}\n`;

          return resultText;
        })
        .join('\n');

      const summary = `Found ${searchResult.total_found} relevant results for "${arg.query}" (showing top ${searchResult.results.length}):\n\n${formattedResults}`;

      // Log search metadata for debugging
      if (runManager) {
        runManager.handleText(
          `Search executed in ${searchResult.execution_time_ms}ms with threshold ${searchResult.threshold_used}`,
        );
      }

      return summary;
    } catch (error) {
      const errorMessage = `Error searching knowledge files: ${error.message}`;
      if (runManager) {
        runManager.handleText(errorMessage);
      }
      throw new Error(errorMessage);
    }
  }

  async getAllFileIdsByUserId(userId: number): Promise<string[]> {
    try {
      this.logger.debug(`Lấy tất cả file IDs cho user ${userId}`);

      const result = await this.knowledgeFileRepository
        .createQueryBuilder('kf')
        .select('kf.file_id', 'file_id')
        .where('kf.owned_by = :userId', { userId })
        .andWhere('kf.owner_type = :ownerType', { ownerType: OwnerType.USER })
        .andWhere('kf.status != :deletedStatus', {
          deletedStatus: KnowledgeFileStatus.DELETED,
        })
        .andWhere('kf.file_id IS NOT NULL') // Chỉ lấy những file có file_id
        .getRawMany();

      const fileIds = result.map((item) => item.file_id).filter((id) => id); // Filter out null values
      this.logger.debug(
        `Tìm thấy ${fileIds.length} file IDs cho user ${userId}`,
      );

      return fileIds;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy file IDs cho user ${userId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
