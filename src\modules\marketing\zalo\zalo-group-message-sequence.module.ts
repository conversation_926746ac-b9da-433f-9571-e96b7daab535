import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { ConfigModule } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';
import { TypeOrmModule } from '@nestjs/typeorm';
import { QueueName } from '../../../queue';
import { ZaloGroupMessageSequenceProcessor } from './zalo-group-message-sequence.processor';
import { ZaloModule } from '../../../shared/services/zalo/zalo.module';
import { Integration } from '../../../shared/entities/integration.entity';

/**
 * Module cho Zalo Group Message Sequence Worker
 * Xử lý các job gửi chuỗi tin nhắn group Zalo
 */
@Module({
  imports: [
    ConfigModule,
    BullModule.registerQueue({
      name: QueueName.ZALO_GROUP_MESSAGE_SEQUENCE,
    }),
    HttpModule.register({
      timeout: 30000,
      maxRedirects: 5,
    }),
    TypeOrmModule.forFeature([Integration]),
    ZaloModule,
  ],
  providers: [ZaloGroupMessageSequenceProcessor],
  exports: [ZaloGroupMessageSequenceProcessor],
})
export class ZaloGroupMessageSequenceModule {}
