import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AgentConfig, ReplyToMessagesContext, ThreadKnowledgeFileAttachments, ThreadMediaAttachments } from '../../../shared/interfaces';
import { InAppAgentConfigBuilderService } from './in-app-agent-config-builder.service';
import {
  InAppJobData,
} from '../interfaces';
import { buildAvailableWorkersPrompt, buildCurrentLocalTimePrompt, buildReplyToMessagesPrompt, buildThreadAttachmentsPrompt, buildUserInfoOnlyPrompt, buildUserMemoryPrompt } from '../../../common-prompt-builders';
import { UserMemories } from 'src/modules/agents/domains/internal/entities';

@Injectable()
export class InAppAgentConfigurationService {
  private readonly logger = new Logger(InAppAgentConfigurationService.name);

  constructor(
    @InjectRepository(UserMemories)
    private readonly userMemoriesRepository: Repository<UserMemories>,
    private readonly agentConfigBuilder: InAppAgentConfigBuilderService,
  ) {}

  /**
   * Build supervisor agent configuration
   * Extracted from InAppAiProcessor Phase 1.4.2
   */
  async buildSupervisorConfiguration(param: {
    jobData: InAppJobData;
    workerConfigs: Record<string, AgentConfig> | undefined;
    replyToMessagesContext: ReplyToMessagesContext;
    threadMediaAttachments: ThreadMediaAttachments;
    threadKnowledgeFileAttachments: ThreadKnowledgeFileAttachments;
  }): Promise<Record<string, AgentConfig>> {
    const {
      jobData,
      workerConfigs,
      replyToMessagesContext,
      threadMediaAttachments,
      threadKnowledgeFileAttachments,
    } = param;

    this.logger.debug('Building supervisor configuration', {
      agentId: jobData.mainAgentId,
      hasWorkerConfigs:
        !!workerConfigs && Object.keys(workerConfigs).length > 0,
      replyToContextCount: replyToMessagesContext.length,
      threadMediaCount: Object.keys(threadMediaAttachments).length,
      threadKnowledgeFileCount: Object.keys(threadKnowledgeFileAttachments)
        .length,
    });

    const supervisorPromptBuilderMap = {
      [jobData.mainAgentId]: [
        () => buildCurrentLocalTimePrompt(jobData?.humanInfo.user?.timezone),
        () => buildAvailableWorkersPrompt(workerConfigs),
        () => buildReplyToMessagesPrompt(replyToMessagesContext),
        () =>
          buildThreadAttachmentsPrompt(
            threadMediaAttachments,
            threadKnowledgeFileAttachments,
          ),
        () => buildUserInfoOnlyPrompt(jobData?.humanInfo.user),
        async () =>
          await buildUserMemoryPrompt(
            jobData?.humanInfo.user?.userId as number,
            this.userMemoriesRepository,
          ),
      ],
    };

    const supervisorConfig = await this.agentConfigBuilder.buildAgentConfigs({
      agentIds: [{ id: jobData.mainAgentId }],
      promptBuilderMap: supervisorPromptBuilderMap,
      userId: jobData.chatWithSystem
        ? undefined
        : jobData?.humanInfo?.user?.userId,
      jwt: jobData.jwt,
    });

    if (!supervisorConfig?.[jobData.mainAgentId]) {
      throw new Error(
        `Agent configuration not found for agentId: ${jobData.mainAgentId}`,
      );
    }

    this.logger.debug('Successfully built supervisor configuration', {
      agentId: jobData.mainAgentId,
      toolCount: supervisorConfig[jobData.mainAgentId].tools?.length || 0,
    });

    return supervisorConfig;
  }

  /**
   * Build worker agent configurations
   * Extracted from InAppAiProcessor Phase 1.4.3
   */
  async buildWorkerConfigurations(param: {
    jobData: InAppJobData;
    replyToMessagesContext: ReplyToMessagesContext;
    threadMediaAttachments: ThreadMediaAttachments;
    threadKnowledgeFileAttachments: ThreadKnowledgeFileAttachments;
  }): Promise<Record<string, AgentConfig> | undefined> {
    const {
      jobData,
      replyToMessagesContext,
      threadMediaAttachments,
      threadKnowledgeFileAttachments,
    } = param;

    if (!jobData.workerAgents?.length) {
      this.logger.debug(
        'No worker agents configured, skipping worker configuration',
      );
      return undefined;
    }

    this.logger.debug('Building worker configurations', {
      workerCount: jobData.workerAgents.length,
      workerIds: jobData.workerAgents.map((w) => w.id),
      replyToContextCount: replyToMessagesContext.length,
      threadMediaCount: Object.keys(threadMediaAttachments).length,
      threadKnowledgeFileCount: Object.keys(threadKnowledgeFileAttachments)
        .length,
    });

    const workerPromptBuilderMap = {};
    jobData.workerAgents.forEach((workerAgent) => {
      workerPromptBuilderMap[workerAgent.id] = [
        () => buildCurrentLocalTimePrompt(jobData?.humanInfo.user?.timezone),
        () => buildReplyToMessagesPrompt(replyToMessagesContext),
        () =>
          buildThreadAttachmentsPrompt(
            threadMediaAttachments,
            threadKnowledgeFileAttachments,
          ),
        () => buildUserInfoOnlyPrompt(jobData?.humanInfo.user),
        async () =>
          await buildUserMemoryPrompt(
            jobData?.humanInfo.user?.userId as number,
            this.userMemoriesRepository,
          ),
      ];
    });

    const workerConfigs = await this.agentConfigBuilder.buildAgentConfigs({
      agentIds: jobData.workerAgents,
      promptBuilderMap: workerPromptBuilderMap,
      userId: jobData.chatWithSystem
        ? undefined
        : jobData?.humanInfo?.user?.userId,
      jwt: jobData.jwt,
    });

    this.logger.debug('Successfully built worker configurations', {
      workerCount: Object.keys(workerConfigs || {}).length,
      workerIds: Object.keys(workerConfigs || {}),
    });

    return workerConfigs;
  }
}
