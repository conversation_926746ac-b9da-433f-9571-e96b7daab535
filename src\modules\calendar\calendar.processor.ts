import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger, Injectable } from '@nestjs/common';
import { Job } from 'bullmq';
import { QueueName } from '../../queue/queue-name.enum';
import { CalendarService } from './calendar.service';

/**
 * Processor xử lý queue Calendar
 * Xử lý các loại job:
 * - SEND_REMINDER: G<PERSON>i nhắc nhở
 * - EXECUTE_TASK: Thực thi nhiệm vụ
 * - GENERATE_REPORT: Tạo báo cáo
 * - CREATE_RECURRENCE_INSTANCES: Tạo instances lặp lại
 * - SYNC_GOOGLE_CALENDAR: Đồng bộ Google Calendar
 * - CREATE_ZOOM_MEETING: Tạo Zoom meeting
 */
@Injectable()
@Processor(QueueName.CALENDAR, {
  concurrency: 5, // Xử lý tối đa 5 job đồng thời
  stalledInterval: 30 * 1000, // 30 giây
  maxStalledCount: 1,
})
export class CalendarProcessor extends WorkerHost {
  private readonly logger = new Logger(CalendarProcessor.name);

  constructor(
    private readonly calendarService: CalendarService,
  ) {
    super();
  }

  /**
   * Xử lý job từ calendar queue
   * @param job Job chứa dữ liệu calendar cần xử lý
   */
  async process(job: Job<any, any, string>): Promise<void> {
    this.logger.log(
      `Bắt đầu xử lý calendar job: ${job.id} - Job name: ${job.name}`,
    );

    try {
      switch (job.name) {
        case 'send-reminder':
          await this.processSendReminder(job);
          break;

        case 'execute-task':
          await this.processExecuteTask(job);
          break;

        case 'generate-report':
          await this.processGenerateReport(job);
          break;

        case 'create-recurrence-instances':
          await this.processCreateRecurrenceInstances(job);
          break;

        case 'sync-google-calendar':
          await this.processSyncGoogleCalendar(job);
          break;

        case 'create-zoom-meeting':
          await this.processCreateZoomMeeting(job);
          break;

        default:
          this.logger.warn(`Unknown calendar job name: ${job.name}`);
          throw new Error(`Unknown calendar job name: ${job.name}`);
      }

      this.logger.log(
        `Hoàn thành xử lý calendar job: ${job.id} - Job name: ${job.name}`,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi xử lý calendar job: ${job.id} - Job name: ${job.name} - Error: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Xử lý job gửi nhắc nhở
   */
  private async processSendReminder(job: Job): Promise<void> {
    const {
      reminderId,
      eventId,
      userId,
      channelType,
      channelConfig,
      title,
      message,
      messageTemplate,
      metadata,
    } = job.data;

    this.logger.log(
      `Processing send reminder job: ${job.id} - Reminder: ${reminderId} - Channel: ${channelType}`,
    );

    await this.calendarService.sendReminder({
      reminderId,
      eventId,
      userId,
      channelType,
      channelConfig,
      title,
      message,
      messageTemplate,
      metadata,
    });

    this.logger.log(`Reminder sent successfully: ${reminderId}`);
  }

  /**
   * Xử lý job thực thi nhiệm vụ
   */
  private async processExecuteTask(job: Job): Promise<void> {
    const {
      taskId,
      eventId,
      userId,
      agentId,
      workflowTaskId,
      taskName,
      taskDescription,
      resources,
      executionConfig,
      executionParams,
      metadata,
    } = job.data;

    this.logger.log(
      `Processing execute task job: ${job.id} - Task: ${taskId} - Agent: ${agentId}`,
    );

    await this.calendarService.executeTask({
      taskId,
      eventId,
      userId,
      agentId,
      workflowTaskId,
      taskName,
      taskDescription,
      resources,
      executionConfig,
      executionParams,
      metadata,
    });

    this.logger.log(`Task executed successfully: ${taskId}`);
  }

  /**
   * Xử lý job tạo báo cáo
   */
  private async processGenerateReport(job: Job): Promise<void> {
    const {
      reportId,
      eventId,
      userId,
      reportType,
      reportConfig,
      metadata,
    } = job.data;

    this.logger.log(
      `Processing generate report job: ${job.id} - Report: ${reportId} - Type: ${reportType}`,
    );

    await this.calendarService.generateReport({
      reportId,
      eventId,
      userId,
      reportType,
      reportConfig,
      metadata,
    });

    this.logger.log(`Report generated successfully: ${reportId}`);
  }

  /**
   * Xử lý job tạo recurrence instances
   */
  private async processCreateRecurrenceInstances(job: Job): Promise<void> {
    const { recurrenceId, maxInstances } = job.data;

    this.logger.log(
      `Processing create recurrence instances job: ${job.id} - Recurrence: ${recurrenceId}`,
    );

    await this.calendarService.createRecurrenceInstances({
      recurrenceId,
      maxInstances,
    });

    this.logger.log(`Recurrence instances created successfully: ${recurrenceId}`);
  }

  /**
   * Xử lý job đồng bộ Google Calendar
   */
  private async processSyncGoogleCalendar(job: Job): Promise<void> {
    const { eventId, userId, accessToken } = job.data;

    this.logger.log(
      `Processing sync Google Calendar job: ${job.id} - Event: ${eventId}`,
    );

    await this.calendarService.syncGoogleCalendar({
      eventId,
      userId,
      accessToken,
    });

    this.logger.log(`Google Calendar synced successfully: ${eventId}`);
  }

  /**
   * Xử lý job tạo Zoom meeting
   */
  private async processCreateZoomMeeting(job: Job): Promise<void> {
    const { eventId, userId, zoomConfig } = job.data;

    this.logger.log(
      `Processing create Zoom meeting job: ${job.id} - Event: ${eventId}`,
    );

    await this.calendarService.createZoomMeeting({
      eventId,
      userId,
      zoomConfig,
    });

    this.logger.log(`Zoom meeting created successfully: ${eventId}`);
  }
}
