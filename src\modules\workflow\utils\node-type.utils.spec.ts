import { ENodeType } from '../interfaces/node-manager.interface';
import { NodeTypeUtils } from './node-type.utils';

describe('NodeTypeUtils', () => {
  
  describe('stringToNodeType', () => {
    it('should convert valid string to ENodeType', () => {
      expect(NodeTypeUtils.stringToNodeType('http-request')).toBe(ENodeType.HTTP_REQUEST);
      expect(NodeTypeUtils.stringToNodeType('webhook')).toBe(ENodeType.WEBHOOK);
      expect(NodeTypeUtils.stringToNodeType('if-condition')).toBe(ENodeType.IF_CONDITION);
    });

    it('should handle case insensitive conversion', () => {
      expect(NodeTypeUtils.stringToNodeType('HTTP-REQUEST')).toBe(ENodeType.HTTP_REQUEST);
      expect(NodeTypeUtils.stringToNodeType('WEBHOOK')).toBe(ENodeType.WEBHOOK);
      expect(NodeTypeUtils.stringToNodeType('If-Condition')).toBe(ENodeType.IF_CONDITION);
    });

    it('should handle whitespace', () => {
      expect(NodeTypeUtils.stringToNodeType('  http-request  ')).toBe(ENodeType.HTTP_REQUEST);
      expect(NodeTypeUtils.stringToNodeType('\twebhook\n')).toBe(ENodeType.WEBHOOK);
    });

    it('should return undefined for invalid strings', () => {
      expect(NodeTypeUtils.stringToNodeType('invalid-node-type')).toBeUndefined();
      expect(NodeTypeUtils.stringToNodeType('')).toBeUndefined();
      expect(NodeTypeUtils.stringToNodeType('   ')).toBeUndefined();
    });

    it('should return undefined for null/undefined input', () => {
      expect(NodeTypeUtils.stringToNodeType(null as any)).toBeUndefined();
      expect(NodeTypeUtils.stringToNodeType(undefined as any)).toBeUndefined();
    });
  });

  describe('nodeTypeToString', () => {
    it('should convert ENodeType to string', () => {
      expect(NodeTypeUtils.nodeTypeToString(ENodeType.HTTP_REQUEST)).toBe('http-request');
      expect(NodeTypeUtils.nodeTypeToString(ENodeType.WEBHOOK)).toBe('webhook');
      expect(NodeTypeUtils.nodeTypeToString(ENodeType.IF_CONDITION)).toBe('if-condition');
    });
  });

  describe('isValidNodeType', () => {
    it('should return true for valid node types', () => {
      expect(NodeTypeUtils.isValidNodeType('http-request')).toBe(true);
      expect(NodeTypeUtils.isValidNodeType('webhook')).toBe(true);
      expect(NodeTypeUtils.isValidNodeType('if-condition')).toBe(true);
    });

    it('should return false for invalid node types', () => {
      expect(NodeTypeUtils.isValidNodeType('invalid-type')).toBe(false);
      expect(NodeTypeUtils.isValidNodeType('')).toBe(false);
      expect(NodeTypeUtils.isValidNodeType('   ')).toBe(false);
    });
  });

  describe('getAllNodeTypeStrings', () => {
    it('should return all node type strings', () => {
      const allStrings = NodeTypeUtils.getAllNodeTypeStrings();
      expect(allStrings).toContain('http-request');
      expect(allStrings).toContain('webhook');
      expect(allStrings).toContain('if-condition');
      expect(allStrings.length).toBeGreaterThan(0);
    });
  });

  describe('getAllNodeTypes', () => {
    it('should return all ENodeType values', () => {
      const allTypes = NodeTypeUtils.getAllNodeTypes();
      expect(allTypes).toContain(ENodeType.HTTP_REQUEST);
      expect(allTypes).toContain(ENodeType.WEBHOOK);
      expect(allTypes).toContain(ENodeType.IF_CONDITION);
      expect(allTypes.length).toBeGreaterThan(0);
    });
  });

  describe('stringToNodeTypeWithFallback', () => {
    it('should return converted type for valid string', () => {
      const result = NodeTypeUtils.stringToNodeTypeWithFallback('http-request', ENodeType.WEBHOOK);
      expect(result).toBe(ENodeType.HTTP_REQUEST);
    });

    it('should return fallback for invalid string', () => {
      const result = NodeTypeUtils.stringToNodeTypeWithFallback('invalid-type', ENodeType.WEBHOOK);
      expect(result).toBe(ENodeType.WEBHOOK);
    });
  });

  describe('stringArrayToNodeTypeArray', () => {
    it('should convert array of valid strings', () => {
      const input = ['http-request', 'webhook', 'if-condition'];
      const result = NodeTypeUtils.stringArrayToNodeTypeArray(input);
      expect(result).toEqual([ENodeType.HTTP_REQUEST, ENodeType.WEBHOOK, ENodeType.IF_CONDITION]);
    });

    it('should filter out invalid strings', () => {
      const input = ['http-request', 'invalid-type', 'webhook'];
      const result = NodeTypeUtils.stringArrayToNodeTypeArray(input);
      expect(result).toEqual([ENodeType.HTTP_REQUEST, ENodeType.WEBHOOK]);
    });
  });

  describe('nodeTypeArrayToStringArray', () => {
    it('should convert array of ENodeTypes to strings', () => {
      const input = [ENodeType.HTTP_REQUEST, ENodeType.WEBHOOK, ENodeType.IF_CONDITION];
      const result = NodeTypeUtils.nodeTypeArrayToStringArray(input);
      expect(result).toEqual(['http-request', 'webhook', 'if-condition']);
    });
  });

  describe('searchNodeTypes', () => {
    it('should find node types by pattern', () => {
      const result = NodeTypeUtils.searchNodeTypes('http');
      expect(result).toContain(ENodeType.HTTP_REQUEST);
    });

    it('should handle case insensitive search', () => {
      const result = NodeTypeUtils.searchNodeTypes('HTTP');
      expect(result).toContain(ENodeType.HTTP_REQUEST);
    });

    it('should return empty array for empty pattern', () => {
      const result = NodeTypeUtils.searchNodeTypes('');
      expect(result).toEqual([]);
    });
  });

  describe('getDisplayName', () => {
    it('should convert kebab-case to Title Case', () => {
      expect(NodeTypeUtils.getDisplayName(ENodeType.HTTP_REQUEST)).toBe('Http Request');
      expect(NodeTypeUtils.getDisplayName(ENodeType.IF_CONDITION)).toBe('If Condition');
      expect(NodeTypeUtils.getDisplayName(ENodeType.WEBHOOK)).toBe('Webhook');
    });
  });

  describe('groupNodeTypesByCategory', () => {
    it('should group node types by category', () => {
      const groups = NodeTypeUtils.groupNodeTypesByCategory();
      expect(groups.HTTP).toContain(ENodeType.HTTP_REQUEST);
      expect(groups.Trigger).toContain(ENodeType.WEBHOOK);
      expect(groups.Logic).toContain(ENodeType.IF_CONDITION);
    });
  });

  describe('validateAndNormalize', () => {
    it('should return normalized string for valid input', () => {
      expect(NodeTypeUtils.validateAndNormalize('HTTP-REQUEST')).toBe('http-request');
      expect(NodeTypeUtils.validateAndNormalize('  webhook  ')).toBe('webhook');
    });

    it('should return null for invalid input', () => {
      expect(NodeTypeUtils.validateAndNormalize('invalid-type')).toBeNull();
      expect(NodeTypeUtils.validateAndNormalize('')).toBeNull();
    });
  });

  describe('createStringToNodeTypeMap', () => {
    it('should create mapping from string to ENodeType', () => {
      const map = NodeTypeUtils.createStringToNodeTypeMap();
      expect(map['http-request']).toBe(ENodeType.HTTP_REQUEST);
      expect(map['webhook']).toBe(ENodeType.WEBHOOK);
    });
  });

  describe('createNodeTypeToStringMap', () => {
    it('should create mapping from ENodeType to string', () => {
      const map = NodeTypeUtils.createNodeTypeToStringMap();
      expect(map[ENodeType.HTTP_REQUEST]).toBe('http-request');
      expect(map[ENodeType.WEBHOOK]).toBe('webhook');
    });
  });
});
