import { Injectable, Logger } from '@nestjs/common';
import {
  StructuredTool,
  ToolRunnableConfig,
  ToolSchemaBase,
} from '@langchain/core/tools';
import { CallbackManagerForToolRun } from '@langchain/core/callbacks/manager';
import { z } from 'zod';
import { DataSource, In, Repository } from 'typeorm';
import { streamImageToBase64 } from '../utils';
import { AIMessage, HumanMessage, ToolMessage } from '@langchain/core/messages';
import { v7 } from 'uuid';
import {
  Command,
  getCurrentTaskInput,
  MessagesAnnotation,
} from '@langchain/langgraph';
import { CdnService } from 'src/infra';
import { TimeIntervalEnum } from '@common/dto/time-interval.enum';
import { InjectRepository } from '@nestjs/typeorm';
import { MediaData } from '../entities/data';

@Injectable()
export class ImageLoaderTool extends StructuredTool {
  name: string;
  description: string;
  schema: ToolSchemaBase;
  private readonly logger = new Logger(ImageLoaderTool.name);

  constructor(
    private readonly dataSource: DataSource,
    private readonly cdnService: CdnService,
    @InjectRepository(MediaData)
    private readonly mediaDataRepository: Repository<MediaData>,
  ) {
    super();
    this.name = 'load_images_into_context';
    this.description = `- Load images listed inside <thread-media> tag or other image IDs got from tools into the context so that images can be read
       - Choose the image base on its title, description, and the current context of the conversation. Choose wisely.
       - You can load at most 5 images and at least 1 image.`;
    this.schema = z.object({
      reason: z
        .string()
        .nonempty()
        .describe('Reason for including the specified images into the context'),
      imageIds: z
        .array(z.string())
        .min(1, 'Image IDs array must contain at least one image ID')
        .max(5, 'Image IDs array must not contain more than 5 image IDs')
        .nonempty()
        .describe(
          'ID of the image to include in the response. You can specify up to 5 image IDs',
        ),
    });
  }

  protected async _call(
    arg: any,
    runManager?: CallbackManagerForToolRun,
    parentConfig?: ToolRunnableConfig,
  ): Promise<any> {
    const { reason, imageIds } = arg;

    try {
      const images = await this.mediaDataRepository.find({
        select: ['id', 'storageKey'],
        where: {
          id: In(imageIds),
        },
      });

      if (images.length === 0) {
        throw new Error(`No images found with IDs: ${imageIds.join(', ')}`);
      }

      const base64 = await Promise.all(
        images.map(async (image: { id: string; storageKey: string }) => {
          // Generate CDN URL manually (avoiding CdnService dependency)
          const cdnUrl = this.cdnService.generateUrlView(
            image.storageKey,
            TimeIntervalEnum.TEN_MINUTES,
          );
          if (!cdnUrl) {
            const message = `Failed to generate CDN URL for image ${image.id}. Retry, but exclude this image from the list.`;
            this.logger.error(message);
            throw new Error(message);
          }
          return await streamImageToBase64(cdnUrl);
        }),
      );

      const imageMessage = new HumanMessage({
        id: v7(),
        content: base64.map((base64Result) => ({
          type: 'image_url',
          image_url: {
            url: `data:${base64Result.mimeType};base64,${base64Result.base64String}`,
          },
        })),
      });

      const currentState =
        getCurrentTaskInput() as (typeof MessagesAnnotation)['State'];
      const { messages } = currentState;
      const lastMessage = messages.at(-1) as AIMessage;
      const toolCallId = lastMessage.tool_calls?.[0].id;
      if (!toolCallId) {
        this.logger.error('Failed to find tool call ID');
        throw new Error('Failed to find tool call ID');
      }

      const toolMessage = new ToolMessage({
        content: `Images ${imageIds.join(', ')} included in response. Reason: ${reason}`,
        tool_call_id: toolCallId,
      });

      this.logger.log(`Loaded ${base64.length} images`);
      return new Command({
        update: {
          messages: [toolMessage, imageMessage],
        },
      });
    } catch (error) {
      this.logger.error(`Failed to load images: ${error.message}`, error.stack);
      throw error;
    }
  }
}
