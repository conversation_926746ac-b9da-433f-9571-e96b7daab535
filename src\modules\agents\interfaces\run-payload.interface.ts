import { Platform } from '../enums/platform.enum';

/**
 * Generic Run Payload Interface
 *
 * Complete execution context for agent runs with platform-specific typing
 * Message data is handled separately via buffering mechanism
 *
 * @template P - Platform context type (e.g., InAppPlatformContext)
 */
export interface RunPayload<P = any> {
  /**
   * Agent ID to invoke
   */
  agentId: string;

  /**
   * User identity (mutually exclusive - either userId OR employeeId)
   */
  user: {
    /**
     * Standard user ID for regular users
     */
    userId?: number;

    /**
     * Employee ID for internal staff
     */
    employeeId?: number;
  };

  /**
   * Platform enum (CHAT, ZALO, MESSENGER, WEBSITE)
   */
  platform: Platform;

  /**
   * Generic platform-specific data (includes authentication and context)
   */
  platformContext: P;
}
