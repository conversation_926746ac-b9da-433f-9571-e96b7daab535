import { Injectable, Logger } from '@nestjs/common';
import { StructuredTool, ToolRunnableConfig } from '@langchain/core/tools';
import { CallbackManagerForToolRun } from '@langchain/core/callbacks/manager';
import { z } from 'zod';
import { RagEngineService } from 'src/infra';
import { AssistantSpendingType } from '../enums';
import { UserBillingService } from '../services';
import { DataSource } from 'typeorm';
import { WebsitePlannerExecutorConfig } from '../../platforms/website/graph-configs/website-planner-executor-config.interface';
import { env } from 'src/config';

@Injectable()
export class AgentProductRAGTool extends StructuredTool {
  name: string;
  description: string;
  schema: z.ZodSchema;
  private readonly rPointEmbeddingRate = 2;
  private readonly logger = new Logger(AgentProductRAGTool.name);

  constructor(
    private readonly ragEngineService: RagEngineService,
    private readonly userBilling: UserBillingService,
    private readonly dataSource: DataSource,
  ) {
    super();
    this.logger.log('Initializing UserProductRAGTool');
    this.name = 'search_products';
    this.description =
      "Search through agents' assigned products (physical, digital, events, services) to find relevant information based on queries. " +
      'Use this tool when the user wants to find specific products or product information. ' +
      "Generate appropriate search queries based on the user's question and product requirements.";

    this.schema = z.object({
      query: z.string().describe('The search query to find relevant products'),
      product_types: z
        .array(z.enum(['physical', 'digital', 'event', 'service']))
        .optional()
        .describe('Optional array of product types to filter by'),
      max_results: z
        .number()
        .min(1)
        .max(100)
        .optional()
        .describe('Optional maximum number of results to return'),
      include_sub_products: z
        .boolean()
        .optional()
        .describe(
          'Optional flag to include sub-products (classifications, variants, phân loại, etc.) in search results',
        ),
      customer_product_ids: z
        .array(z.string())
        .min(1, 'Customer product IDs are required to search products')
        .describe(
          'Array of customer product IDs to search within. If not provided, all accessible products will be searched.',
        ),
    });
  }

  protected async _call(
    arg: {
      query: string;
      product_types?: ('physical' | 'digital' | 'event' | 'service')[];
      max_results?: number;
      include_sub_products?: boolean;
      customer_product_ids: number[];
    },
    runManager?: CallbackManagerForToolRun,
    parentConfig?: ToolRunnableConfig<WebsitePlannerExecutorConfig>,
  ): Promise<string> {
    try {
      const userId = parentConfig?.configurable?.currentUser.owner.userId;
      if (!userId) {
        return 'internal error: User ID is required to search products. Stop using this tool.';
      }
      this.logger.debug(
        `User ${userId} is searching products with query: "${arg.query}"`,
      );
      const customerProductIds =
        arg.customer_product_ids ||
        (
          await this.dataSource.query(
            `SELECT id FROM customer_products cp 
                JOIN agents_product ap ON cp.id = ap.product_id
                WHERE agent_id = $1`,
            [userId],
          )
        ).map((row: { id: number }) => row.id);

      this.logger.debug(
        `User ${userId} has ${customerProductIds.length} products to search within.`,
      );
      // Call the RAG engine service to search products with default settings
      const searchResult = await this.ragEngineService.searchProducts({
        query: arg.query,
        product_types: arg.product_types,
        limit: arg.max_results || 10, // Default to 10 if not provided
        include_sub_products: arg.include_sub_products ?? true,
        customer_product_ids: customerProductIds,
        threshold: 0.5, // Default threshold
        use_rerank: true, // Default to using rerank
      });

      this.logger.debug(
        `search finishes with ${searchResult.total_found} products found for user ${userId}`,
      );

      this.logger.debug('caling api to fetch product details');
      const productIds = [50, ...searchResult.results.map((r) => r.id)];
      const url = new URL(env.red.REDAI_API_URL);
      url.pathname = '/v1/public/customer-products/by-ids';
      url.searchParams.append('ids', productIds.join(','));
      url.searchParams.append('userId', userId.toString());
      const fetchResult = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': env.red.REDAI_API_KEY,
        },
      });

      if (!fetchResult.ok) {
        this.logger.error(
          `Failed to fetch product details for user ${userId}`,
        );
        return `Failed to fetch product details. Please try again later.`;
      }
      const result = await fetchResult.json();

      const usageToken = searchResult.total_tokens_used || 0;
      this.logger.debug(
        `User ${userId} used ${usageToken} points for product search query "${arg.query}"`,
      );
      const usageRPoint = usageToken * this.rPointEmbeddingRate;
      this.logger.debug(
        `User ${userId} will be charged ${usageRPoint} R-Points for product search query "${arg.query}"`,
      );
      await this.userBilling.updateUserPointBalance(
        userId,
        usageRPoint,
        AssistantSpendingType.IN_APP,
      );
      await this.userBilling.createSpendingRecords(
        [
          {
            agentId: parentConfig?.configurable?.executorAgent?.id as string,
            model: 'jina-embedding-v4',
            inputTokens: usageToken,
            outputTokens: 0,
            totalTokens: usageToken,
            pointCost: usageRPoint,
          },
        ],
        userId,
        AssistantSpendingType.IN_APP,
        parentConfig?.configurable?.run_id || '',
      );

      // Format the results for the LLM
      if (!searchResult.success || searchResult.results.length === 0) {
        return `No relevant products found for query: "${arg.query}"`;
      }

      const formattedResults = JSON.stringify(result, null, 2);

      let summary = `Found ${searchResult.total_found} relevant products for "${arg.query}" (showing top ${searchResult.results.length}):\n`;
      summary += `Recognized Product Types: ${searchResult.recognized_product_types.join(', ')}\n`;
      summary += `Search Strategy: ${searchResult.search_strategy}\n\n`;
      summary += formattedResults;

      // Log search metadata for debugging
      if (runManager) {
        runManager.handleText(
          `Product search executed in ${searchResult.execution_time_ms}ms with threshold ${searchResult.threshold_used}`,
        );
      }

      return summary;
    } catch (error) {
      const errorMessage = `Error searching products: ${error.message}`;
      if (runManager) {
        runManager.handleText(errorMessage);
      }
      throw new Error(errorMessage);
    }
  }
}
