/**
 * Interface cho job data gửi chuỗi tin nhắn tư vấn cho 1 user
 */
export interface ZaloConsultationSequenceUserJobData {
  /**
   * ID chiến dịch
   */
  campaignId: number;

  /**
   * ID Official Account
   */
  oaId: string;

  /**
   * ID người dùng <PERSON> (1 user duy nhất)
   */
  userId: string;

  /**
   * Danh sách tin nhắn trong chuỗi
   */
  messages: ConsultationMessageItemDto[];

  /**
   * Thời gian tạo job
   */
  timestamp: number;

  /**
   * ID để tracking job (optional)
   */
  trackingId?: string;
}

/**
 * Enum định nghĩa các loại tin nhắn tư vấn
 */
export enum ConsultationMessageType {
  TEXT = 'text',
  IMAGE = 'image',
  STICKER = 'sticker',
  FILE = 'file',
  REQUEST_INFO = 'request_info',
}

/**
 * Interface cho một tin nhắn trong chuỗi tin nhắn tư vấn
 */
export interface ConsultationMessageItemDto {
  /**
   * Loại tin nhắn tư vấn
   */
  messageType: ConsultationMessageType;

  /**
   * Thời gian delay trước khi gửi tin nhắn này (giây)
   */
  delaySeconds?: number;

  // TEXT message fields
  /**
   * Nội dung tin nhắn văn bản (bắt buộc cho TEXT)
   */
  text?: string;

  // IMAGE message fields
  /**
   * URL hình ảnh (bắt buộc cho IMAGE nếu không có attachmentId)
   */
  imageUrl?: string;

  /**
   * ID của attachment đã upload (bắt buộc cho IMAGE nếu không có imageUrl)
   */
  attachmentId?: string;

  /**
   * Nội dung tin nhắn kèm theo hình ảnh (tùy chọn cho IMAGE)
   */
  imageMessage?: string;

  // STICKER message fields
  /**
   * ID sticker (bắt buộc cho STICKER)
   */
  stickerId?: string;

  // FILE message fields
  /**
   * URL file đính kèm (bắt buộc cho FILE)
   */
  fileUrl?: string;

  /**
   * Tên file (bắt buộc cho FILE)
   */
  filename?: string;

  /**
   * Nội dung tin nhắn kèm theo file (tùy chọn cho FILE)
   */
  fileMessage?: string;

  // REQUEST_INFO message fields
  /**
   * Tiêu đề của form yêu cầu thông tin (bắt buộc cho REQUEST_INFO)
   */
  requestTitle?: string;

  /**
   * Phụ đề của form yêu cầu thông tin (tùy chọn cho REQUEST_INFO)
   */
  requestSubtitle?: string;

  /**
   * URL hình ảnh đại diện cho form yêu cầu thông tin (bắt buộc cho REQUEST_INFO)
   */
  requestImageUrl?: string;
}

/**
 * Union type cho tất cả các loại job data consultation sequence
 */
export type ZaloConsultationSequenceJobData = ZaloConsultationSequenceUserJobData;
