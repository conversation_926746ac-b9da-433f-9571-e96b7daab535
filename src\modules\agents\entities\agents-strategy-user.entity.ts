import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { StrategyContentStep } from '../interfaces';

/**
 * Entity đại diện cho bảng agents_strategy_user trong cơ sở dữ liệu
 * Bảng lưu thông tin các strategy agent do người dùng sở hữu, bao gồm ví dụ tùy chỉnh và thời điểm sở hữu
 */
@Entity('agents_strategy_user')
export class AgentStrategyUser {
  /**
   * UUID duy nhất định danh bản ghi agents_strategy_user
   */
  @PrimaryGeneratedColumn('uuid') id: string;

  /**
   * Khóa ngoại tham chiếu đến bảng agents
   */
  @Column({ name: 'agents_strategy_id', type: 'uuid', nullable: true })
  agentsStrategyId: string | null;

  /**
   * Danh sách ví dụ (example) tuỳ chỉnh theo người dùng cho strategy agent, dạng JSONB
   */
  @Column({ name: 'example', type: 'jsonb', default: '[]' })
  example: StrategyContentStep[];

  /**
   * ID của người dùng sở hữu strategy này, tham chiếu đến bảng users
   */
  @Column({ name: 'user_id', type: 'integer', nullable: true }) userId:
    | number
    | null;

  /**
   * Thời điểm người dùng sở hữu strategy, tính theo epoch time (milliseconds)
   */
  @Column({
    name: 'owned_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  ownedAt: number;
}
