import { Platform } from '../../../shared/enums';
import { AgentConfig } from '../../../shared/interfaces/agent-config.interface';
import { PlannerExecutorGraphConfigType } from '../../../shared/graphs/planner-executor.graph';
import { UserConvertCustomerInfo, UserInfo } from '../../../shared/interfaces';
import { ZaloInfo } from '../interfaces/zalo-info.interface';

/**
 * Zalo User Context for PlannerExecutorGraph
 * Contains all user-related information needed for Zalo conversations
 */
export interface ZaloUserContext {
  /**
   * Zalo user (customer/visitor interacting with the OA)
   */
  visitor: UserConvertCustomerInfo;

  /**
   * Zalo OA owner (business owner who pays for tokens)
   */
  owner: UserInfo;

  /**
   * Zalo-specific context information (OA details, user details, etc.)
   */
  info: ZaloInfo;
}

/**
 * Zalo Planner-Executor Graph Configuration Interface
 * 
 * Extends the base PlannerExecutorGraphConfigType with Zalo-specific context.
 * Used to configure the PlannerExecutorGraph for Zalo conversations.
 * 
 * Pattern: Planner → Executor
 * - Planner: Internal agent that creates execution plans
 * - Executor: Main agent that interacts with Zalo users and creates visible messages
 */
export interface ZaloPlannerExecutorConfig extends PlannerExecutorGraphConfigType {
  /**
   * Platform identifier - always ZALO
   */
  platform: Platform.ZALO;

  /**
   * Current user context containing all Zalo-related information
   */
  currentUser: ZaloUserContext;
}

/**
 * Example Zalo Planner-Executor Configuration:
 * {
 *   plannerAgent: {
 *     id: 'planner-456',
 *     // ... other AgentConfig properties
 *   },
 *   executorAgent: {
 *     id: 'executor-789',
 *     // ... other AgentConfig properties
 *   },
 *   thread_id: 'zalo:0a983b62-cdd0-4ebe-9d9d-b259f97194ac',
 *   platform: Platform.ZALO,
 *   currentUser: {
 *     zaloUser: {
 *       id: '864b9263-257d-4999-b468-74ff73ca8d1d',
 *       name: 'Zalo User',
 *       phone: '+***********',
 *       // ... other UserConvertCustomerInfo properties
 *     },
 *     zaloOwner: {
 *       userId: 1,
 *       fullName: 'Business Owner',
 *       email: '<EMAIL>',
 *       // ... other UserInfo properties
 *     },
 *     zaloInfo: {
 *       oaId: 'oa_123456789',
 *       oaName: 'My Business OA',
 *       encryptedConfig: 'encrypted_token_data',
 *       secretKey: 'secret_key_for_decryption',
 *       zaloUserDetail: {
 *         user_id: 'zalo_user_123',
 *         display_name: 'User Display Name',
 *         // ... other Zalo API user fields
 *       }
 *     }
 *   }
 * }
 */