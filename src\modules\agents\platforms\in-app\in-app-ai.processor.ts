import { Injectable, Logger } from '@nestjs/common';
import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { z } from 'zod';
import { SupervisorWorkersGraph } from '../../graphs/multi-agents-graphs/supervisor-workers.graph';
import { InAppJobData } from './interfaces';
import { GraphJobName, QueueName } from '../../../../queue';
import {
  InAppEventProcessorService,
  InternalMessageOperationsService,
  InternalAttachmentProcessingService,
  InternalInputPreparationService,
  InAppExecutionCoordinatorService,
  InAppPostProcessingService,
  InAppAgentConfigurationService,
} from './services';
import { StreamEventType } from '../../enums';
import { InAppSupervisorWorkersConfig } from './graph-configs/in-app-supervisor-workers.config';
import { RunStatusService } from '../../../../shared/services/run-status.service';
import {
  ExecutionInput,
  ReplyToMessagesContext,
  ThreadKnowledgeFileAttachments,
  ThreadMediaAttachments,
} from '../../interfaces';
import { UserAbortError } from '../../errors';

@Injectable()
@Processor(QueueName.IN_APP_AI)
export class InAppAiProcessor extends WorkerHost {
  private readonly logger = new Logger(InAppAiProcessor.name);

  constructor(
    private readonly supervisorWorkersGraph: SupervisorWorkersGraph,
    private readonly eventProcessor: InAppEventProcessorService,
    private readonly messageOperations: InternalMessageOperationsService,
    private readonly attachmentProcessing: InternalAttachmentProcessingService,
    private readonly inputPreparation: InternalInputPreparationService,
    private readonly executionCoordinator: InAppExecutionCoordinatorService,
    private readonly postProcessing: InAppPostProcessingService,
    private readonly configuration: InAppAgentConfigurationService,
    private readonly runStatusService: RunStatusService,
  ) {
    super();
  }

  /**
   * Main job processing method
   * Only handles trigger operations - cancellation via Redis/progress monitoring
   * Job name = graph type
   */
  async process(job: Job<InAppJobData, any, GraphJobName>): Promise<void> {
    const jobData = job.data;
    const jobName: GraphJobName = job.name;
    switch (jobName) {
      case GraphJobName.SUPERVISOR_WORKERS:
        await this.handleSupervisorWorkers(jobData);
        break;
      case GraphJobName.PLANNER_EXECUTOR:
        this.logger.warn('Planner executor not implemented');
        break;
      default:
        const message = `Unknown job name: ${jobName}`;
        this.logger.error(message);
        throw new Error(message);
    }
  }

  /**
   * Handle trigger job - database-first execution
   * 1. Set active run 2. Get unprocessed messages 3. Build config 4. Execute LangGraph 5. Handle cancellation
   * Includes re-enqueueing logic for double-texting after interrupt cancellation
   */
  private async handleSupervisorWorkers(jobData: InAppJobData): Promise<void> {
    this.logger.log(
      `Triggering run: ${jobData.runId} for thread: ${jobData.threadId}`,
    );

    const { keys } = jobData;
    if (!keys) {
      this.logger.error('Job data keys are missing');
      throw new Error('Job data keys are required for processing');
    }

    // 🔒 VALIDATE KEYS RECEIVED FROM BACKEND
    this.validateJobDataKeys(jobData);

    const platformThreadId = keys.platformThreadId;

    this.logger.debug('Worker starting processing', {
      runId: jobData.runId,
      threadId: jobData.threadId,
      platformThreadId,
      runStatusKey: keys.runStatusKey,
    });

    try {
      // 1. ✅ NEW: Create and activate run with context
      const activated = await this.runStatusService.activateRun(
        platformThreadId,
        jobData.runId,
        {
          agentId: jobData.mainAgentId,
          userId: jobData.humanInfo.user?.userId,
          platform: jobData.platform,
          workerAgentCount: jobData.workerAgents?.length || 0,
        },
      );

      if (!activated) {
        this.logger.error(
          'Failed to activate run - may have been cancelled or invalid',
          {
            threadId: jobData.threadId,
            runId: jobData.runId,
          },
        );
        return;
      }

      // 2. Get UNPROCESSED USER messages from database (only new messages)
      const userMessages =
        await this.messageOperations.fetchUnprocessedUserMessages(
          jobData.threadId,
        );

      const replyToMessageIds = userMessages
        .filter((msg) => !!msg.replyingToMessageId)
        .map((msg) => msg.replyingToMessageId)
        .filter((id): id is string => id !== undefined);

      const replyToMessages =
        await this.messageOperations.fetchReplyToMessages(replyToMessageIds);

      let input: ExecutionInput;
      let replyToMessagesContext: ReplyToMessagesContext = [];
      let threadMediaAttachments: ThreadMediaAttachments = [];
      let threadKnowledgeFileAttachments: ThreadKnowledgeFileAttachments = [];

      if (jobData.toolCallDecision) {
        this.logger.debug('is tool call decision');
        this.logger.debug('tool call decision', jobData.toolCallDecision);
        input = await this.inputPreparation.handleToolCallDecisionInput(
          jobData.toolCallDecision,
        );
        // For tool call decisions, attachments remain empty as they're not needed
      } else {
        this.logger.debug('is not tool call decision');
        // 3. Get all thread attachments with message IDs and build collections
        const threadAttachmentResult =
          await this.attachmentProcessing.processThreadAttachments(
            jobData.threadId,
          );
        const threadAttachmentsWithMessages =
          threadAttachmentResult.threadAttachmentsWithMessages;

        // Use the processed attachments
        threadMediaAttachments = threadAttachmentResult.threadMediaAttachments;
        threadKnowledgeFileAttachments =
          threadAttachmentResult.threadKnowledgeFileAttachments;

        // 4. Process message-specific attachments
        // TODO: handle more media types if needed
        const {
          messageSpecificImages,
          messageSpecificKnowledgeFiles,
          replyToSpecificImages,
          replyToSpecificKnowledgeFiles,
        } = await this.attachmentProcessing.processMessageAttachments(
          userMessages,
          replyToMessageIds,
          threadAttachmentsWithMessages,
        );
        // TODO: add more media types if needed
        replyToMessagesContext =
          await this.messageOperations.buildReplyToMessagesContext(
            replyToMessages,
            replyToSpecificImages,
            replyToSpecificKnowledgeFiles,
          );

        // 5. Build LangChain input from messages and images
        input = await this.inputPreparation.buildLangChainInput({
          userMessages,
          messageSpecificImages,
          messageSpecificKnowledgeFiles,
        });
        if (userMessages.length === 0) {
          this.logger.warn(
            `No USER messages found for thread ${jobData.threadId}`,
          );
          return;
        }
      }

      this.logger.debug('Found USER messages', {
        threadId: jobData.threadId,
        messageCount: userMessages.length,
        messageIds: userMessages.map((m) => m.id),
      });

      // ✅ NEW: Check for cancellation using unified status
      const cancelled =
        await this.runStatusService.isRunCancelled(platformThreadId);
      if (cancelled) {
        this.logger.warn(
          `Run ${jobData.runId} for thread ${jobData.threadId} was cancelled before processing`,
        );
        throw new UserAbortError(jobData.threadId, 'Run was cancelled');
      }

      // Build worker configurations first (needed for supervisor prompt)
      const workerConfigs = await this.configuration.buildWorkerConfigurations({
        jobData,
        replyToMessagesContext,
        threadMediaAttachments,
        threadKnowledgeFileAttachments,
      });

      // Build supervisor configuration with worker configs available
      const supervisorConfig =
        await this.configuration.buildSupervisorConfiguration({
          jobData,
          workerConfigs,
          replyToMessagesContext,
          threadMediaAttachments,
          threadKnowledgeFileAttachments,
        });

      // 4. Execute LangGraph with streaming and cancellation monitoring

      const graphConfigurable =
        this.executionCoordinator.buildGraphConfigurable({
          jobData,
          supervisorConfig,
          workerConfigs,
          platformThreadId,
        });

      await this.executeLangGraph({
        jobData,
        langGraphInput: input,
        graphConfigurable,
        streamKey: jobData.keys.streamKey,
      });

      // 6. Mark messages as processed after successful execution
      const messageIds = userMessages.map((msg) => msg.id);
      await this.messageOperations.markMessagesAsProcessed(
        messageIds,
        jobData.threadId,
      );

      // ✅ REMOVED: Run completion now handled by PostProcessingService
      // This was causing double completion - PostProcessingService already completes the run
    } catch (error) {
      this.logger.error(`Error processing run ${jobData.runId}:`, error);

      // Classify error type and handle accordingly for double-texting
      const errorType = this.analyzeError(error);

      if (errorType === 'user_abort') {
        this.logger.log(
          `User aborted processing for thread: ${jobData.threadId}`,
        );
        await this.eventProcessor.processLangGraphEvent(
          { event: StreamEventType.RUN_CANCELLED, reason: 'user_abort' },
          jobData,
          jobData.keys.streamKey,
          null,
        );
        // User pressed abort button - run already marked as cancelled
        return;
      }

      // ✅ NEW: Mark run as failed for non-abort errors
      await this.runStatusService.failRun(
        platformThreadId,
        jobData.runId,
        error.message || 'Unknown error',
        error.stack,
      );

      // publish RUN_ERROR event
      await this.eventProcessor.processLangGraphEvent(
        { event: StreamEventType.RUN_ERROR, runId: jobData.runId },
        jobData,
        jobData.keys.streamKey,
        null,
      );

      // Real error - let it bubble up for BullMQ error handling
      throw error;
    } finally {
      // Basic cleanup logging - run status is managed in try/catch blocks
      this.logger.debug('Finished handleSupervisorWorkers', {
        threadId: jobData.threadId,
        runId: jobData.runId,
      });
    }
  }

  /**
   * Execute LangGraph with streaming, cancellation monitoring, and token usage tracking
   * Includes user billing after completion
   */
  private async executeLangGraph(param: {
    jobData: InAppJobData;
    langGraphInput: ExecutionInput;
    graphConfigurable: InAppSupervisorWorkersConfig;
    streamKey: string;
  }): Promise<void> {
    const { jobData, langGraphInput, graphConfigurable, streamKey } = param;
    // Emit RunStarted event
    await this.eventProcessor.processLangGraphEvent(
      { event: StreamEventType.RUN_STARTED, runId: jobData.runId },
      jobData,
      streamKey,
      null,
    );

    this.logger.log(`Emitting RUN_STARTED event for run: ${jobData.runId}`);

    // Setup execution parameters and components
    const { tokenUsageCollector, input, tags, abortController } =
      await this.executionCoordinator.setupLangGraphExecution(
        jobData,
        langGraphInput,
        graphConfigurable,
      );

    let currentMessageId: string | null = null;

    try {
      // Execute LangGraph with streaming and token usage collection
      const stream = this.supervisorWorkersGraph.executeStream({
        input,
        config: graphConfigurable,
        tags,
        callbacks: [tokenUsageCollector],
        signal: abortController.signal,
      });

      for await (const event of stream) {
        // ✅ NEW: Check for cancellation using unified status
        const cancelled = await this.runStatusService.isRunCancelled(
          jobData.keys.platformThreadId,
        );
        if (cancelled) {
          this.logger.log(
            `Thread ${jobData.threadId} cancelled via RunStatusService`,
            {
              threadId: jobData.threadId,
              runId: jobData.runId,
            },
          );

          // Abort the LangGraph execution
          abortController.abort();
        }

        // Process each LangGraph event
        const result = await this.eventProcessor.processLangGraphEvent(
          event,
          jobData,
          streamKey,
          currentMessageId,
        );
        if (result.newMessageId !== undefined) {
          currentMessageId = result.newMessageId;
        }
        if (result.messageUpdated) {
          tokenUsageCollector.clearAccumulatedText(jobData.mainAgentId);
        }
      }

      this.logger.log(
        `Completed LangGraph execution for run: ${jobData.runId}`,
      );

      // Emit RunComplete event on successful completion
      const usageSummary = tokenUsageCollector.getUsageSummary();
      await this.eventProcessor.processLangGraphEvent(
        {
          event: StreamEventType.RUN_COMPLETE,
          totalCost: usageSummary.totalCost,
        },
        jobData,
        streamKey,
        null,
      );
    } catch (error) {
      // Don't emit RUN_ERROR for cancellation errors - they already emitted RUN_CANCELLED
      // use reason instead
      if (error.message !== 'Aborted') {
        this.logger.error(
          `LangGraph execution failed for run ${jobData.runId}:`,
          error,
        );

        // Emit RunError event only for real errors
        await this.eventProcessor.processLangGraphEvent(
          {
            event: StreamEventType.RUN_ERROR,
            error: {
              type: error.constructor.name || 'Error',
              message: error.message || 'Unknown error',
              code: error.code,
              stack: error.stack,
            },
          },
          jobData,
          streamKey,
          null,
        );
      } else {
        // LangGraph abort - handle gracefully without trying to determine reason
        this.logger.debug('Job was cancelled/aborted', {
          runId: jobData.runId,
          threadId: jobData.threadId,
        });

        // Always emit RUN_CANCELLED for any abort
        await this.eventProcessor.processLangGraphEvent(
          { event: StreamEventType.RUN_CANCELLED, reason: 'aborted' },
          jobData,
          streamKey,
          null,
        );

        // Throw a generic cancellation error
        throw new UserAbortError(jobData.threadId, 'Job was cancelled');
      }
      throw error;
    } finally {
      // Post-stream operations - always execute regardless of success/failure
      await this.postProcessing.performPostStreamOperations(
        jobData,
        tokenUsageCollector,
        currentMessageId,
      );
    }
  }

  /**
   * Analyze error type to determine handling strategy for double-texting
   * Uses custom error types for proper classification
   */
  private analyzeError(error: any): 'real_error' | 'user_abort' {
    // Check custom error types
    if (error instanceof UserAbortError) {
      return 'user_abort';
    }

    // Real error - not a cancellation
    return 'real_error';
  }

  /**
   * 🔧 UPDATED: Now validates new runStatusKey pattern instead of activeRunKey/cancelKey
   * Validates both format AND components match jobData threadId/runId
   * 🔒 SECURITY: Ensures worker receives valid keys before processing
   */
  private validateJobDataKeys(jobData: InAppJobData): void {
    const { threadId, runId } = jobData;

    const JobDataKeysSchema = z.object({
      keys: z.object({
        platformThreadId: z.literal(`in_app:${threadId}`),
        runStatusKey: z.literal(`run_status:in_app:${threadId}`),
        streamKey: z.literal(`in_app:agent_stream:${threadId}:${runId}`),
      }),
    });

    JobDataKeysSchema.parse(jobData);

    this.logger.debug('Job data keys validation passed', {
      threadId,
      runId,
      runStatusKey: jobData.keys.runStatusKey,
    });
  }
}
