import { Injectable, Logger } from '@nestjs/common';
import { IExecutionPayload } from '../dto/node-execute.dto';
import { ConnectionRepository } from '../repositories/connection.repository';
import { ExecutionNodeDataRepository } from '../repositories/execution-node-data.repository';
import { NodeDefinitionRepository } from '../repositories/node-definition.repository';
import { NodeRepository } from '../repositories/node.repository';
import { WorkflowRepository } from '../repositories/workflow.repository';
import { RedisPublisherService } from '../services/redis-publisher.service';
import { MachineIntegrationService } from '../services/xstate/machines/machine-integration.service';
import { ExecutionRepository } from './../repositories/execution.repository';

@Injectable()
export class UserWorkflowService {
    private readonly logger = new Logger(UserWorkflowService.name);

    constructor(
        private readonly executionNodeDataRepository: ExecutionNodeDataRepository,
        private readonly executionRepository: ExecutionRepository,
        private readonly workflowRepository: WorkflowRepository,
        private readonly nodeRepository: NodeRepository,
        private readonly connectionRepository: ConnectionRepository,
        private readonly nodeDefinitionRepository: NodeDefinitionRepository,
        private readonly machineIntegrationService: MachineIntegrationService,
        private readonly redisPublisher: RedisPublisherService,
    ) { }



    /**
     * Execute single node synchronously - Controller đợi kết quả
     * Dùng cho: Single node execution từ BE APP
     */
    async executeNodeSync(data: IExecutionPayload): Promise<any> {
        const { userId, workflowId, currentNode, executionId } = data;

        if (!userId) {
            throw new Error('userId is required for node execution');
        }

        if (!currentNode) {
            throw new Error('currentNode is required for single node execution');
        }

        this.logger.log(`[SYNC] Executing single node: ${currentNode} in workflow: ${workflowId}`);

        try {
            // Execute node và đợi kết quả
            const result = await this.executeNodeInternal(
                executionId,
                workflowId,
                currentNode,
                userId,
                false, // isTestMode
                data.initContext,
                'REALTIME' // Always realtime cho sync execution
            );

            this.logger.log(`[SYNC] Single node execution completed: ${currentNode}`);

            // Return clean result cho BE APP
            return {
                success: true,
                nodeId: currentNode,
                executionId,
                result: result.output,
                executionTime: result.executionTime,
                timestamp: Date.now()
            };

        } catch (error) {
            this.logger.error(`[SYNC] Single node execution failed: ${currentNode}`, error);
            throw error;
        }
    }

    /**
     * Execute node asynchronously với complete lifecycle events
     * Dùng cho: Workflow execution trong background
     */
    async executeNodeAsync(
        data: IExecutionPayload,
        executionMode: 'REALTIME' | 'BACKGROUND' = 'BACKGROUND'
    ): Promise<{
        success: boolean;
        result?: any;
        error?: any;
    }> {
        const { userId, workflowId, currentNode, executionId } = data;

        if (!userId) {
            throw new Error('userId is required for node execution');
        }

        const nodeId = currentNode || 'unknown_node';
        this.logger.log(`[ASYNC] Starting async node execution: ${nodeId} in workflow: ${workflowId}`);

        try {
            // 1. Publish NODE_STARTED event
            if (executionMode === 'REALTIME') {
                await this.publishNodeEvent('NODE_STARTED', {
                    nodeId,
                    workflowId,
                    userId,
                    executionId,
                    timestamp: Date.now(),
                    context: data.initContext
                });
            }

            // 2. Publish NODE_PROCESSING event
            if (executionMode === 'REALTIME') {
                await this.publishNodeEvent('NODE_PROCESSING', {
                    nodeId,
                    workflowId,
                    userId,
                    executionId,
                    timestamp: Date.now(),
                    status: 'processing'
                });
            }

            // 3. Execute node internal
            const result = await this.executeNodeInternal(
                executionId,
                workflowId,
                nodeId,
                userId,
                false, // isTestMode
                data.initContext,
                executionMode
            );

            // 4. Publish NODE_COMPLETED event
            if (executionMode === 'REALTIME') {
                await this.publishNodeEvent('NODE_COMPLETED', {
                    nodeId,
                    workflowId,
                    userId,
                    executionId,
                    timestamp: Date.now(),
                    result: result.output,
                    executionTime: result.executionTime,
                    status: 'completed'
                });
            }

            this.logger.log(`[ASYNC] Async node execution completed: ${nodeId}`);

            return {
                success: true,
                result
            };

        } catch (error) {
            this.logger.error(`[ASYNC] Async node execution failed: ${nodeId}`, error);

            // 5. Publish NODE_FAILED event
            if (executionMode === 'REALTIME') {
                await this.publishNodeEvent('NODE_FAILED', {
                    nodeId,
                    workflowId,
                    userId,
                    executionId,
                    timestamp: Date.now(),
                    error: error.message,
                    status: 'failed'
                });
            }

            return {
                success: false,
                error
            };
        }
    }

    /**
     * Execute node với execution mode cụ thể (LEGACY - for backward compatibility)
     */
    async executeNodeWithMode(data: IExecutionPayload, executionMode: 'REALTIME' | 'BACKGROUND'): Promise<any> {
        const { userId, workflowId, currentNode, executionId } = data;

        if (!userId) {
            throw new Error('userId is required for node execution');
        }

        try {
            // Execute node với execution mode
            const result = await this.executeNodeInternal(
                executionId,
                workflowId,
                currentNode || 'unknown_node',
                userId,
                false, // isTestMode
                data.initContext, // Pass initContext as inputData
                executionMode // Pass execution mode
            );

            this.logger.log(`[${executionMode}] Node execution completed: ${currentNode}`);
            return { ...result, executionId, executionMode };

        } catch (error) {
            this.logger.error(`[${executionMode}] Node execution failed: ${currentNode}`, error);
            throw error;
        }
    }

    /**
     * Execute workflow với execution mode cụ thể (REALTIME hoặc BACKGROUND)
     * Supports both full workflow và workflow from startNode với complete lifecycle events
     */
    async executeWorkflowWithMode(data: IExecutionPayload, executionMode: 'REALTIME' | 'BACKGROUND'): Promise<any> {
        const { userId, workflowId, executionId, startNode } = data;

        if (!userId) {
            throw new Error('userId is required for workflow execution');
        }

        const executionType = startNode ? `workflow from start node: ${startNode}` : 'full workflow';
        this.logger.log(`[${executionMode}] Executing ${executionType}: ${workflowId} for user: ${userId}`);

        try {
            // 1. Publish WORKFLOW_STARTED event
            if (executionMode === 'REALTIME') {
                await this.publishWorkflowEvent('WORKFLOW_STARTED', {
                    workflowId,
                    userId,
                    executionId,
                    startNode,
                    timestamp: Date.now(),
                    isPartialExecution: !!startNode
                });
            }

            // 2. Execute workflow với execution mode và startNode support
            const result = await this.executeWorkflowInternal(
                executionId,
                workflowId,
                userId,
                data.initContext, // Pass initContext as inputData
                executionMode, // Pass execution mode
                startNode // Pass startNode for optimized execution
            );

            // 3. Publish WORKFLOW_COMPLETED event
            if (executionMode === 'REALTIME') {
                await this.publishWorkflowEvent('WORKFLOW_COMPLETED', {
                    workflowId,
                    userId,
                    executionId,
                    startNode,
                    timestamp: Date.now(),
                    result,
                    totalNodes: result.totalNodesExecuted || 0,
                    executionTime: result.executionTime || 0,
                    isPartialExecution: !!startNode
                });
            }

            this.logger.log(`[${executionMode}] Workflow execution completed: ${workflowId}`);
            return { ...result, executionId, executionMode, startNode };

        } catch (error) {
            this.logger.error(`[${executionMode}] Workflow execution failed: ${workflowId}`, error);

            // 4. Publish WORKFLOW_FAILED event
            if (executionMode === 'REALTIME') {
                await this.publishWorkflowEvent('WORKFLOW_FAILED', {
                    workflowId,
                    userId,
                    executionId,
                    startNode,
                    timestamp: Date.now(),
                    error: error.message,
                    isPartialExecution: !!startNode
                });
            }

            throw error;
        }
    }

    /**
     * Internal method để execute một node cụ thể với execution mode
     */
    private async executeNodeInternal(
        executionId: string,
        workflowId: string,
        nodeId: string,
        userId: number,
        inputData?: Record<string, any>
    ): Promise<any> {
        // Load node information để get name (outside try block để accessible trong catch)
        const nodeEntity = await this.nodeRepository.findById(nodeId);

        if (!nodeEntity) {
            throw new Error(`Node not found: ${nodeId}`);
        }

        const nodeName = nodeEntity?.name;

        try {
            await this.redisPublisher.publishTestProgress(executionId, userId, {
                currentStep: 1,
                totalSteps: 1,
                percentage: 0,
                description: `Starting node execution: ${nodeId}`,
                nodeId
            });

            // Mock node execution - trong thực tế sẽ sử dụng XState machine
            const startTime = Date.now();

            // Simulate node execution with context
            await new Promise(resolve => setTimeout(resolve, 1000));

            const executionTime = Date.now() - startTime;
            const result = {
                success: true,
                output: {
                    nodeId,
                    message: `Node ${nodeId} executed successfully`,
                    timestamp: Date.now(),
                    isTestMode,
                    inputContext: executionContext,
                    processedData: this.processNodeWithContext(nodeEntity, executionContext)
                },
                executionTime,
                metadata: {
                    nodeId,
                    executionId,
                    workflowId,
                    userId,
                    isTestMode,
                    hasInputData: !!inputData,
                    contextKeys: Object.keys(executionContext)
                }
            };

            return result;

        } catch (error) {
            this.logger.error(`[${}] Node execution internal failed: ${nodeId}`, error);

            const errorResult = {
                success: false,
                error: {
                    message: error.message,
                    code: 'NODE_EXECUTION_ERROR',
                    nodeId,
                    executionId
                },
                executionTime: Date.now() - Date.now()
            };

            // Save error if not test mode
            if (!isTestMode) {
                await this.executionNodeDataRepository.create({
                    executionId,
                    nodeName: nodeName,
                    inputData: { nodeId, workflowId },
                    outputData: { error: error.message },
                    executedAt: Date.now(),
                });
            }

            return errorResult;
        }
    }

    /**
     * Internal method để execute toàn bộ workflow với execution mode
     */
    private async executeWorkflowInternal(
        executionId: string,
        workflowId: string,
        userId: number,
        inputData?: Record<string, any>,
        executionMode?: 'REALTIME' | 'BACKGROUND'
    ): Promise<any>;

    /**
     * Internal method để execute workflow với execution mode và startNode support
     */
    private async executeWorkflowInternal(
        executionId: string,
        workflowId: string,
        userId: number,
        inputData?: Record<string, any>,
        executionMode?: 'REALTIME' | 'BACKGROUND',
        startNode?: string | null
    ): Promise<any>;

    /**
     * Internal method để execute toàn bộ workflow sử dụng XState (legacy signature)
     */
    private async executeWorkflowInternal(
        executionId: string,
        workflowId: string,
        userId: number,
        isTestMode: boolean,
        inputData?: Record<string, any>
    ): Promise<any>;

    /**
     * Implementation của executeWorkflowInternal với startNode support
     */
    private async executeWorkflowInternal(
        executionId: string,
        workflowId: string,
        userId: number,
        inputDataOrTestMode?: Record<string, any> | boolean,
        executionModeOrInputData?: 'REALTIME' | 'BACKGROUND' | Record<string, any>,
        startNode?: string | null
    ): Promise<any> {
        // Determine parameters based on signature
        let isTestMode: boolean;
        let inputData: Record<string, any> | undefined;
        let executionMode: 'REALTIME' | 'BACKGROUND' = 'BACKGROUND';

        if (typeof inputDataOrTestMode === 'boolean') {
            // Legacy signature: (executionId, workflowId, userId, isTestMode, inputData)
            isTestMode = inputDataOrTestMode;
            inputData = executionModeOrInputData as Record<string, any>;
        } else {
            // New signature: (executionId, workflowId, userId, inputData, executionMode, startNode)
            isTestMode = false;
            inputData = inputDataOrTestMode;
            executionMode = (executionModeOrInputData as 'REALTIME' | 'BACKGROUND') || 'BACKGROUND';
        }

        const executionType = startNode ? `from start node: ${startNode}` : 'full workflow';
        this.logger.log(`[${executionMode}] Executing workflow internal with XState: ${workflowId} (${executionType}), test mode: ${isTestMode}, with inputData: ${!!inputData}`);

        try {
            const startTime = Date.now();

            // Extract trigger context from inputData
            const triggerContext = this.extractExecutionContext(inputData);

            // 1. Load workflow definition từ database (with startNode optimization)
            const workflowDefinition = startNode
                ? await this.loadWorkflowDefinitionHybrid(workflowId, startNode)
                : await this.loadWorkflowDefinition(workflowId);

            // 1.5. Validate workflow definition
            this.validateWorkflowDefinition(workflowDefinition);

            // 2. Create WorkflowContext cho XState machine với trigger context
            const workflowContext = await this.createWorkflowContext(
                executionId,
                workflowId,
                userId,
                workflowDefinition,
                isTestMode,
                triggerContext
            );

            // 3. Publish workflow started (only for REALTIME mode or test mode)
            const shouldPublishEvents = isTestMode || executionMode === 'REALTIME';

            if (shouldPublishEvents) {
                if (isTestMode) {
                    await this.redisPublisher.publishTestStarted(executionId, userId, {
                        workflowId,
                        mode: 'xstate_workflow',
                        totalNodes: workflowContext.metadata?.totalNodes || 0
                    });
                } else {
                    await this.redisPublisher.publishExecutionStarted(executionId, userId, {
                        workflowId,
                        mode: 'xstate_workflow',
                        totalNodes: workflowContext.metadata?.totalNodes || 0,
                        executionMode
                    });
                }
            } else {
                this.logger.log(`[${executionMode}] Skipping workflow started event for background execution`);
            }

            // 4. Start XState workflow machine
            const workflowActor = await this.machineIntegrationService.startWorkflow(workflowContext);

            // 5. Wait for workflow completion
            const result = await this.waitForWorkflowCompletion(workflowActor, executionId, userId, isTestMode);

            const executionTime = Date.now() - startTime;

            return {
                ...result,
                executionTime,
                metadata: {
                    executionId,
                    workflowId,
                    userId,
                    isTestMode,
                    usedXState: true
                }
            };

        } catch (error) {
            this.logger.error(`[${executionMode}] XState workflow execution failed: ${workflowId}`, error);

            // Publish error (only for REALTIME mode or test mode)
            const shouldPublishEvents = isTestMode || executionMode === 'REALTIME';

            if (shouldPublishEvents) {
                const errorType = isTestMode ? 'test' : 'execution';
                await this.redisPublisher.publishError(executionId, userId, error, errorType);
            } else {
                this.logger.log(`[${executionMode}] Skipping error event for background execution`);
            }

            return {
                success: false,
                error: {
                    message: error.message,
                    code: 'XSTATE_WORKFLOW_ERROR',
                    workflowId,
                    executionId
                },
                executionTime: Date.now() - Date.now(),
                nodesExecuted: 0,
                totalNodes: 0
            };
        }
    }

    /**
     * Load workflow definition từ database
     */
    private async loadWorkflowDefinition(workflowId: string): Promise<any> {
        this.logger.debug(`Loading workflow definition for: ${workflowId}`);

        try {
            // 1. Load workflow metadata
            const workflow = await this.workflowRepository.findById(workflowId);
            if (!workflow) {
                throw new Error(`Workflow not found: ${workflowId}`);
            }

            if (!workflow.isActive) {
                throw new Error(`Workflow is not active: ${workflowId}`);
            }

            // 2. Load all nodes for this workflow
            const nodes = await this.nodeRepository.findByWorkflowId(workflowId);
            if (nodes.length === 0) {
                throw new Error(`No nodes found for workflow: ${workflowId}`);
            }

            // 3. Load all connections for this workflow
            const connections = await this.connectionRepository.findByWorkflowId(workflowId);

            // 4. Load node definitions for each node to get type information
            const nodeDefinitions = new Map();
            for (const node of nodes) {
                if (node.nodeDefinitionId) {
                    const nodeDefinition = await this.nodeDefinitionRepository.findById(node.nodeDefinitionId);
                    if (nodeDefinition) {
                        nodeDefinitions.set(node.id, nodeDefinition);
                    }
                }
            }

            // 5. Build dependency graph from connections
            const dependencyGraph = this.buildDependencyGraph(nodes, connections);

            // 6. Identify trigger nodes (nodes without dependencies)
            const triggerNodes = nodes.filter(node => {
                const dependencies = dependencyGraph.get(node.id) || [];
                return dependencies.length === 0;
            });

            // 7. Identify processing nodes (nodes with dependencies)
            const processingNodes = nodes.filter(node => {
                const dependencies = dependencyGraph.get(node.id) || [];
                return dependencies.length > 0;
            });

            this.logger.debug(`Loaded workflow: ${workflow.name}, Nodes: ${nodes.length}, Connections: ${connections.length}`);
            this.logger.debug(`Trigger nodes: ${triggerNodes.length}, Processing nodes: ${processingNodes.length}`);

            return {
                id: workflow.id,
                name: workflow.name,
                isActive: workflow.isActive,
                settings: workflow.settings,
                nodes: nodes.map(node => ({
                    id: node.id,
                    name: node.name,
                    workflowId: node.workflowId,
                    position: node.position,
                    parameters: node.parameters,
                    disabled: node.disabled,
                    notes: node.notes,
                    retryOnFail: node.retryOnFail,
                    maxTries: node.maxTries,
                    waitBetweenTries: node.waitBetweenTries,
                    onError: node.onError,
                    agentId: node.agentId,
                    integrationId: node.integrationId,
                    nodeDefinitionId: node.nodeDefinitionId,
                    // Add computed fields
                    dependencies: dependencyGraph.get(node.id) || [],
                    nodeDefinition: nodeDefinitions.get(node.id),
                    isTriggerNode: triggerNodes.some(tn => tn.id === node.id),
                    isProcessingNode: processingNodes.some(pn => pn.id === node.id)
                })),
                connections: connections.map(conn => ({
                    id: conn.id,
                    workflowId: conn.workflowId,
                    from: conn.sourceNodeId,
                    to: conn.targetNodeId,
                    sourceHandle: conn.sourceHandle,
                    targetHandle: conn.targetHandle,
                    sourceHandleIndex: conn.sourceHandleIndex,
                    targetHandleIndex: conn.targetHandleIndex
                })),
                triggerNodes: triggerNodes.map(node => node.id),
                processingNodes: processingNodes.map(node => node.id),
                dependencyGraph,
                metadata: {
                    totalNodes: nodes.length,
                    totalConnections: connections.length,
                    triggerNodeCount: triggerNodes.length,
                    processingNodeCount: processingNodes.length,
                    loadedAt: Date.now()
                }
            };

        } catch (error) {
            this.logger.error(`Failed to load workflow definition: ${workflowId}`, error);
            throw error;
        }
    }

    /**
     * Load workflow definition với hybrid approach (Load All + Start Node filtering)
     */
    private async loadWorkflowDefinitionHybrid(
        workflowId: string,
        startNodeId: string
    ): Promise<any> {
        this.logger.debug(`Loading workflow (hybrid): ${workflowId}, startNode: ${startNodeId}`);

        try {
            // 1. Load workflow metadata
            const workflow = await this.workflowRepository.findById(workflowId);
            if (!workflow) {
                throw new Error(`Workflow not found: ${workflowId}`);
            }

            if (!workflow.isActive) {
                throw new Error(`Workflow is not active: ${workflowId}`);
            }

            // 2. Load ALL nodes với essential fields
            const allNodes = await this.nodeRepository.findByWorkflowId(workflowId);
            if (allNodes.length === 0) {
                throw new Error(`No nodes found for workflow: ${workflowId}`);
            }

            // 3. Load ALL connections
            const allConnections = await this.connectionRepository.findByWorkflowId(workflowId);

            // 4. Filter active nodes
            const activeNodes = allNodes.filter(node => !node.disabled);

            // 5. Build complete dependency graph
            const fullDependencyGraph = this.buildDependencyGraph(activeNodes, allConnections);

            // 6. Apply start node filtering
            const {
                executionNodes,
                executionGraph,
                executionConnections
            } = this.filterFromStartNode(activeNodes, allConnections, fullDependencyGraph, startNodeId);

            // 7. Load node definitions for execution nodes only
            const nodeDefinitions = new Map();
            for (const node of executionNodes) {
                if (node.nodeDefinitionId) {
                    const nodeDefinition = await this.nodeDefinitionRepository.findById(node.nodeDefinitionId);
                    if (nodeDefinition) {
                        nodeDefinitions.set(node.id, nodeDefinition);
                    }
                }
            }

            // 8. Identify processing nodes (exclude triggers)
            const processingNodes = executionNodes.filter(node => {
                const deps = executionGraph.get(node.id) || [];
                return deps.length > 0;
            });

            // 9. Identify trigger nodes
            const triggerNodes = executionNodes.filter(node => {
                const deps = executionGraph.get(node.id) || [];
                return deps.length === 0;
            });

            this.logger.debug(`Hybrid workflow loaded: ${workflow.name}`);
            this.logger.debug(`Execution scope: ${executionNodes.length}/${activeNodes.length} nodes, ${executionConnections.length}/${allConnections.length} connections`);
            this.logger.debug(`Processing nodes: ${processingNodes.length}, Trigger nodes: ${triggerNodes.length}`);

            return {
                id: workflow.id,
                name: workflow.name,
                isActive: workflow.isActive,
                settings: workflow.settings,
                startNodeId,
                nodes: executionNodes.map(node => ({
                    id: node.id,
                    name: node.name,
                    workflowId: node.workflowId,
                    position: node.position,
                    parameters: node.parameters,
                    disabled: node.disabled,
                    notes: node.notes,
                    retryOnFail: node.retryOnFail,
                    maxTries: node.maxTries,
                    waitBetweenTries: node.waitBetweenTries,
                    onError: node.onError,
                    agentId: node.agentId,
                    integrationId: node.integrationId,
                    nodeDefinitionId: node.nodeDefinitionId,
                    // Add computed fields
                    dependencies: executionGraph.get(node.id) || [],
                    nodeDefinition: nodeDefinitions.get(node.id),
                    isTriggerNode: triggerNodes.some(tn => tn.id === node.id),
                    isProcessingNode: processingNodes.some(pn => pn.id === node.id)
                })),
                connections: executionConnections.map(conn => ({
                    id: conn.id,
                    workflowId: conn.workflowId,
                    from: conn.sourceNodeId,
                    to: conn.targetNodeId,
                    sourceHandle: conn.sourceHandle,
                    targetHandle: conn.targetHandle,
                    sourceHandleIndex: conn.sourceHandleIndex,
                    targetHandleIndex: conn.targetHandleIndex
                })),
                triggerNodes: triggerNodes.map(node => node.id),
                processingNodes: processingNodes.map(node => node.id),
                dependencyGraph: executionGraph,
                metadata: {
                    totalWorkflowNodes: activeNodes.length,
                    executionNodes: executionNodes.length,
                    totalConnections: allConnections.length,
                    executionConnections: executionConnections.length,
                    triggerNodeCount: triggerNodes.length,
                    processingNodeCount: processingNodes.length,
                    optimizationRatio: Math.round((1 - executionNodes.length / activeNodes.length) * 100),
                    loadedAt: Date.now()
                }
            };

        } catch (error) {
            this.logger.error(`Failed to load hybrid workflow definition: ${workflowId}`, error);
            throw error;
        }
    }

    /**
     * Filter execution scope từ start node
     */
    private filterFromStartNode(
        allNodes: any[],
        allConnections: any[],
        fullGraph: Map<string, string[]>,
        startNodeId: string
    ): {
        executionNodes: any[],
        executionGraph: Map<string, string[]>,
        executionConnections: any[]
    } {
        this.logger.debug(`Filtering execution scope from start node: ${startNodeId}`);

        // 1. Validate start node exists
        const startNode = allNodes.find((node: any) => node.id === startNodeId);
        if (!startNode) {
            throw new Error(`Start node not found: ${startNodeId}`);
        }

        // 2. Build forward adjacency list từ connections
        const forwardGraph = new Map<string, string[]>();
        allConnections.forEach((conn: any) => {
            const source = conn.sourceNodeId;
            const target = conn.targetNodeId;

            if (!forwardGraph.has(source)) {
                forwardGraph.set(source, []);
            }
            forwardGraph.get(source)!.push(target);
        });

        // 3. BFS forward traversal từ start node
        const reachableNodeIds = new Set<string>();
        const queue = [startNodeId];
        reachableNodeIds.add(startNodeId);

        while (queue.length > 0) {
            const currentNodeId = queue.shift()!;
            const nextNodes = forwardGraph.get(currentNodeId) || [];

            for (const nextNodeId of nextNodes) {
                if (!reachableNodeIds.has(nextNodeId)) {
                    reachableNodeIds.add(nextNodeId);
                    queue.push(nextNodeId);
                }
            }
        }

        // 4. Filter nodes và connections cho execution scope
        const executionNodes = allNodes.filter((node: any) => reachableNodeIds.has(node.id));

        const executionConnections = allConnections.filter((conn: any) =>
            reachableNodeIds.has(conn.sourceNodeId) && reachableNodeIds.has(conn.targetNodeId)
        );

        // 5. Build filtered dependency graph
        const executionGraph = new Map<string, string[]>();
        executionNodes.forEach((node: any) => {
            const originalDeps = fullGraph.get(node.id) || [];
            const filteredDeps = originalDeps.filter((depId: string) => reachableNodeIds.has(depId));
            executionGraph.set(node.id, filteredDeps);
        });

        this.logger.debug(`Execution scope: ${executionNodes.length}/${allNodes.length} nodes, ${executionConnections.length}/${allConnections.length} connections`);

        return {
            executionNodes,
            executionGraph,
            executionConnections
        };
    }

    /**
     * Create WorkflowContext cho XState machine
     */
    private async createWorkflowContext(
        executionId: string,
        workflowId: string,
        userId: number,
        workflowDefinition: any,
        isTestMode: boolean,
        triggerContext?: Record<string, any>
    ): Promise<any> {
        // Create nodes map
        const nodes = new Map();
        const dependencyGraph = new Map();

        // Process trigger node outputs - chỉ là outputs đơn giản từ BE App
        const triggerNodeOutputs = new Map();

        // Xử lý triggerNodeOutputs array
        if (triggerContext?.triggerNodeOutputs && Array.isArray(triggerContext.triggerNodeOutputs)) {
            for (const output of triggerContext.triggerNodeOutputs) {
                const nodeId = output.nodeId || `trigger_${Date.now()}`;
                triggerNodeOutputs.set(nodeId, {
                    nodeId,
                    outputData: output.outputData || output, // Flexible: có thể là object hoặc direct data
                    success: output.success !== false, // Default true
                    processedAt: output.processedAt || Date.now()
                });
            }
        }

        // Xử lý nodeOutputs object (alternative format)
        if (triggerContext?.nodeOutputs && typeof triggerContext.nodeOutputs === 'object') {
            for (const [nodeId, outputData] of Object.entries(triggerContext.nodeOutputs)) {
                triggerNodeOutputs.set(nodeId, {
                    nodeId,
                    outputData,
                    success: true,
                    processedAt: Date.now()
                });
            }
        }

        // Process workflow definition
        for (const node of workflowDefinition.nodes) {
            // Check if this is a trigger node with pre-computed output
            const triggerOutput = triggerNodeOutputs.get(node.id);
            const isTriggerNode = node.isTriggerNode || triggerOutput;

            nodes.set(node.id, {
                id: node.id,
                status: isTriggerNode ? 'completed' : 'pending', // Mark trigger nodes as completed
                inputData: isTriggerNode ? (triggerOutput?.triggerData || {}) : {},
                outputData: isTriggerNode ? (triggerOutput?.outputData || {}) : {},
                retryCount: 0,
                node: node,
                isTriggerNode,
                triggerOutput
            });

            dependencyGraph.set(node.id, node.dependencies || []);
        }

        // Determine initial ready nodes
        // For trigger context: processing nodes whose dependencies are satisfied by trigger outputs
        // For normal execution: nodes with no dependencies
        const readyNodes = this.determineReadyNodes(workflowDefinition.nodes, dependencyGraph, triggerNodeOutputs);

        const waitingNodes = workflowDefinition.nodes
            .filter((node: any) => node.dependencies && node.dependencies.length > 0)
            .map((node: any) => node.id);

        return {
            workflowId,
            executionId,
            nodes,
            connections: new Map(workflowDefinition.connections.map((c: any) => [c.from, c.to])),
            dependencyGraph,
            readyNodes,
            runningNodes: [],
            waitingNodes,
            executionData: new Map(),
            errors: new Map(),
            // Include trigger context
            triggerContext: triggerContext || {},
            triggerNodeOutputs,
            hasTriggerContext: !!triggerContext && Object.keys(triggerContext).length > 0,
            metadata: {
                startTime: Date.now(),
                userId,
                triggerType: 'manual', // Đơn giản hóa
                totalNodes: workflowDefinition.nodes.length,
                completedNodes: triggerNodeOutputs.size, // Số trigger nodes đã hoàn thành
                failedNodes: 0,
                processingNodes: workflowDefinition.nodes.length - triggerNodeOutputs.size,
                triggerNodes: triggerNodeOutputs.size,
                retryCount: 0,
                hasTriggerOutputs: triggerNodeOutputs.size > 0
            },
            options: {
                timeout: 3600000, // 1 hour
                maxConcurrency: 5,
                retryOnFailure: true
            },
            triggerData: {
                isTestMode,
                ...triggerContext?.triggerData
            }
        };
    }

    /**
     * Wait for workflow completion và handle events
     */
    private async waitForWorkflowCompletion(
        workflowActor: any,
        _executionId: string,
        _userId: number,
        _isTestMode: boolean
    ): Promise<any> {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Workflow execution timeout'));
            }, 300000); // 5 minutes timeout

            // Subscribe to workflow events
            workflowActor.subscribe({
                next: (snapshot: any) => {
                    this.logger.debug(`Workflow state: ${JSON.stringify(snapshot.value)}`);

                    // Check if workflow completed
                    if (snapshot.status === 'done') {
                        clearTimeout(timeout);

                        const context = snapshot.context;
                        const result = {
                            success: context.result?.success || false,
                            output: context.result?.output || {},
                            totalNodes: context.metadata?.totalNodes || 0,
                            nodesExecuted: context.metadata?.completedNodes || 0,
                            failedNodes: context.metadata?.failedNodes || 0,
                            errors: Object.fromEntries(context.errors || new Map())
                        };

                        resolve(result);
                    }
                },
                error: (error: any) => {
                    clearTimeout(timeout);
                    reject(error);
                },
                complete: () => {
                    this.logger.debug('Workflow actor completed');
                }
            });
        });
    }

    /**
     * Build dependency graph từ connections
     */
    private buildDependencyGraph(nodes: any[], connections: any[]): Map<string, string[]> {
        const dependencyGraph = new Map<string, string[]>();

        // Initialize all nodes with empty dependencies
        nodes.forEach(node => {
            dependencyGraph.set(node.id, []);
        });

        // Build dependencies from connections
        connections.forEach(connection => {
            const targetNodeId = connection.targetNodeId || connection.to;
            const sourceNodeId = connection.sourceNodeId || connection.from;

            if (targetNodeId && sourceNodeId) {
                const dependencies = dependencyGraph.get(targetNodeId) || [];
                if (!dependencies.includes(sourceNodeId)) {
                    dependencies.push(sourceNodeId);
                    dependencyGraph.set(targetNodeId, dependencies);
                }
            }
        });

        return dependencyGraph;
    }

    /**
     * Validate workflow definition trước khi execute
     */
    private validateWorkflowDefinition(workflowDefinition: any): void {
        if (!workflowDefinition.nodes || workflowDefinition.nodes.length === 0) {
            throw new Error('Workflow must have at least one node');
        }

        if (workflowDefinition.triggerNodes.length === 0) {
            throw new Error('Workflow must have at least one trigger node');
        }

        // Check for circular dependencies
        this.detectCircularDependencies(workflowDefinition.dependencyGraph);
    }

    /**
     * Detect circular dependencies trong workflow
     */
    private detectCircularDependencies(dependencyGraph: Map<string, string[]>): void {
        const visited = new Set<string>();
        const recursionStack = new Set<string>();

        const hasCycle = (nodeId: string): boolean => {
            if (recursionStack.has(nodeId)) {
                return true; // Circular dependency detected
            }

            if (visited.has(nodeId)) {
                return false; // Already processed
            }

            visited.add(nodeId);
            recursionStack.add(nodeId);

            const dependencies = dependencyGraph.get(nodeId) || [];
            for (const depId of dependencies) {
                if (hasCycle(depId)) {
                    return true;
                }
            }

            recursionStack.delete(nodeId);
            return false;
        };

        for (const nodeId of dependencyGraph.keys()) {
            if (hasCycle(nodeId)) {
                throw new Error(`Circular dependency detected involving node: ${nodeId}`);
            }
        }
    }

    /**
     * Extract execution context từ inputData
     * inputData chỉ chứa outputs của trigger nodes đã xử lý ở BE App
     */
    private extractExecutionContext(inputData?: Record<string, any>): Record<string, any> {
        if (!inputData) {
            return {};
        }

        return {
            // Trigger node outputs - đây là output của webhook, schedule nodes đã chạy ở BE App
            triggerNodeOutputs: inputData.triggerNodeOutputs || inputData.outputs || [],

            // Hoặc có thể là direct outputs theo nodeId
            nodeOutputs: inputData.nodeOutputs || {},

            // Context data đơn giản
            contextData: inputData.contextData || inputData.data || {},

            // Raw inputData để backward compatibility
            rawInputData: inputData
        };
    }

    /**
     * Process node với execution context (đơn giản hóa)
     */
    private processNodeWithContext(nodeEntity: any, executionContext: Record<string, any>): Record<string, any> {
        if (!nodeEntity) {
            return { error: 'Node entity not found' };
        }

        const processedData: Record<string, any> = {
            nodeInfo: {
                id: nodeEntity.id,
                name: nodeEntity.name,
                type: nodeEntity.nodeDefinitionId,
                parameters: nodeEntity.parameters
            },
            availableInputs: {
                // Outputs từ trigger nodes (webhook, schedule, etc.)
                triggerNodeOutputs: Array.isArray(executionContext.triggerNodeOutputs) ?
                    executionContext.triggerNodeOutputs.length : 0,

                // Direct node outputs
                nodeOutputs: Object.keys(executionContext.nodeOutputs || {}).length,

                // Context data
                hasContextData: !!executionContext.contextData && Object.keys(executionContext.contextData).length > 0
            }
        };

        // List available trigger node outputs
        if (Array.isArray(executionContext.triggerNodeOutputs) && executionContext.triggerNodeOutputs.length > 0) {
            processedData.triggerOutputsSummary = executionContext.triggerNodeOutputs.map((output: any) => ({
                nodeId: output.nodeId || 'unknown',
                outputKeys: Object.keys(output.outputData || output || {}),
                dataSize: JSON.stringify(output.outputData || output || {}).length
            }));
        }

        // List available node outputs
        if (executionContext.nodeOutputs && Object.keys(executionContext.nodeOutputs).length > 0) {
            processedData.nodeOutputsSummary = Object.keys(executionContext.nodeOutputs).map(nodeId => ({
                nodeId,
                outputKeys: Object.keys(executionContext.nodeOutputs[nodeId] || {}),
                dataSize: JSON.stringify(executionContext.nodeOutputs[nodeId] || {}).length
            }));
        }

        // Context data summary
        if (executionContext.contextData && Object.keys(executionContext.contextData).length > 0) {
            processedData.contextDataSummary = {
                keys: Object.keys(executionContext.contextData),
                dataTypes: Object.entries(executionContext.contextData).reduce((acc, [key, value]) => {
                    acc[key] = typeof value;
                    return acc;
                }, {} as Record<string, string>)
            };
        }

        return processedData;
    }

    /**
     * Determine ready nodes based on dependencies and trigger outputs
     */
    private determineReadyNodes(
        nodes: any[],
        dependencyGraph: Map<string, string[]>,
        triggerNodeOutputs: Map<string, any>
    ): string[] {
        const readyNodes: string[] = [];

        for (const node of nodes) {
            const dependencies = dependencyGraph.get(node.id) || [];

            // If node has no dependencies, it's ready
            if (dependencies.length === 0) {
                // Skip if it's a trigger node that's already processed
                if (!triggerNodeOutputs.has(node.id)) {
                    readyNodes.push(node.id);
                }
                continue;
            }

            // Check if all dependencies are satisfied
            const allDependenciesSatisfied = dependencies.every(depId => {
                // Dependency is satisfied if:
                // 1. It's a trigger node with output, OR
                // 2. It's a completed processing node (for future iterations)
                return triggerNodeOutputs.has(depId);
            });

            if (allDependenciesSatisfied) {
                readyNodes.push(node.id);
            }
        }

        return readyNodes;
    }

    /**
     * Publish node lifecycle events
     */
    private async publishNodeEvent(
        eventType: 'NODE_STARTED' | 'NODE_PROCESSING' | 'NODE_COMPLETED' | 'NODE_FAILED',
        eventData: {
            nodeId: string;
            workflowId: string;
            userId: number;
            executionId: string;
            timestamp: number;
            [key: string]: any;
        }
    ): Promise<void> {
        try {
            const channel = this.getNodeEventChannel(eventType, eventData.nodeId);

            await this.redisPublisher.publish(channel, {
                type: eventType,
                ...eventData
            });

            this.logger.debug(`Published ${eventType} event for node: ${eventData.nodeId}`);

        } catch (error) {
            this.logger.error(`Failed to publish ${eventType} event:`, error);
            // Don't throw - event publishing failure shouldn't fail execution
        }
    }

    /**
     * Publish workflow lifecycle events
     */
    private async publishWorkflowEvent(
        eventType: 'WORKFLOW_STARTED' | 'WORKFLOW_COMPLETED' | 'WORKFLOW_FAILED',
        eventData: {
            workflowId: string;
            userId: number;
            executionId: string;
            timestamp: number;
            [key: string]: any;
        }
    ): Promise<void> {
        try {
            const channel = this.getWorkflowEventChannel(eventType, eventData.workflowId);

            await this.redisPublisher.publish(channel, {
                type: eventType,
                ...eventData
            });

            this.logger.debug(`Published ${eventType} event for workflow: ${eventData.workflowId}`);

        } catch (error) {
            this.logger.error(`Failed to publish ${eventType} event:`, error);
        }
    }

    /**
     * Get Redis channel for node events
     */
    private getNodeEventChannel(eventType: string, nodeId: string): string {
        switch (eventType) {
            case 'NODE_STARTED':
                return `node.started.${nodeId}`;
            case 'NODE_PROCESSING':
                return `node.processing.${nodeId}`;
            case 'NODE_COMPLETED':
                return `node.completed.${nodeId}`;
            case 'NODE_FAILED':
                return `node.failed.${nodeId}`;
            default:
                throw new Error(`Unknown event type: ${eventType}`);
        }
    }

    /**
     * Get Redis channel for workflow events
     */
    private getWorkflowEventChannel(eventType: string, workflowId: string): string {
        switch (eventType) {
            case 'WORKFLOW_STARTED':
                return `workflow.started.${workflowId}`;
            case 'WORKFLOW_COMPLETED':
                return `workflow.completed.${workflowId}`;
            case 'WORKFLOW_FAILED':
                return `workflow.failed.${workflowId}`;
            default:
                throw new Error(`Unknown workflow event type: ${eventType}`);
        }
    }
}