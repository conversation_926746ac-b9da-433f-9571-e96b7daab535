# RAG Engine Service

This service provides integration with the Red.ai RAG Engine v2.0.1 API for searching files, media, and products.

## Configuration

Add these environment variables to your `.env` file:

```env
RAG_ENGINE_BASE_URL=http://localhost:8000
RAG_ENGINE_API_KEY=your_api_key_here
```

## Usage

### Inject the service

```typescript
import { RagEngineService } from '@/infra';

@Injectable()
export class YourService {
  constructor(private readonly ragEngineService: RagEngineService) {}
}
```

### Search Files

```typescript
// Basic file search
const fileResults = await this.ragEngineService.searchFiles({
  query: "hướng dẫn cấu hình database connection",
  threshold: 0.7,
  limit: 10,
  use_rerank: true
});

// Search specific files
const specificFileResults = await this.ragEngineService.searchFiles({
  query: "API authentication methods",
  file_ids: ["file-KBYXBHVZX8MMk4BP", "file-ABC123"],
  threshold: 0.75,
  limit: 5
});
```

### Search Media

```typescript
// Basic media search
const mediaResults = await this.ragEngineService.searchMedia({
  query: "giày thể thao Nike trắng",
  threshold: 0.7,
  limit: 10,
  use_rerank: true
});

// Search specific media type
const imageResults = await this.ragEngineService.searchMedia({
  query: "hình ảnh sản phẩm iPhone",
  media_type: "image",
  threshold: 0.8,
  limit: 15
});

// Search specific media IDs
const specificMediaResults = await this.ragEngineService.searchMedia({
  query: "product images",
  media_ids: ["media-XYZ789", "media-DEF456"],
  use_rerank: false
});
```

### Search Products

```typescript
// Auto product type recognition
const productResults = await this.ragEngineService.searchProducts({
  query: "giày thể thao Nike Air Max trắng size 42",
  threshold: 0.7,
  limit: 10,
  use_rerank: true,
  include_sub_products: true
});

// Search specific product types
const digitalProducts = await this.ragEngineService.searchProducts({
  query: "khóa học lập trình Python online",
  product_types: ["digital", "service"],
  threshold: 0.8,
  limit: 15,
  recognition_method: "auto",
  rerank_method: "auto"
});

// Advanced search with custom limits
const advancedResults = await this.ragEngineService.searchProducts({
  query: "laptop gaming RTX",
  product_types: ["physical"],
  threshold: 0.75,
  limit: 20,
  main_limit: 10,
  sub_limit: 30,
  include_sub_products: true
});
```

### Health Check

```typescript
const isHealthy = await this.ragEngineService.healthCheck();
if (!isHealthy) {
  console.log('RAG Engine service is not available');
}
```

## API Endpoints

- **POST /api/files/search** - Search content in files with dual-source search (files + media)
- **POST /api/media/search** - Search media through text (name, description, tags)
- **POST /api/products/search/** - Smart product search with AI recognition

## Features

- **Semantic Search**: Vector-based similarity search using Jina AI embeddings
- **AI Reranking**: Improved result ordering using Gemini/Jina models
- **Vietnamese Language Support**: Full support for Vietnamese queries
- **Flexible Filtering**: Filter by specific IDs, types, or let AI auto-detect
- **Performance Monitoring**: Detailed timing statistics for each operation
- **Error Handling**: Comprehensive error handling with detailed logging

## Response Format

All search methods return responses with this structure:

```typescript
{
  success: boolean;
  message: string;
  query: string;
  results: any[];
  statistics?: any;
  performance?: any;
  ai_recognition?: any; // Only for product search
}
```
