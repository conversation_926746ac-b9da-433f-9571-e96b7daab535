import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { InputModality, ModelFeature, OutputModality, ProviderLlmEnum, SamplingParameter } from '../../enums';
import { ModelPricingInterface } from '../../interfaces';

/**
 * Entity đại diện cho bảng model_registry trong cơ sở dữ liệu
 * Lưu thông tin cấu hình mô hình (input/output, sampling, feature, ...)
 */
@Entity('model_registry')
export class ModelRegistry {
  /**
   * UUID của registry, sinh tự động
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Nhà cung cấp model
   */
  @Column({ name: 'provider', type: 'enum', enum: ProviderLlmEnum, default: ProviderLlmEnum.OPENAI, nullable: false })
  provider: ProviderLlmEnum;

  /**
   * Tên mẫu đại diện của model
   */
  @Column({ name: 'model_base_id', type: 'varchar', length: 255, nullable: false })
  modelBaseId: string;

  /**
   * Các loại dữ liệu đầu vào hỗ trợ (text, image, audio,...)
   */
  @Column({ name: 'input_modalities_base', type: 'jsonb', default: '[]' })
  inputModalitiesBase: InputModality[];

    /**
   * Các loại dữ liệu đầu vào hỗ trợ (text, image, audio,...)
   */
  @Column({ name: 'input_modalities_fine_tune', type: 'jsonb', default: '[]' })
  inputModalitiesFineTune: InputModality[];

  /**
   * Các loại dữ liệu đầu ra hỗ trợ
   */
  @Column({ name: 'output_modalities_base', type: 'jsonb', default: '[]' })
  outputModalitiesBase: OutputModality[];

  /**
   * Các loại dữ liệu đầu ra hỗ trợ
   */
  @Column({ name: 'output_modalities_fine_tune', type: 'jsonb', default: '[]' })
  outputModalitiesFineTune: OutputModality[];

  /**
   * Các tham số sampling như temperature, top_p,...
   */
  @Column({ name: 'sampling_parameters_base', type: 'jsonb', default: '[]' })
  samplingParametersBase: SamplingParameter[];

  /**
   * Các tham số sampling như temperature, top_p,...
   */
  @Column({ name: 'sampling_parameters_fine_tune', type: 'jsonb', default: '[]' })
  samplingParametersFineTune: SamplingParameter[];


  /**
   * Tập hợp feature đặc biệt (như tool-use, function-calling)
   */
  @Column({ name: 'features_base', type: 'jsonb', default: '[]' })
  featuresBase: ModelFeature[];

  /**
   * Tập hợp feature đặc biệt (như tool-use, function-calling)
   */
  @Column({ name: 'features_fine_tune', type: 'jsonb', default: '[]' })
  featuresFineTune: ModelFeature[];

  /**
   * Thời gian tạo (epoch millis)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  createdAt: number;

  /**
   * Người tạo (liên kết employees)
   */
  @Column({ name: 'created_by', type: 'integer', nullable: true })
  createdBy: number | null;

  /**
   * Thời gian cập nhật
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  updatedAt: number;

  /**
   * Người cập nhật
   */
  @Column({ name: 'updated_by', type: 'integer', nullable: true })
  updatedBy: number | null;

  /**
   * Thời gian xóa mềm (soft delete)
   */
  @Column({ name: 'deleted_at', type: 'bigint', nullable: true })
  deletedAt: number | null;

  /**
   * Người thực hiện xóa
   */
  @Column({ name: 'deleted_by', type: 'bigint', nullable: true })
  deletedBy: number | null;

  /**
   * Giá cơ bản cho model (input/output rate)
   */
  @Column({
    name: 'base_pricing',
    type: 'jsonb',
    default: '{"inputRate": 1, "outputRate": 1}',
    nullable: false,
    comment: 'Giá cơ bản cho model (input/output rate)'
  })
  basePricing: ModelPricingInterface;

  /**
   * Giá fine-tune cho model (input/output rate)
   */
  @Column({
    name: 'fine_tune_pricing',
    type: 'jsonb',
    default: '{"inputRate": 1, "outputRate": 1}',
    nullable: false,
    comment: 'Giá fine-tune cho model (input/output rate)'
  })
  fineTunePricing: ModelPricingInterface;

  /**
   * Giá training cho model
   */
  @Column({
    name: 'training_pricing',
    type: 'integer',
    default: 1,
    nullable: false,
    comment: 'Giá training cho model'
  })
  trainingPricing: number;

  /**
   * Có hỗ trợ fine-tuning không
   */
  @Column({
    name: 'fine_tune',
    type: 'boolean',
    default: false,
    nullable: false,
    comment: 'Có hỗ trợ fine-tuning không'
  })
  fineTune: boolean;

  /**
   * Số tokens tối đa có thể sinh ra
   */
  @Column({
    name: 'max_tokens',
    type: 'integer',
    default: 0,
    nullable: false,
    comment: 'Số tokens tối đa có thể sinh ra'
  })
  maxTokens: number;

  /**
   * Độ dài context tối đa
   */
  @Column({
    name: 'context_window',
    type: 'integer',
    default: 0,
    nullable: false,
    comment: 'Độ dài context tối đa'
  })
  contexWindow: number;
}
