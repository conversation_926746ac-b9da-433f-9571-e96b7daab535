import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger, Injectable } from '@nestjs/common';
import { Job } from 'bullmq';
import { QueueName, ZaloConsultationSequenceJobName } from '../../../queue';
import {
  ZaloConsultationSequenceUserJobData,
  ConsultationMessageType,
} from './interfaces';
import { ZaloConsultationService } from '../../../shared/services/zalo/zalo-consultation.service';
import { ZaloTokenUtilsService } from '../../../shared/services/zalo/zalo-token-utils.service';
import { ConfigService } from '@nestjs/config';

/**
 * Processor xử lý queue Zalo Consultation Sequence
 * Xử lý job gửi chuỗi tin nhắn tư vấn cho từng user
 */
@Injectable()
@Processor(QueueName.ZALO_CONSULTATION_SEQUENCE, { concurrency: 3 })
export class ZaloConsultationSequenceProcessor extends WorkerHost {
  private readonly logger = new Logger(ZaloConsultationSequenceProcessor.name);

  constructor(
    private readonly zaloConsultationService: ZaloConsultationService,
    private readonly zaloTokenUtilsService: ZaloTokenUtilsService,
    private readonly configService: ConfigService,
  ) {
    super();
  }

  /**
   * Xử lý job từ queue
   */
  async process(job: Job<ZaloConsultationSequenceUserJobData>): Promise<void> {
    this.logger.log(
      `Processing consultation sequence job ${job.id} for user ${job.data.userId}`,
    );

    try {
      switch (job.name) {
        case ZaloConsultationSequenceJobName.SEND_CONSULTATION_SEQUENCE_USER:
          await this.processUserConsultationSequence(job);
          break;
        default:
          throw new Error(`Unknown job name: ${job.name}`);
      }

      this.logger.log(`Job ${job.id} completed successfully`);
    } catch (error) {
      this.logger.error(`Job ${job.id} failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xử lý job gửi chuỗi tin nhắn tư vấn cho 1 user
   */
  private async processUserConsultationSequence(
    job: Job<ZaloConsultationSequenceUserJobData>,
  ): Promise<void> {
    const { campaignId, oaId, userId, messages, trackingId } = job.data;

    this.logger.log(
      `Processing consultation sequence for user ${userId}, campaign ${campaignId}, ${messages.length} messages`,
    );

    try {
      // Lấy access token
      const accessToken = await this.getAccessToken(oaId);

      let userSuccessCount = 0;
      let userFailureCount = 0;
      const userResults: Array<{
        messageIndex: number;
        messageId?: string;
        status: 'sent' | 'failed';
        error?: string;
      }> = [];

      // Gửi từng tin nhắn trong chuỗi cho user này
      for (let i = 0; i < messages.length; i++) {
        const message = messages[i];

        // Delay giữa các tin nhắn
        if (message.delaySeconds && message.delaySeconds > 0) {
          await new Promise((resolve) =>
            setTimeout(resolve, (message.delaySeconds || 0) * 1000),
          );
        }

        try {
          let result: { message_id: string };

          // Gửi tin nhắn dựa trên messageType
          switch (message.messageType) {
            case ConsultationMessageType.TEXT:
              result =
                await this.zaloConsultationService.sendConsultationTextMessage(
                  accessToken,
                  userId,
                  message.text!,
                );
              break;

            case ConsultationMessageType.IMAGE:
              result =
                await this.zaloConsultationService.sendConsultationImageMessage(
                  accessToken,
                  userId,
                  message.imageUrl,
                  message.attachmentId,
                  message.imageMessage,
                );
              break;

            case ConsultationMessageType.STICKER:
              result =
                await this.zaloConsultationService.sendConsultationStickerMessage(
                  accessToken,
                  userId,
                  message.stickerId!,
                );
              break;

            case ConsultationMessageType.FILE:
              result =
                await this.zaloConsultationService.sendConsultationFileMessage(
                  accessToken,
                  userId,
                  message.fileUrl!,
                  message.filename!,
                  message.fileMessage,
                );
              break;

            case ConsultationMessageType.REQUEST_INFO:
              // Sử dụng elements mặc định cho form yêu cầu thông tin
              const defaultElements = [
                {
                  title: 'Họ và tên',
                  type: 'text' as const,
                  required: true,
                  placeholder: 'Nhập họ và tên của bạn',
                },
                {
                  title: 'Số điện thoại',
                  type: 'phone' as const,
                  required: true,
                  placeholder: 'Nhập số điện thoại',
                },
                {
                  title: 'Email',
                  type: 'email' as const,
                  required: false,
                  placeholder: 'Nhập địa chỉ email',
                },
              ];

              result =
                await this.zaloConsultationService.sendConsultationRequestInfoMessage(
                  accessToken,
                  userId,
                  message.requestTitle!,
                  defaultElements,
                  message.requestSubtitle,
                  message.requestImageUrl,
                );
              break;

            default:
              throw new Error(
                'Loại tin nhắn tư vấn không hỗ trợ gửi hàng loạt',
              );
          }

          userResults.push({
            messageIndex: i,
            messageId: result.message_id,
            status: 'sent',
          });
          userSuccessCount++;
          this.logger.debug(
            `Message ${i + 1}/${messages.length} sent successfully to user ${userId}, message ID: ${result.message_id}`,
          );
        } catch (error) {
          userResults.push({
            messageIndex: i,
            status: 'failed',
            error: error.message || 'Lỗi không xác định',
          });
          userFailureCount++;
          this.logger.error(
            `Failed to send message ${i + 1}/${messages.length} to user ${userId}: ${error.message}`,
          );

          // Dừng gửi các tin nhắn tiếp theo cho user này nếu gặp lỗi
          break;
        }
      }

      // Cập nhật thống kê cho user job này
      await this.updateCampaignStats(
        campaignId,
        userSuccessCount > 0 ? 1 : 0,
        userSuccessCount > 0 ? 0 : 1,
      );

      this.logger.log(
        `Consultation sequence for user ${userId} completed. Success: ${userSuccessCount}, Failure: ${userFailureCount}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to process consultation sequence for user ${userId}: ${error.message}`,
      );

      // Cập nhật thống kê thất bại
      await this.updateCampaignStats(campaignId, 0, 1);
      throw error;
    }
  }

  /**
   * Lấy access token cho OA
   */
  private async getAccessToken(oaId: string): Promise<string> {
    try {
      return await this.zaloTokenUtilsService.getValidAccessTokenWithRetry(
        oaId,
        3,
      );
    } catch (error) {
      this.logger.error(
        `Failed to get access token for OA ${oaId}: ${error.message}`,
      );
      throw new Error(`Không thể lấy access token cho OA ${oaId}`);
    }
  }

  /**
   * Cập nhật thống kê campaign và kiểm tra completion
   */
  private async updateCampaignStats(
    campaignId: number,
    successIncrement: number,
    failureIncrement: number,
  ): Promise<void> {
    try {
      // Cập nhật thống kê campaign bằng atomic increment
      // Sử dụng raw query để đảm bảo atomic operation
      await this.updateCampaignStatsAtomic(
        campaignId,
        successIncrement,
        failureIncrement,
      );

      // Kiểm tra xem tất cả jobs đã hoàn thành chưa
      await this.checkCampaignCompletion(campaignId);

      this.logger.debug(
        `Updated campaign ${campaignId} stats: +${successIncrement} success, +${failureIncrement} failure`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to update campaign stats for campaign ${campaignId}: ${error.message}`,
      );
      // Don't throw error here to avoid failing the job
    }
  }

  /**
   * Cập nhật thống kê campaign bằng atomic increment
   */
  private async updateCampaignStatsAtomic(
    campaignId: number,
    successIncrement: number,
    failureIncrement: number,
  ): Promise<void> {
    // TODO: Implement với database connection thực tế
    // Hiện tại chỉ log để test
    this.logger.debug(
      `Atomic update campaign ${campaignId}: +${successIncrement} success, +${failureIncrement} failure`,
    );
  }

  /**
   * Kiểm tra xem campaign đã hoàn thành chưa
   */
  private async checkCampaignCompletion(campaignId: number): Promise<void> {
    try {
      // TODO: Implement logic kiểm tra completion
      // 1. Đếm tổng số jobs đã tạo cho campaign này
      // 2. Đếm số jobs đã hoàn thành (success + failure)
      // 3. Nếu tất cả jobs đã hoàn thành:
      //    - Nếu có ít nhất 1 success → COMPLETED
      //    - Nếu tất cả failure → FAILED

      this.logger.debug(`Checking completion for campaign ${campaignId}`);
    } catch (error) {
      this.logger.error(
        `Failed to check campaign completion for ${campaignId}: ${error.message}`,
      );
    }
  }
}
