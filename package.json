{"name": "redai-v201-be-worker", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js", "migration:create": "npm run typeorm -- migration:create src/modules/database/migrations/Migration", "migration:generate": "npm run typeorm -- migration:generate -d src/config/typeorm.config.ts src/modules/database/migrations/Migration", "migration:run": "npm run typeorm -- migration:run -d src/config/typeorm.config.ts", "migration:revert": "npm run typeorm -- migration:revert -d src/config/typeorm.config.ts", "migrate": "ts-node -r tsconfig-paths/register src/cli/migration-cli.ts", "test:microservice": "ts-node -r tsconfig-paths/register src/microservice-client-example.ts"}, "dependencies": {"@aws-sdk/client-s3": "^3.837.0", "@aws-sdk/lib-storage": "^3.837.0", "@aws-sdk/s3-request-presigner": "^3.837.0", "@bull-board/api": "^6.9.6", "@bull-board/express": "^6.10.1", "@bull-board/nestjs": "^6.9.6", "@google-cloud/storage": "^7.16.0", "@google-cloud/translate": "^9.2.0", "@google-cloud/vision": "^5.3.0", "@google/genai": "^1.6.0", "@langchain/anthropic": "^0.3.21", "@langchain/community": "^0.3.44", "@langchain/core": "^0.3.66", "@langchain/deepseek": "^0.0.1", "@langchain/google-genai": "^0.2.9", "@langchain/langgraph": "^0.2.74", "@langchain/langgraph-checkpoint-postgres": "^0.0.5", "@langchain/langgraph-supervisor": "^0.0.12", "@langchain/langgraph-swarm": "^0.0.3", "@langchain/mcp-adapters": "^0.5.4", "@langchain/openai": "^0.5.11", "@langchain/tavily": "^0.1.2", "@langchain/xai": "^0.0.2", "@nestjs/axios": "^4.0.0", "@nestjs/bullmq": "^11.0.2", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/event-emitter": "^3.0.1", "@nestjs/microservices": "^11.1.2", "@nestjs/platform-express": "^11.0.1", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^11.1.6", "@nestjs/terminus": "^11.0.0", "@nestjs/typeorm": "^11.0.0", "ajv": "^8.12.0", "ajv-formats": "^2.1.1", "axios": "^1.9.0", "base64-stream": "^1.0.0", "bullmq": "^5.53.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "dotenv": "^16.5.0", "exponential-backoff": "^3.1.2", "express-basic-auth": "^1.2.1", "facebook-nodejs-business-sdk": "^23.0.0", "google-ads-api": "^20.0.1", "googleapis": "^150.0.1", "ioredis": "^5.6.1", "langchain": "^0.3.29", "nanoid": "^5.1.5", "nest-commander": "^3.17.0", "nodemailer": "^6.10.1", "openai": "^4.97.0", "pg": "^8.15.6", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.22", "uuid": "^11.1.0", "xstate": "^5.20.1", "zod": "^3.25.23"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.11.29", "@swc/jest": "^0.2.38", "@types/base64-stream": "^1.0.5", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/multer": "^1.4.13", "@types/node": "^22.10.7", "@types/nodemailer": "^6.4.17", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s?$": ["@swc/jest"]}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}