import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { McpConfiguration } from '../../interfaces';


/**
 * Entity đại diện cho bảng mcps trong cơ sở dữ liệu
 * Bảng lưu trữ thông tin MCP servers do người dùng hoặc nhân viên tạo
 */
@Entity('mcps')
export class Mcp {
  /**
   * UUID định danh duy nhất cho mỗi MCP server
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Tên server MCP
   */
  @Column({ name: 'name_server', length: 255 })
  nameServer: string;

  /**
   * <PERSON>ô tả chi tiết về MCP server
   */
  @Column({ type: 'text', nullable: true })
  description: string | null;

  /**
   * Cấu hình MCP server (JSON)
   * Bao gồm thông tin như endpoint, authentication, capabilities, etc.
   */
  @Column({ name: 'config', type: 'jsonb' })
  config: McpConfiguration;

  /**
   * Header mặc định cho tất cả các request đến MCP server
   * Định dạng là chuỗi JSON
   */
  @Column({ name: 'headers', type: 'text', nullable: true })
  headers: string | null;

  /**
   * ID của người dùng tạo MCP server (nullable)
   * Tham chiếu đến bảng users
   */
  @Column({ name: 'user_id', type: 'integer', nullable: true })
  userId: number | null;

  /**
   * ID của nhân viên tạo MCP server (nullable)
   * Tham chiếu đến bảng employees
   */
  @Column({ name: 'employee_id', type: 'integer', nullable: true })
  employeeId: number | null;

  /**
   * Thời điểm tạo record (timestamp millis)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật gần nhất (timestamp millis)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  updatedAt: number;
}
