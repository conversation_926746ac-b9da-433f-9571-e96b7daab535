import { CallbackManagerForToolRun } from '@langchain/core/callbacks/manager';
import { StructuredTool, ToolRunnableConfig } from '@langchain/core/tools';
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { z } from 'zod';
import { UserConvertCustomerMemory } from '../../domains/external/entities';
import { WebsitePlannerExecutorConfig } from '../../platforms/website/graph-configs/website-planner-executor-config.interface';

@Injectable()
export class UserConvertCustomerMemoryTool extends StructuredTool {
  private readonly logger = new Logger(
    UserConvertCustomerMemoryTool.name,
  );
  name: string;
  description: string;
  schema: any;
  constructor(
    @InjectRepository(UserConvertCustomerMemory)
    private readonly userConvertCustomerMemoryRepository: Repository<UserConvertCustomerMemory>,
  ) {
    super();
    this.name = 'manage_visitor_memory';
    this.description = `
- A tool to add, update, or delete long-term memories about the visitor. 
- Each memory object in the 'memories' array must have an 'action' field.`;

    const addSchema = z.object({
      action: z.literal('add'),
      content: z
        .string()
        .describe('The main content of the memory about the user.'),
      metadata: z
        .record(z.any())
        .optional()
        .describe('Optional metadata for the memory.'),
    });

    const updateSchema = z.object({
      action: z.literal('update'),
      id: z.string().uuid('The ID of the memory to update.'),

      content: z
        .string()
        .optional()
        .describe('The main content of the memory about the user.'),
      metadata: z.record(z.any()).optional(),
    });

    const deleteSchema = z.object({
      action: z.literal('delete'),
      id: z.string().uuid('The ID of the memory to delete.'),
    });

    const memoryActionSchema = z
      .discriminatedUnion('action', [addSchema, updateSchema, deleteSchema])
      .refine(
        (data) => {
          if (data.action === 'update') {
            return !!data.content || !!data.metadata;
          }
          return true;
        },
        {
          message:
            "An 'update' action must provide either 'content' or 'metadata' to modify.",

          path: ['content'],
        },
      );

    this.schema = z.object({
      memories: z
        .array(memoryActionSchema)
        .describe('An array of memory operations to perform about the user.'),
    });
  }

  protected async _call(
    arg: z.infer<typeof this.schema>,
    runManager?: CallbackManagerForToolRun,
    parentConfig?: ToolRunnableConfig<WebsitePlannerExecutorConfig>,
  ): Promise<string> {
    const { memories } = arg as z.infer<typeof this.schema>;
    const userConvertCustomerId =
      parentConfig?.configurable?.currentUser.visitor.id;
    if (!userConvertCustomerId) {
      return 'Error: Missing user ID in execution context. Do not retry upon this message';
    }
    const toAdd = memories.filter((m) => m.action === 'add');
    const toUpdate = memories.filter((m) => m.action === 'update');
    const toDelete = memories.filter((m) => m.action === 'delete');

    if (
      toAdd.length === 0 &&
      toUpdate.length === 0 &&
      toDelete.length === 0
    ) {
      return 'No valid memory operations provided. Please specify at least one add, update, or delete action.';
    }

    if (toAdd.length > 0) {
      const newMemories = toAdd.map((mem) =>
        this.userConvertCustomerMemoryRepository.create({
          content: mem.content,
          metadata: mem.metadata,
          userConvertCustomerId: userConvertCustomerId,
        }),
      );
      await this.userConvertCustomerMemoryRepository.save(newMemories);
    }

    if (toUpdate.length > 0) {
      const toBeUpdated = await this.userConvertCustomerMemoryRepository.findBy(
        {
          id: In(toUpdate.map((mem) => mem.id)),
          userConvertCustomerId: userConvertCustomerId,
        },
      );
      const updatedEntities = toBeUpdated.map((existingMemory) => {
        return this.userConvertCustomerMemoryRepository.create({
          ...existingMemory,
          content:
            toUpdate.find((m) => m.id === existingMemory.id)?.content ||
            existingMemory.content,
          metadata:
            toUpdate.find((m) => m.id === existingMemory.id)?.metadata ||
            existingMemory.metadata,
        });
      });
      await this.userConvertCustomerMemoryRepository.save(updatedEntities);
    }

    if (toDelete.length > 0) {
      const deleteIds = toDelete.map((mem) => mem.id);
      await this.userConvertCustomerMemoryRepository.delete({
        id: deleteIds,
        userConvertCustomerId: userConvertCustomerId,
      });
    }

    return 'Error: Unable to determine user or employee context for memory operations.';
  }
}
