import { Injectable, Logger } from '@nestjs/common';
import { ModelConfigOutput } from './model-registry-mapper.service';

/**
 * Raw integration data from database queries
 */
export interface IntegrationRawData {
  agentId: string;
  encryptedConfig: string; // Integration.encryptedConfig from DB
  secretKey: string | null; // Integration.secretKey from DB
}

/**
 * Encrypted API key pair for AgentConfigInterface
 */
export interface EncryptedApiKeyPair {
  publicKey: string; // Integration.secretKey from DB
  encryptedContent: string; // Integration.encryptedConfig from DB
}

/**
 * Integration Config Builder Service
 *
 * Simple service that maps database integration fields directly to
 * AgentConfigInterface format. No decryption or complex logic needed.
 */
@Injectable()
export class IntegrationConfigBuilderService {
  private readonly logger = new Logger(IntegrationConfigBuilderService.name);

  /**
   * Build integration configs from pre-fetched batch data
   * ZERO ADDITIONAL QUERIES - pure data mapping
   *
   * @param integrationData Pre-fetched integration data from Query 3
   * @param modelConfigs Pre-processed model configs from Task 3.1
   * @returns Map of agent IDs to their encrypted API key pairs
   */
  buildIntegrationConfigs(
    integrationData: IntegrationRawData[],
    modelConfigs: Record<string, ModelConfigOutput>,
  ): Record<string, EncryptedApiKeyPair[]> {
    this.logger.debug(
      `Processing integrations for ${integrationData.length} records`,
    );

    const integrationsByAgent = this.groupIntegrationsByAgent(integrationData);

    const result: Record<string, EncryptedApiKeyPair[]> = {};

    for (const [agentId, integrations] of integrationsByAgent) {
      const modelConfig = modelConfigs[agentId];
      if (!modelConfig) {
        this.logger.error(`No model config found for agent ${agentId}`);
        throw new Error(`No model config found for agent ${agentId}`);
      }

      const matchingIntegration = integrations.find(
        (integration) => integration.agentId === agentId,
      );

      if (!matchingIntegration) {
        this.logger.error(
          `No matching integration found for agent ${agentId}, provider ${modelConfig.provider}`,
        );
        throw new Error(
          `No matching integration found for agent ${agentId}, provider ${modelConfig.provider}`,
        );
      }
      const apiKeyPairs = this.mapIntegrationToApiKeyPairs(matchingIntegration);
      result[agentId] = apiKeyPairs;

      this.logger.debug(
        `Mapped integration config for agent ${agentId}, provider ${modelConfig.provider}`,
      );
    }

    return result;
  }

  /**
   * Map integration data to API key pairs - simple field mapping
   *
   * @param integration Raw integration data
   * @returns Array with single encrypted API key pair
   */
  private mapIntegrationToApiKeyPairs(
    integration: IntegrationRawData,
  ): EncryptedApiKeyPair[] {
    if (!integration.secretKey) {
      this.logger.error(
        `No secret key found for integration, agent ${integration.agentId}`,
      );
      throw new Error(
        `No secret key found for integration, agent ${integration.agentId}`,
      );
    }

    // Simple 1:1 mapping - no decryption, no complex logic
    return [
      {
        publicKey: integration.secretKey, // Integration.secretKey -> publicKey
        encryptedContent: integration.encryptedConfig, // Integration.encryptedConfig -> encryptedContent
      },
    ];
  }

  /**
   * Group integrations by agent ID for efficient processing
   *
   * @param integrationData Array of integration data
   * @returns Map of agent IDs to their integrations
   */
  private groupIntegrationsByAgent(
    integrationData: IntegrationRawData[],
  ): Map<string, IntegrationRawData[]> {
    const grouped = new Map<string, IntegrationRawData[]>();

    for (const integration of integrationData) {
      if (!grouped.has(integration.agentId)) {
        grouped.set(integration.agentId, []);
      }
      grouped.get(integration.agentId)!.push(integration);
    }

    return grouped;
  }
}
