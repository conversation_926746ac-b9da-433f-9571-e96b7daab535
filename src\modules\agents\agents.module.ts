import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bullmq';
import { QueueName } from '../../queue/queue-name.enum';
import { ReactAgentGraph } from './shared/graphs/react-agent.graph';
import { WebSearchTool } from './shared/tools/web-search.tool';
import { ImageLoaderTool } from './shared/tools/image-loader.tool';
import { McpClientService } from './shared/services/mcp-client.service';
import { SupervisorWorkersGraph } from './shared/graphs/supervisor-workers.graph';
import { PlannerExecutorGraph } from './shared/graphs/planner-executor.graph';
import { InAppAiProcessor } from './platforms/in-app/in-app-ai.processor';
import { AgentMemoryTool } from './shared/tools/agent-memory.tool';

import {
  InAppAgentConfigBuilderService,
  InAppEventProcessorService,
  InAppExecutionCoordinatorService,
  InAppPostProcessingService,
  InAppAgentConfigurationService,
  InAppPlatformStrategy,
} from './platforms/in-app/services';

import {
  InternalMessageOperationsService,
  InternalAttachmentProcessingService,
  InternalInputPreparationService,
} from './domains/internal/services';

import {
  AgentConnection,
  AgentMedia,
  AgentMemories,
  AgentRank,
  AgentUserTools,
  Agent,
  AgentsKnowledgeFile,
  AgentsMcp,
  AgentProduct,
  AgentStrategyUser,
  AgentUrl,
  AssistantSpendingHistory,
  TypeAgentAgentSystem,
  TypeAgentModels,
  TypeAgentTools,
  TypeAgent,
  UserMultiAgent,
  Integration,
  IntegrationProvider,
  Mcp,
  ModelRegistry,
  Models,
  ModelIntegration,
  SystemPrompt,
  UserConverts,
} from './shared/entities';

import {
  InternalConversationThread,
  InternalConversationMessage,
  InternalConversationMessagesAttachment,
  EmployeeMemories,
  UserMemories,
  User,
} from './domains/internal/entities';

import { WebsiteAiProcessor } from './platforms/website/website-ai.processor';
import { ZaloAiProcessor } from './platforms/zalo/zalo-ai.processor';
import {
  AgentConfigAssemblerService,
  AgentConfigBuilderService,
  IntegrationConfigBuilderService,
  McpConfigBuilderService,
  ModelRegistryMapperService,
  StreamingService,
  UserBillingService,
} from './shared/services';
import { RunStatusService } from '../../shared/services';
import { UserMemoryTool } from './shared/tools/user-memory.tool';
import { UserKnowledgeRAGTool } from './shared/tools/user-knowledge-rag.tool';
import { UserMediaRAGTool } from './shared/tools/user-media-rag.tool';
import { UserProductRAGTool } from './shared/tools/user-product-rag.tool';
import { KnowledgeFile, MediaData } from './shared/entities/data';
import { ExternalConversationMessage } from './domains/external/entities/external-conversation-message.entity';
import { ExternalConversationMessageAttachment } from './domains/external/entities/external-conversation-message-attachment.entity';
import { UserConvertCustomer } from './domains/external/entities/user-convert-customer.entity';
import { ExternalCustomerPlatformData } from './domains/external/entities/external-customer-platform-data.entity';
import {
  WebsiteAgentConfigBuilderService,
  WebsiteAgentConfigurationService,
  WebsiteExecutionCoordinatorService,
  WebsiteEventProcessorService,
  WebsitePostProcessingService,
  WebsitePlatformStrategy,
} from './platforms/website/services';

import {
  ExternalAttachmentProcessingService,
  ExternalInputPreparationService,
  ExternalMessageOperationsService,
} from './domains/external/services';
import { UserConvertCustomerMemory } from './domains/external/entities/user-convert-customer-memory.entity';
import { UserConvertCustomerMemoryTool } from './shared/tools/manage-visitor-memory.tool';
import { AgentKnowledgeRAGTool } from './shared/tools/agent-knowledge-rag.tool';
import { AgentMediaRAGTool } from './shared/tools/agent-media-rag.tool';
import { AgentProductRAGTool } from './shared/tools/agent-product-rag.tool';
import {
  ZaloAgentConfigBuilderService,
  ZaloAgentConfigurationService,
  ZaloEventProcessorService,
  ZaloExecutionCoordinatorService,
  ZaloPlatformStrategy,
  ZaloPostProcessingService,
} from './platforms/zalo';
import { UpdateVisitorInfoTool } from './shared/tools/update-visitor-info.tool';

@Module({
  imports: [
    BullModule.registerQueue({
      name: QueueName.IN_APP_AI,
    }),
    BullModule.registerQueue({
      name: QueueName.WEBSITE_AI,
    }),
    BullModule.registerQueue({
      name: QueueName.ZALO_AI,
    }),

    TypeOrmModule.forFeature([
      Agent,
      AgentConnection,
      AgentMedia,
      AgentMemories,
      AgentRank,
      AgentUserTools,
      AgentsKnowledgeFile,
      AgentsMcp,
      AgentProduct,
      AgentStrategyUser,
      AgentUrl,
      AssistantSpendingHistory,
      EmployeeMemories,
      UserMemories,
      UserMultiAgent,
      SystemPrompt,
      KnowledgeFile,
      MediaData,
      // Type agent entities
      TypeAgent,
      TypeAgentAgentSystem,
      TypeAgentModels,
      TypeAgentTools,

      // Integration entities
      Integration,
      IntegrationProvider,

      // Tools entities
      Mcp,

      // Model entities
      ModelRegistry,
      Models,
      ModelIntegration,

      // In-app entities
      InternalConversationThread,
      InternalConversationMessage,
      InternalConversationMessagesAttachment,

      // External entities
      ExternalConversationMessage,
      ExternalConversationMessageAttachment,
      UserConvertCustomer,
      ExternalCustomerPlatformData,
      UserConvertCustomerMemory,
      // User entities
      User,
      UserConverts,
    ]),
  ],
  providers: [
    ReactAgentGraph,

    WebSearchTool,
    ImageLoaderTool,
    McpClientService,
    AgentMemoryTool,

    // New LangGraph implementations
    SupervisorWorkersGraph,
    PlannerExecutorGraph,

    // Processors
    InAppAiProcessor,
    WebsiteAiProcessor,
    ZaloAiProcessor,

    // In-App Platform Services
    StreamingService,
    ModelRegistryMapperService,
    IntegrationConfigBuilderService,
    McpConfigBuilderService,
    AgentConfigAssemblerService,
    AgentConfigBuilderService,
    InAppAgentConfigBuilderService,
    InAppEventProcessorService,
    UserBillingService,
    InternalMessageOperationsService,
    // ✅ NEW: Unified run status management
    RunStatusService,
    // User Memory Tool
    UserMemoryTool,
    // RAG search tools
    UserKnowledgeRAGTool,
    UserMediaRAGTool,
    UserProductRAGTool,
    UserConvertCustomerMemoryTool,
    AgentKnowledgeRAGTool,
    AgentMediaRAGTool,
    AgentProductRAGTool,
    UpdateVisitorInfoTool,

    // New services
    InAppPlatformStrategy,
    InternalAttachmentProcessingService,
    InternalInputPreparationService,
    InAppExecutionCoordinatorService,
    InAppPostProcessingService,
    InAppAgentConfigurationService,
    ExternalMessageOperationsService,
    ExternalInputPreparationService,
    ExternalAttachmentProcessingService,
    // Website Platform Services
    WebsitePlatformStrategy,
    WebsiteAgentConfigBuilderService,
    WebsiteAgentConfigurationService,
    WebsiteExecutionCoordinatorService,
    WebsiteEventProcessorService,
    WebsitePostProcessingService,
    // Zalo Platform Services
    ZaloPlatformStrategy,
    ZaloAgentConfigBuilderService,
    ZaloAgentConfigurationService,
    ZaloExecutionCoordinatorService,
    ZaloEventProcessorService,
    ZaloPostProcessingService,
  ],
  exports: [TypeOrmModule, InAppAiProcessor],
})
export class AgentsModule {}
