import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bullmq';
import { QueueName } from '../../queue/queue-name.enum';
import { ReactAgentGraph } from './graphs/react-agent.graph';
import { MessengerTool } from './platforms/messenger/messenger.tool';
import { ZaloTool } from './platforms/zalo/zalo.tool.';
import { WebSearchTool } from './runtime/tools/web-search.tool';
import { ImageLoaderTool } from './runtime/tools/image-loader.tool';
import { McpClientService } from './services/mcp-client.service';
import { ModelService } from './graphs/services/model.service';
import { TrimService } from './graphs/services/trim.service';
import { SupervisorWorkersGraph } from './graphs/multi-agents-graphs/supervisor-workers.graph';
import { PlannerExecutorGraph } from './graphs/multi-agents-graphs/planner-executor.graph';
import { InAppAiProcessor } from './platforms/in-app/in-app-ai.processor';
import { AgentMemoryTool } from './runtime/tools/agent-memory.tool';

import {
  InAppAgentConfigBuilderService,
  InAppEventProcessorService,
  InternalMessageOperationsService,
  InternalAttachmentProcessingService,
  InternalInputPreparationService,
  InAppExecutionCoordinatorService,
  InAppPostProcessingService,
  InAppAgentConfigurationService,
} from './platforms/in-app/services';

import {
  AgentConnection,
  AgentMedia,
  AgentMemories,
  AgentRank,
  AgentUserTools,
  Agent,
  AgentsKnowledgeFile,
  AgentsMcp,
  AgentProduct,
  AgentStrategyUser,
  AgentUrl,
  AssistantSpendingHistory,
  EmployeeMemories,
  TypeAgentAgentSystem,
  TypeAgentModels,
  TypeAgentTools,
  TypeAgent,
  UserMemories,
  UserMultiAgent,
  Integration,
  IntegrationProvider,
  BoxChatConfig,
  Mcp,
  ModelRegistry,
  Models,
  ModelIntegration,
  User,
  SystemPrompt,
} from './entities';

import {
  InternalConversationThread,
  InternalConversationMessage,
  InternalConversationMessagesAttachment,
} from './entities/internals';

import { WebsiteAiProcessor } from './platforms/website/website-ai.processor';
import {
  AgentConfigAssemblerService,
  AgentConfigBuilderService,
  IntegrationConfigBuilderService,
  McpConfigBuilderService,
  ModelRegistryMapperService,
  StreamingService,
  UserBillingService,
} from './services';
import { RunStatusService } from '../../shared/services';
import { UserMemoryTool } from './runtime/tools/user-memory.tool';
import { InAppPlatformStrategy } from './platforms/in-app/services';
import { UserKnowledgeRAGTool } from './runtime/tools/user-knowledge-rag.tool';
import { UserMediaRAGTool } from './runtime/tools/user-media-rag.tool';
import { UserProductRAGTool } from './runtime/tools/user-product-rag.tool';
import { KnowledgeFile, MediaData } from './entities/data';
import { ExternalConversationMessage } from './entities/externals/external-conversation-message.entity';
import { ExternalConversationMessageAttachment } from './entities/externals/external-conversation-message-attachment.entity';
import { UserConvertCustomer } from './entities/externals/user-convert-customer.entity';
import { ExternalCustomerPlatformData } from './entities/externals/external-customer-platform-data.entity';
import {
  ExternalAttachmentProcessingService,
  ExternalInputPreparationService,
  ExternalMessageOperationsService,
  WebsiteAgentConfigBuilderService,
  WebsiteAgentConfigurationService,
  WebsiteExecutionCoordinatorService,
  WebsiteEventProcessorService,
  WebsitePostProcessingService,
} from './platforms/website/services';
import { WebsitePlatformStrategy } from './platforms/website/services';
import { UserConvertCustomerMemory } from './entities/externals/user-convert-customer-memory.entity';
import { WebsiteUserConvertCustomerMemoryTool } from './runtime/tools/website-user-convert-customer-memory.tool';
import { AgentKnowledgeRAGTool } from './runtime/tools/agent-knowledge-rag.tool';
import { AgentMediaRAGTool } from './runtime/tools/agent-media-rag.tool';

@Module({
  imports: [
    BullModule.registerQueue({
      name: QueueName.IN_APP_AI,
    }),

    TypeOrmModule.forFeature([
      Agent,
      AgentConnection,
      AgentMedia,
      AgentMemories,
      AgentRank,
      AgentUserTools,
      AgentsKnowledgeFile,
      AgentsMcp,
      AgentProduct,
      AgentStrategyUser,
      AgentUrl,
      AssistantSpendingHistory,
      EmployeeMemories,
      UserMemories,
      UserMultiAgent,
      SystemPrompt,
      KnowledgeFile,
      MediaData,
      // Type agent entities
      TypeAgent,
      TypeAgentAgentSystem,
      TypeAgentModels,
      TypeAgentTools,

      // Integration entities
      Integration,
      IntegrationProvider,
      BoxChatConfig,

      // Tools entities
      Mcp,

      // Model entities
      ModelRegistry,
      Models,
      ModelIntegration,

      // In-app entities
      InternalConversationThread,
      InternalConversationMessage,
      InternalConversationMessagesAttachment,

      // External entities
      ExternalConversationMessage,
      ExternalConversationMessageAttachment,
      UserConvertCustomer,
      ExternalCustomerPlatformData,
      UserConvertCustomerMemory,
      // User entities
      User,
    ]),
  ],
  providers: [
    ReactAgentGraph,

    MessengerTool,
    ZaloTool,
    WebSearchTool,
    ImageLoaderTool,
    McpClientService,
    ModelService,
    AgentMemoryTool,

    // New LangGraph implementations
    SupervisorWorkersGraph,
    PlannerExecutorGraph,

    // Processors
    InAppAiProcessor,

    // In-App Platform Services
    StreamingService,
    ModelRegistryMapperService,
    IntegrationConfigBuilderService,
    McpConfigBuilderService,
    AgentConfigAssemblerService,
    AgentConfigBuilderService,
    InAppAgentConfigBuilderService,
    InAppEventProcessorService,
    UserBillingService,
    InternalMessageOperationsService,
    TrimService,
    // ✅ NEW: Unified run status management
    RunStatusService,
    // User Memory Tool
    UserMemoryTool,
    // Website AI Processor
    WebsiteAiProcessor,
    // RAG search tools
    UserKnowledgeRAGTool,
    UserMediaRAGTool,
    UserProductRAGTool,
    WebsiteUserConvertCustomerMemoryTool,
    AgentKnowledgeRAGTool,
    AgentMediaRAGTool,
    // New services
    InAppPlatformStrategy,
    InternalAttachmentProcessingService,
    InternalInputPreparationService,
    InAppExecutionCoordinatorService,
    InAppPostProcessingService,
    InAppAgentConfigurationService,
    ExternalMessageOperationsService,
    ExternalInputPreparationService,
    ExternalAttachmentProcessingService,
    // Website Platform Services
    WebsitePlatformStrategy,
    WebsiteAgentConfigBuilderService,
    WebsiteAgentConfigurationService,
    WebsiteExecutionCoordinatorService,
    WebsiteEventProcessorService,
    WebsitePostProcessingService,
  ],
  exports: [TypeOrmModule, InAppAiProcessor],
})
export class AgentsModule {}
