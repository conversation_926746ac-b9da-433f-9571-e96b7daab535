import { GenderEnum } from '../enums';

export interface Profile {
  gender?: GenderEnum;
  dateOfBirth?: number;
  position?: string;
  education?: string;
  skills?: string[];
  personality?: string[];
  languages?: string[];
  nations?: string;
}

export interface IStrategyContentStep {
  stepOrder: number;
  content: string;
}

export interface AgentEntityConfigInterface {
  profile?: Profile;
  convert?: Record<string, any>;
  isForSale?: boolean;
  description?: string;
  content?: IStrategyContentStep[];
  example?: IStrategyContentStep[];
}
