import { Body, Controller, Post, Logger, BadRequestException } from '@nestjs/common';
import { 
  StandaloneNodeExecutionService, 
  StandaloneNodeExecutionInput,
  StandaloneNodeExecutionResult 
} from '../services/standalone-node-execution.service';

/**
 * DTO cho standalone node execution request
 */
export class ExecuteStandaloneNodeDto {
  workflowId: string;
  nodeId: string;
  userId?: number;
  context?: Record<string, any>;
  config?: {
    timeout?: number;
    retryCount?: number;
    skipValidation?: boolean;
  };
}

/**
 * Controller cho standalone node execution
 * Cho phép execute một node cụ thể mà không cần chạy toàn bộ workflow
 */
@Controller('workflow/standalone-node')
export class StandaloneNodeExecutionController {
  private readonly logger = new Logger(StandaloneNodeExecutionController.name);

  constructor(
    private readonly standaloneNodeExecutionService: StandaloneNodeExecutionService,
  ) {}

  /**
   * Execute một node cụ thể
   * POST /workflow/standalone-node/execute
   */
  @Post('execute')
  async executeNode(@Body() dto: ExecuteStandaloneNodeDto): Promise<StandaloneNodeExecutionResult> {
    this.logger.log(`Received standalone node execution request: ${dto.nodeId} in workflow: ${dto.workflowId}`);

    try {
      // Validate input
      const validation = await this.standaloneNodeExecutionService.validateInput(dto);
      if (!validation.isValid) {
        throw new BadRequestException(`Validation failed: ${validation.errors.join(', ')}`);
      }

      // Execute node
      const result = await this.standaloneNodeExecutionService.executeNode(dto);

      this.logger.log(`Standalone node execution completed: ${dto.nodeId}, success: ${result.success}`);
      
      return result;

    } catch (error) {
      this.logger.error(`Standalone node execution failed: ${dto.nodeId}`, error);
      throw error;
    }
  }

  /**
   * Execute node với context từ XState workflow
   * POST /workflow/standalone-node/execute-with-xstate-context
   */
  @Post('execute-with-xstate-context')
  async executeNodeWithXStateContext(@Body() dto: {
    workflowId: string;
    nodeId: string;
    userId?: number;
    xstateContext: {
      // XState workflow context
      nodes?: Map<string, any>;
      connections?: any[];
      previousOutputs?: Record<string, any>;
      triggerData?: Record<string, any>;
      metadata?: any;
      // Additional XState context fields
      [key: string]: any;
    };
    config?: {
      timeout?: number;
      retryCount?: number;
      skipValidation?: boolean;
    };
  }): Promise<StandaloneNodeExecutionResult> {
    this.logger.log(`Received XState context node execution: ${dto.nodeId} in workflow: ${dto.workflowId}`);

    try {
      // Convert XState context to standard context format
      const context = this.convertXStateContextToStandardContext(dto.xstateContext);

      // Execute node
      const result = await this.standaloneNodeExecutionService.executeNode({
        workflowId: dto.workflowId,
        nodeId: dto.nodeId,
        userId: dto.userId,
        context,
        config: dto.config,
      });

      this.logger.log(`XState context node execution completed: ${dto.nodeId}, success: ${result.success}`);
      
      return result;

    } catch (error) {
      this.logger.error(`XState context node execution failed: ${dto.nodeId}`, error);
      throw error;
    }
  }

  /**
   * Execute node với simple input data
   * POST /workflow/standalone-node/execute-simple
   */
  @Post('execute-simple')
  async executeNodeSimple(@Body() dto: {
    workflowId: string;
    nodeId: string;
    userId?: number;
    inputData?: any;
    previousOutputs?: Record<string, any>;
    triggerData?: any;
  }): Promise<StandaloneNodeExecutionResult> {
    this.logger.log(`Received simple node execution: ${dto.nodeId} in workflow: ${dto.workflowId}`);

    try {
      // Prepare simple context
      const context = {
        inputData: dto.inputData || {},
        previousOutputs: dto.previousOutputs || {},
        triggerData: dto.triggerData || {},
      };

      // Execute node
      const result = await this.standaloneNodeExecutionService.executeNode({
        workflowId: dto.workflowId,
        nodeId: dto.nodeId,
        userId: dto.userId,
        context,
      });

      this.logger.log(`Simple node execution completed: ${dto.nodeId}, success: ${result.success}`);
      
      return result;

    } catch (error) {
      this.logger.error(`Simple node execution failed: ${dto.nodeId}`, error);
      throw error;
    }
  }

  /**
   * Convert XState context to standard context format
   */
  private convertXStateContextToStandardContext(xstateContext: any): Record<string, any> {
    const context: Record<string, any> = {};

    // Extract previous outputs
    if (xstateContext.previousOutputs) {
      context.previousOutputs = xstateContext.previousOutputs;
    }

    // Extract node outputs từ nodes Map
    if (xstateContext.nodes && xstateContext.nodes instanceof Map) {
      const nodeOutputs: Record<string, any> = {};
      
      xstateContext.nodes.forEach((nodeState: any, nodeId: string) => {
        if (nodeState.output) {
          nodeOutputs[nodeId] = nodeState.output;
        }
      });
      
      if (Object.keys(nodeOutputs).length > 0) {
        context.nodeOutputs = nodeOutputs;
      }
    }

    // Extract trigger data
    if (xstateContext.triggerData) {
      context.triggerData = xstateContext.triggerData;
    }

    // Extract metadata
    if (xstateContext.metadata) {
      context.metadata = xstateContext.metadata;
    }

    // Include raw XState context
    context.xstateContext = xstateContext;

    // Extract any other relevant fields
    Object.keys(xstateContext).forEach(key => {
      if (!['nodes', 'connections', 'previousOutputs', 'triggerData', 'metadata'].includes(key)) {
        context[key] = xstateContext[key];
      }
    });

    return context;
  }
}
