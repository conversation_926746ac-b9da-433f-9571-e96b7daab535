import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { ConversationThreadsAttachmentType } from '../../enums';

/**
 * External Conversation Messages Attachment entity
 * Junction table for website message attachments (media + knowledge files)
 * Mirrors the internal attachment structure for code reuse
 */
@Entity('external_conversation_message_attachment')
export class ExternalConversationMessageAttachment {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Reference to the external customer platform data (conversation thread)
   */
  @Column({
    name: 'external_customer_platform_data_id',
    type: 'uuid',
    nullable: false,
  })
  externalCustomerPlatformDataId: string;

  /**
   * Reference to the external conversation message
   */
  @Column({
    name: 'external_conversation_message_id',
    type: 'uuid',
    nullable: false,
  })
  externalConversationMessageId: string;

  /**
   * Reference to the attachment (MediaData.id or KnowledgeFile.id)
   */
  @Column({ name: 'attachment_id', type: 'uuid', nullable: false })
  attachmentId: string;

  /**
   * Type of attachment (IMAGE or KNOWLEDGE_FILE)
   */
  @Column({
    name: 'media_type',
    type: 'enum',
    enum: ConversationThreadsAttachmentType,
    nullable: false,
  })
  mediaType: ConversationThreadsAttachmentType;

  /**
   * Reference to the user who owns this attachment
   */
  @Column({ name: 'user_id', type: 'bigint', nullable: false })
  userId: number;

  @Column({
    name: 'created_at',
    type: 'bigint',
    nullable: false,
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  createdAt: string;

  @Column({ name: 'deleted_at', type: 'bigint', nullable: true })
  deletedAt?: string;
}
