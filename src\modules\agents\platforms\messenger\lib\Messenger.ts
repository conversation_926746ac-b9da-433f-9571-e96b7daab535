import type {
  SendApiTextMessageRequest,
  SendApiTextMessageResponse,
  SendApiMediaMessagePhotoRequest,
  SendApiMediaMessagePhotoResponse,
  SendApiQuickRepliesRequest,
  SendApiQuickRepliesResponse,
  SendApiButtonTemplateRequest,
  SendApiButtonTemplateResponse,
  SendApiGenericTemplateRequest,
  SendApiGenericTemplateResponse,
  SendApiMediaTemplateRequest,
  SendApiMediaTemplateResponse,
  SendApiNamespace,
  AttachmentUploadApiAttachmentUploadPhotoRequest,
  AttachmentUploadApiAttachmentUploadPhotoResponse,
  AttachmentUploadApiNamespace,
  ModerateConversationsApiBlockUserRequest,
  ModerateConversationsApiBlockUserResponse,
  ModerateConversationsApiUnblockUserRequest,
  ModerateConversationsApiUnblockUserResponse,
  ModerateConversationsApiBanUserRequest,
  ModerateConversationsApiBanUserResponse,
  ModerateConversationsApiUnbanUserRequest,
  ModerateConversationsApiUnbanUserResponse,
  ModerateConversationsApiMoveConversationToSpamRequest,
  ModerateConversationsApiMoveConversationToSpamResponse,
  ModerateConversationsApiNamespace,
  TemplatesGenericTemplateRequest,
  TemplatesGenericTemplateResponse,
  TemplatesButtonTemplateRequest,
  TemplatesButtonTemplateResponse,
  TemplatesNamespace,
} from './MessengerTypes';
export class Messenger {
  readonly sendApi: SendApiNamespace;
  readonly attachmentUploadApi: AttachmentUploadApiNamespace;
  readonly moderateConversationsApi: ModerateConversationsApiNamespace;
  readonly templates: TemplatesNamespace;
  private readonly token: string;
  private readonly baseUrl: string;
  private readonly version: string = 'v23.0';
  constructor(baseUrl: string, token: string) {
    const self = this;
    this.baseUrl = baseUrl;
    this.token = token;
    const version = this.version;
    this.sendApi = {
      async textMessage(
        pageId: string,
        body: SendApiTextMessageRequest,
      ): Promise<SendApiTextMessageResponse> {
        const headers = {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${self.token}`,
        };
        const url = `/${version}/${pageId}/messages`;

        const res = await fetch(self.baseUrl + url, {
          method: 'POST',
          headers: headers,
          body: JSON.stringify(body),
        });
        return res.json();
      },

      async mediaMessagePhoto(
        pageId: string,
        body: SendApiMediaMessagePhotoRequest,
      ): Promise<SendApiMediaMessagePhotoResponse> {
        const headers = {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${self.token}`,
        };
        const url = `/${version}/${pageId}/messages`;

        const res = await fetch(self.baseUrl + url, {
          method: 'POST',
          headers: headers,
          body: JSON.stringify(body),
        });
        return res.json();
      },

      async quickReplies(
        pageId: string,
        body: SendApiQuickRepliesRequest,
      ): Promise<SendApiQuickRepliesResponse> {
        const headers = {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${self.token}`,
        };
        const url = `/${version}/${pageId}/messages`;

        const res = await fetch(self.baseUrl + url, {
          method: 'POST',
          headers: headers,
          body: JSON.stringify(body),
        });
        return res.json();
      },

      async buttonTemplate(
        pageId: string,
        body: SendApiButtonTemplateRequest,
      ): Promise<SendApiButtonTemplateResponse> {
        const headers = {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${self.token}`,
        };
        const url = `/${version}/${pageId}/messages`;

        const res = await fetch(self.baseUrl + url, {
          method: 'POST',
          headers: headers,
          body: JSON.stringify(body),
        });
        return res.json();
      },

      async genericTemplate(
        pageId: string,
        body: SendApiGenericTemplateRequest,
      ): Promise<SendApiGenericTemplateResponse> {
        const headers = {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${self.token}`,
        };
        const url = `/${version}/${pageId}/messages`;

        const res = await fetch(self.baseUrl + url, {
          method: 'POST',
          headers: headers,
          body: JSON.stringify(body),
        });
        return res.json();
      },

      async mediaTemplate(
        pageId: string,
        body: SendApiMediaTemplateRequest,
      ): Promise<SendApiMediaTemplateResponse> {
        const headers = {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${self.token}`,
        };
        const url = `/${version}/${pageId}/messages`;

        const res = await fetch(self.baseUrl + url, {
          method: 'POST',
          headers: headers,
          body: JSON.stringify(body),
        });
        return res.json();
      },
    };
    this.attachmentUploadApi = {
      async attachmentUploadPhoto(
        pageId: string,
        body: AttachmentUploadApiAttachmentUploadPhotoRequest,
      ): Promise<AttachmentUploadApiAttachmentUploadPhotoResponse> {
        const headers = {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${self.token}`,
        };
        const url = `/${version}/${pageId}/message_attachments`;

        const res = await fetch(self.baseUrl + url, {
          method: 'POST',
          headers: headers,
          body: JSON.stringify({
            message: body.message,
          }),
        });
        return res.json();
      },
    };
    this.moderateConversationsApi = {
      async blockUser(
        pageId: string,
        body: ModerateConversationsApiBlockUserRequest,
      ): Promise<ModerateConversationsApiBlockUserResponse> {
        const headers = {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${self.token}`,
        };
        const url = `/${version}/${pageId}/moderate_conversations`;

        const res = await fetch(self.baseUrl + url, {
          method: 'POST',
          headers: headers,
          body: JSON.stringify({
            user_ids: body.user_ids,
            actions: body.actions,
          }),
        });
        return res.json();
      },

      async unblockUser(
        pageId: string,
        body: ModerateConversationsApiUnblockUserRequest,
      ): Promise<ModerateConversationsApiUnblockUserResponse> {
        const headers = {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${self.token}`,
        };
        const url = `/${version}/${pageId}/moderate_conversations`;

        const res = await fetch(self.baseUrl + url, {
          method: 'POST',
          headers: headers,
          body: JSON.stringify(body),
        });
        return res.json();
      },

      async banUser(
        pageId: string,
        body: ModerateConversationsApiBanUserRequest,
      ): Promise<ModerateConversationsApiBanUserResponse> {
        const headers = {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${self.token}`,
        };
        const url = `/${version}/${pageId}/moderate_conversations`;

        const res = await fetch(self.baseUrl + url, {
          method: 'POST',
          headers: headers,
          body: JSON.stringify(body),
        });
        return res.json();
      },

      async unbanUser(
        pageId: string,
        body: ModerateConversationsApiUnbanUserRequest,
      ): Promise<ModerateConversationsApiUnbanUserResponse> {
        const headers = {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${self.token}`,
        };
        const url = `/${version}/${pageId}/moderate_conversations`;

        const res = await fetch(self.baseUrl + url, {
          method: 'POST',
          headers: headers,
          body: JSON.stringify(body),
        });
        return res.json();
      },

      async moveConversationToSpam(
        pageId: string,
        body: ModerateConversationsApiMoveConversationToSpamRequest,
      ): Promise<ModerateConversationsApiMoveConversationToSpamResponse> {
        const headers = {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${self.token}`,
        };
        const url = `/${version}/${pageId}/moderate_conversations`;

        const res = await fetch(self.baseUrl + url, {
          method: 'POST',
          headers: headers,
          body: JSON.stringify(body),
        });
        return res.json();
      },
    };
    this.templates = {
      async genericTemplate(
        pageId: string,
        body: TemplatesGenericTemplateRequest,
      ): Promise<TemplatesGenericTemplateResponse> {
        const headers = {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${self.token}`,
        };
        const url = `/${version}/${pageId}/messages`;

        const res = await fetch(self.baseUrl + url, {
          method: 'POST',
          headers: headers,
          body: JSON.stringify(body),
        });
        return res.json();
      },

      async buttonTemplate(
        pageId: string,
        body: TemplatesButtonTemplateRequest,
      ): Promise<TemplatesButtonTemplateResponse> {
        const headers = {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${self.token}`,
        };
        const url = `/${version}/${pageId}/messages`;

        const res = await fetch(self.baseUrl + url, {
          method: 'POST',
          headers: headers,
          body: JSON.stringify(body),
        });
        return res.json();
      },
    };
  }
}
