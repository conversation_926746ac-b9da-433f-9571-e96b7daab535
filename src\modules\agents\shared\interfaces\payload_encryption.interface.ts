export interface OpenAIPayload {
  apiKey: string;
}

export interface GeminiPayload {
  apiKey: string;
}

export interface AnthropicPayload {
  apiKey: string;
}

export interface DeepSeekPayload {
  apiKey: string;
}

export interface XAIPayload {
  apiKey: string;
}

export interface FacebookPagePayload {
  accessToken: string;
}

export interface FacebookPersonalPayload {
  accessToken: string;
}

/**
 * Payload cho Zalo Official Account - dữ liệu nhạy cảm cần mã hóa
 */
export interface ZaloOAPayload {
  accessToken: string;
  refreshToken?: string;
}

export type PayloadEncryption =
  | OpenAIPayload
  | GeminiPayload
  | AnthropicPayload
  | DeepSeekPayload
  | XAIPayload
  | FacebookPagePayload
  | FacebookPersonalPayload
  | ZaloOAPayload;
