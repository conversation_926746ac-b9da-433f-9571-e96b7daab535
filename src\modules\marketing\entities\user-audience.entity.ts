import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng user_audience trong cơ sở dữ liệu
 * Bảng khách hàng của người dùng
 */
@Entity('user_audience')
export class UserAudience {
  /**
   * ID của audience
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * ID của người dùng
   */
  @Column({ name: 'user_id', nullable: true, comment: 'Mã khách hàng' })
  userId: number;

  // Không sử dụng quan hệ với bảng User, chỉ lưu ID

  /**
   * Tên của khách hàng
   */
  @Column({ name: 'name', length: 255, nullable: true, comment: 'Tên khách hàng' })
  name: string;

  /**
   * Email của khách hàng
   */
  @Column({
    name: 'email',
    length: 255,
    nullable: true,
    comment: 'Email người dùng',
  })
  email: string;

  /**
   * Mã quốc gia (số)
   * @example 84
   */
  @Column({ name: 'country_code', type: 'integer', nullable: true, comment: 'Mã quốc gia' })
  countryCode: number | null;

  /**
   * Số điện thoại (không bao gồm mã quốc gia)
   * @example "912345678"
   */
  @Column({ name: 'phone_number', type: 'varchar', length: 20, nullable: true, comment: 'Số điện thoại không bao gồm mã quốc gia' })
  phoneNumber: string | null;

  /**
   * URL avatar của khách hàng (S3 key)
   */
  @Column({ name: 'avatar',type: 'varchar', length: 500, nullable: true, comment: 'URL avatar của khách hàng' })
  avatar: string | null;

  /**
   * Zalo Social ID của khách hàng (user_id từ Zalo API)
   */
  @Column({ name: 'zalo_social_id', type: 'varchar', length: 255, nullable: true, comment: 'Zalo Social ID của khách hàng' })
  zaloSocialId: string | null;

  /**
   * Danh sách avatar URLs từ các nguồn bên ngoài (Zalo, Facebook, etc.)
   * Lưu trữ dưới dạng JSON array của strings
   */
  @Column({
    name: 'avatars_external',
    type: 'jsonb',
    nullable: true,
    comment: 'Danh sách avatar URLs từ các nguồn bên ngoài'
  })
  avatarsExternal: string[] | null;

  /**
   * Nguồn import của audience (ZALO, WEB, MANUAL, FACEBOOK)
   */
  @Column({
    name: 'import_resource',
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: 'Nguồn import của audience'
  })
  importResource: string | null;

  /**
   * ID của Zalo Official Account (foreign key)
   */
  @Column({
    name: 'zalo_official_account_id',
    type: 'integer',
    nullable: true,
    comment: 'ID của Zalo Official Account (foreign key)'
  })
  zaloOfficialAccountId: number | null;

  /**
   * Trạng thái theo dõi OA của người dùng Zalo
   */
  @Column({
    name: 'zalo_user_is_follower',
    type: 'boolean',
    nullable: true,
    comment: 'Trạng thái theo dõi OA của người dùng Zalo'
  })
  zaloUserIsFollower: boolean | null;

  /**
   * Ngày cuối cùng người dùng có tương tác với OA
   */
  @Column({
    name: 'user_last_interaction_date',
    type: 'bigint',
    nullable: true,
    comment: 'Ngày cuối cùng người dùng có tương tác với OA'
  })
  userLastInteractionDate: number | null;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', comment: 'Ngày tạo' })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    nullable: true,
    comment: 'Ngày cập nhật',
  })
  updatedAt: number;

  /**
   * Trạng thái email của audience
   */
  @Column({
    name: 'email_status',
    length: 50,
    nullable: true,
    default: 'VALID',
    comment: 'Trạng thái email (VALID, INVALID, BOUNCED, BLOCKED)',
  })
  emailStatus: string;

  /**
   * Loại lỗi email (nếu có)
   */
  @Column('varchar', {
    name: 'email_error_type',
    length: 100,
    nullable: true,
    comment:
      'Loại lỗi email (DOMAIN_NOT_FOUND, MAILBOX_FULL, INVALID_FORMAT, etc.)',
  })
  emailErrorType!: string;

  /**
   * Chi tiết lỗi email
   */
  @Column('text', {
    name: 'email_error_details',
    nullable: true,
    comment: 'Chi tiết lỗi email',
  })
  emailErrorDetails!: string;

  /**
   * Thời gian lần cuối gửi email thành công
   */
  @Column('bigint', {
    name: 'last_email_sent_at',
    nullable: true,
    comment: 'Thời gian lần cuối gửi email thành công',
  })
  lastEmailSentAt!: number;

  /**
   * Thời gian lần cuối gửi email thất bại
   */
  @Column('bigint', {
    name: 'last_email_failed_at',
    nullable: true,
    comment: 'Thời gian lần cuối gửi email thất bại',
  })
  lastEmailFailedAt!: number;

  /**
   * Số lần gửi email thất bại liên tiếp
   */
  @Column({
    name: 'email_failure_count',
    type: 'integer',
    default: 0,
    comment: 'Số lần gửi email thất bại liên tiếp',
  })
  emailFailureCount: number;

  // Không sử dụng quan hệ với các bảng khác, chỉ lưu ID
}
