import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { Platform } from '../enums';

/**
 * create table system_prompt
(
    id         serial
        primary key,
    prompt     text                                                  not null,
    active     boolean         default true not null,
    platform   platform_enum   default 'IN_APP'::platform_enum       not null,
    agent_type type_agent_enum default 'SUPERVISOR'::type_agent_enum not null
);

alter table system_prompt
    owner to root;

grant select, update, usage on sequence system_prompt_id_seq to member;

grant delete, insert, select, update on system_prompt to member;

create type platform_enum as enum ('IN_APP', 'WEBSITE', 'ZALO', 'MESSENGER');

create type type_agent_enum as enum ('SYSTEM', 'TEMPLATE', 'SUPERVISOR', 'ASSISTANT', 'STRATEGY');
 */
@Entity('system_prompt')
export class SystemPrompt {
  @PrimaryGeneratedColumn() id: number;
  @Column({
    type: 'text',
    nullable: false,
  })
  prompt: string;

  @Column({
    type: 'boolean',
    default: true,
    nullable: false,
    name: 'active',
  })
  active: boolean;

  @Column({
    type: 'enum',
    enum: ['SYSTEM', 'TEMPLATE', 'SUPERVISOR', 'ASSISTANT', 'STRATEGY'],
    default: 'SUPERVISOR',
    nullable: false,
    name: 'agent_type',
  })
  agentType: 'SYSTEM' | 'TEMPLATE' | 'SUPERVISOR' | 'ASSISTANT' | 'STRATEGY';
}
