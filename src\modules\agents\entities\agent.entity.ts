import {
  BeforeUpdate,
  Column,
  Entity,
  Index,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { AgentEntityConfigInterface, ModelConfig } from '../interfaces';

/**
 * Entity đại diện cho bảng agents trong cơ sở dữ liệu
 * Bảng lưu thông tin chung của tất cả agent trong hệ thống
 */
@Entity('agents')
@Index('idx_agents_deleted_at', ['deletedAt'])
export class Agent {
  /**
   * UUID định danh duy nhất cho mỗi agent
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Tên hiển thị của agent
   */
  @Column({ length: 255 })
  name: string;

  /**
   * Lưu key s3 của avatar agent
   */
  @Column({ length: 255, type: 'varchar', nullable: true })
  avatar: string | null;

  /**
   * Cấu hình AI model dạng JSONB (ví dụ: {"temperature": 0.7, "top_p": 0.9, "top_k": 40})
   */
  @Column({ name: 'model_config', type: 'jsonb', default: '{}' })
  modelConfig: ModelConfig;

  /**
   * Hướng dẫn hoặc system prompt cho agent
   */
  @Column({ type: 'text', nullable: true })
  instruction: string | null;

  /**
   * Thời điểm tạo (timestamp millis)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật gần nhất (timestamp millis)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  updatedAt: number;

  @BeforeUpdate()
  updateTimestamp() {
    this.updatedAt = Date.now();
  }

  /**
   * Thời điểm xóa mềm (timestamp millis)
   */
  @Column({ name: 'deleted_at', type: 'bigint', nullable: true })
  deletedAt: number | null;

  /**
   * ID của người dùng sở hữu agent (nullable)
   * Tham chiếu đến bảng users
   */
  @Column({ name: 'user_id', type: 'integer', nullable: true })
  userId: number | null;

  /**
   * ID của nhân viên tạo agent (nullable)
   * Tham chiếu đến bảng employees
   */
  @Column({ name: 'employee_id', type: 'integer', nullable: true })
  employeeId: number | null;

  /**
   * ID của loại agent
   * Tham chiếu đến bảng type_agents
   */
  @Column({ name: 'type_id', type: 'integer', nullable: true })
  typeId: number | null;

  /**
   * Cấu hình bổ sung của agent dạng JSONB
   */
  @Column({ name: 'config', type: 'jsonb', nullable: true })
  config: AgentEntityConfigInterface | null;

  /**
   * Trạng thái hoạt động của agent
   */
  @Column({ name: 'active', type: 'boolean', default: true })
  active: boolean;

  /**
   * Điểm kinh nghiệm của agent
   */
  @Column({ name: 'exp', type: 'bigint', default: 0 })
  exp: number;

  /**
   * ID của agent chiến lược (self-reference)
   * Tham chiếu đến bảng agents
   */
  @Column({ name: 'strategy_id', type: 'uuid', nullable: true })
  strategyId: string | null;

  /**
   * ID của model được sử dụng bởi agent
   * Tham chiếu đến bảng models
   */
  @Column({ name: 'model_id', type: 'uuid', nullable: true })
  modelId: string | null;

  /**
   * Sử dụng key LLM của hệ thống hay không
   */
  @Column({ name: 'use_system_key', type: 'boolean', default: false })
  useSystemKey: boolean;
}
