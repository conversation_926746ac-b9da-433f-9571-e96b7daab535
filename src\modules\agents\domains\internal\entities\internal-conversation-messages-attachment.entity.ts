import { ConversationThreadsAttachmentType } from '../../../shared/enums';
import { Entity, PrimaryColumn, Column } from 'typeorm';

/**
 * Internal Conversation Messages Attachment entity
 * Junction table for message attachments (media + knowledge files)
 */
@Entity('internal_conversation_messages_attachment')
export class InternalConversationMessagesAttachment {
  @PrimaryColumn({ name: 'id', type: 'uuid' })
  id: string;

  /**
   * Reference to the conversation thread
   */
  @Column({ name: 'conversation_id', type: 'uuid' })
  conversationId: string;

  /**
   * Reference to the message
   */
  @Column({ name: 'message_id', type: 'uuid' })
  messageId: string;

  /**
   * Reference to the attachment (media or knowledge file)
   */
  @Column({ name: 'attachment_id', type: 'uuid' })
  attachmentId: string;

  /**
   * Type of attachment (video, image, audio, knowledge file)
   */
  @Column({
    name: 'media_type',
    type: 'enum',
    enum: ConversationThreadsAttachmentType,
  })
  mediaType: ConversationThreadsAttachmentType;
}
