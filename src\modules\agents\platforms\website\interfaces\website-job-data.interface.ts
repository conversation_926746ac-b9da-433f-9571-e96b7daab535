import { Platform } from '../../../shared/enums';
import { WebsiteInfo } from './website-info.interface';
import { UserInfo } from '../../../shared/interfaces';
import { UserConvertCustomerInfo } from '../../../shared/interfaces';

export interface WebsiteJobData {
  runId: string;
  threadId: string;
  mainAgentId: string;
  plannerAgentId?: string;
  platform: Platform.WEBSITE;
  keys: {
    platformThreadId: string; // "website:thread-123"
    runStatusKey: string; // "run_status:website:thread-123"
    streamKey: string; // "website:agent_stream:thread-123:run-456"
  };
  humanInfo: {
    websiteOwner: UserInfo;
    websiteVisitor: UserConvertCustomerInfo;
    websiteInfo: WebsiteInfo;
  };
}

/**
 * Example (JSON):
 * {
 *   runId: '8ab6e045-76ab-4ae7-93fb-68882d049e78',
 *   threadId: '0a983b62-cdd0-4ebe-9d9d-b259f97194ac',
 *   mainAgentId: '0c0d5c6d-403a-4ae5-8bd2-2f00b7a09b45',
 *   plannerAgentId: 'agent-456',
 *   platform: Platform.WEBSITE,
 *   keys: {
 *     platformThreadId: 'website:0a983b62-cdd0-4ebe-9d9d-b259f97194ac',
 *     runStatusKey: 'run_status:website:0a983b62-cdd0-4ebe-9d9d-b259f97194ac',
 *     streamKey: 'website:agent_stream:0a983b62-cdd0-4ebe-9d9d-b259f97194ac:8ab6e045-76ab-4ae7-93fb-68882d049e78',
 *   },
 *   humanInfo: {
 *     websiteOwner: {
 *       userId: 1,
 *     },
 *     websiteVisitor: {
 *       id: '864b9263-257d-4999-b468-74ff73ca8d1d',
 *       externalPlatformId: '0a983b62-cdd0-4ebe-9d9d-b259f97194ac',
 *     },
 *     websiteInfo: {
 *       url: 'https://example.com',
 *       title: 'Example Website',
 *     },
 *   },
 * }
 */