import { Platform } from 'src/modules/agents/enums';
import { WebsiteInfo } from './website-info.interface';
import { UserInfo } from 'src/modules/agents/interfaces';
import { UserConvertCustomerInfo } from 'src/modules/agents/interfaces';

export interface WebsiteJobData {
  runId: string;
  threadId: string;
  mainAgentId: string;
  plannerAgentId?: string;
  platform: Platform.WEBSITE;
  keys: {
    platformThreadId: string; // "website:thread-123"
    runStatusKey: string; // "run_status:website:thread-123"
    streamKey: string; // "website:agent_stream:thread-123:run-456"
  };
  humanInfo: {
    websiteOwner: UserInfo;
    websiteVisitor: UserConvertCustomerInfo;
    websiteInfo: WebsiteInfo;
  };
}
