import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { HttpStatusCode } from 'axios';
import { 
  IHttpRequestParameters, 
  IHttpRequestOutput,
  EHttpMethod,
  EAuthType,
  validateHttpRequestParameters 
} from '../interfaces/core/http/http-request.interface';

/**
 * HTTP Request Executor Service
 * Service chuyên dụng để thực hiện HTTP requests với đầu vào/ra chuẩn
 */
@Injectable()
export class HttpRequestExecutorService {
  private readonly logger = new Logger(HttpRequestExecutorService.name);

  constructor(private readonly httpService: HttpService) {}

  /**
   * Execute HTTP request node
   * @param context - HTTP request parameters
   * @returns HTTP request output with code and data
   */
  protected async executeNode(
    context: IHttpRequestParameters
  ): Promise<IHttpRequestOutput> {
    const startTime = Date.now();

    try {
      this.logger.debug(`Executing HTTP ${context.method} request to ${context.url}`);

      // Validate parameters
      const validation = validateHttpRequestParameters(context);
      if (!validation.isValid) {
        throw new Error(`Invalid HTTP request parameters: ${validation.errors.join(', ')}`);
      }

      // Build request configuration
      const config = this.buildRequestConfig(context);

      // Execute request based on method
      const response = await this.performRequest(context.method, context.url, context.body, config);

      const endTime = Date.now();
      const executionTime = endTime - startTime;

      this.logger.debug(`HTTP request completed in ${executionTime}ms with status ${response.status}`);

      // Return result in IHttpRequestOutput format
      return {
        code: response.status as HttpStatusCode,
        data: {
          success: response.status >= 200 && response.status < 300,
          status_code: response.status,
          status_text: response.statusText,
          headers: response.headers,
          body: response.data,
          response_time: executionTime,
          final_url: context.url,
          error: null,
          
          // Detailed response information
          response: {
            code: response.status,
            status: response.statusText,
            headers: response.headers,
            data: response.data,
            size: this.calculateResponseSize(response),
            contentType: response.headers['content-type'] || 'unknown'
          },
          
          // Request information for debugging
          request: {
            method: context.method.toString(),
            url: context.url,
            headers: config.headers || {},
            body: context.body,
            timestamp: startTime
          },
          
          metadata: {
            content_length: this.calculateResponseSize(response),
            request_id: response.headers['x-request-id'] || 'unknown',
            execution_time: executionTime
          }
        }
      };

    } catch (error) {
      const errorTime = Date.now();
      const executionTime = errorTime - startTime;
      
      this.logger.error(`HTTP request failed after ${executionTime}ms:`, error);

      // Handle axios error (HTTP error response)
      if (error.response) {
        return {
          code: error.response.status as HttpStatusCode,
          data: {
            success: false,
            status_code: error.response.status,
            status_text: error.response.statusText,
            headers: error.response.headers,
            body: error.response.data,
            response_time: executionTime,
            final_url: context.url,
            error: error.message,
            
            // Detailed error response information
            response: {
              code: error.response.status,
              status: error.response.statusText,
              headers: error.response.headers,
              data: error.response.data,
              size: this.calculateResponseSize(error.response),
              contentType: error.response.headers['content-type'] || 'unknown'
            },
            
            // Request information for debugging
            request: {
              method: context.method.toString(),
              url: context.url,
              headers: this.buildRequestConfig(context).headers || {},
              body: context.body,
              timestamp: startTime
            },
            
            metadata: {
              content_length: this.calculateResponseSize(error.response),
              request_id: error.response.headers['x-request-id'] || 'unknown',
              execution_time: executionTime
            }
          }
        };
      }

      // Handle network or other errors (no response)
      return {
        code: 0 as HttpStatusCode,
        data: {
          success: false,
          status_code: 0,
          status_text: 'Request Failed',
          headers: {},
          body: null,
          response_time: executionTime,
          final_url: context.url,
          error: error.message,
          
          // Empty response for network errors
          response: {
            code: 0,
            status: 'Network Error',
            headers: {},
            data: null,
            size: 0,
            contentType: 'unknown'
          },
          
          // Request information for debugging
          request: {
            method: context.method.toString(),
            url: context.url,
            headers: this.buildRequestConfig(context).headers || {},
            body: context.body,
            timestamp: startTime
          },
          
          metadata: {
            content_length: 0,
            request_id: 'unknown',
            execution_time: executionTime
          }
        }
      };
    }
  }

  /**
   * Build axios request configuration
   */
  private buildRequestConfig(parameters: IHttpRequestParameters): any {
    const config: any = {
      timeout: parameters.timeout || 30000,
      headers: { ...parameters.headers } || {},
      validateStatus: () => true // Don't throw on HTTP error status
    };

    // Add authentication
    if (parameters.auth_type && parameters.auth_type !== EAuthType.NONE) {
      switch (parameters.auth_type) {
        case EAuthType.BEARER:
          if (parameters.auth_config?.token) {
            config.headers['Authorization'] = `Bearer ${parameters.auth_config.token}`;
          }
          break;
        case EAuthType.BASIC:
          if (parameters.auth_config?.username && parameters.auth_config?.password) {
            config.auth = {
              username: parameters.auth_config.username,
              password: parameters.auth_config.password
            };
          }
          break;
        case EAuthType.API_KEY:
          if (parameters.auth_config?.api_key && parameters.auth_config?.api_key_header) {
            config.headers[parameters.auth_config.api_key_header] = parameters.auth_config.api_key;
          }
          break;
      }
    }

    // Add query parameters
    if (parameters.query_params) {
      config.params = { ...parameters.query_params };
    }

    // Set content type
    if (parameters.content_type) {
      config.headers['Content-Type'] = parameters.content_type;
    }

    return config;
  }

  /**
   * Perform HTTP request based on method
   */
  private async performRequest(
    method: EHttpMethod,
    url: string,
    body?: any,
    config?: any
  ): Promise<any> {
    switch (method) {
      case EHttpMethod.GET:
        return firstValueFrom(this.httpService.get(url, config));
      case EHttpMethod.POST:
        return firstValueFrom(this.httpService.post(url, body, config));
      case EHttpMethod.PUT:
        return firstValueFrom(this.httpService.put(url, body, config));
      case EHttpMethod.PATCH:
        return firstValueFrom(this.httpService.patch(url, body, config));
      case EHttpMethod.DELETE:
        return firstValueFrom(this.httpService.delete(url, config));
      case EHttpMethod.HEAD:
        return firstValueFrom(this.httpService.head(url, config));
      case EHttpMethod.OPTIONS:
        return firstValueFrom(this.httpService.request({ ...config, method: 'OPTIONS', url }));
      default:
        throw new Error(`Unsupported HTTP method: ${method}`);
    }
  }

  /**
   * Calculate response size
   */
  private calculateResponseSize(response: any): number {
    if (response.headers['content-length']) {
      return parseInt(response.headers['content-length']);
    }
    
    if (typeof response.data === 'string') {
      return response.data.length;
    }
    
    return JSON.stringify(response.data || {}).length;
  }

  /**
   * Validate HTTP request parameters
   */
  validateParameters(parameters: IHttpRequestParameters): string[] {
    const validation = validateHttpRequestParameters(parameters);
    return validation.errors;
  }

  /**
   * Test HTTP connection
   */
  async testConnection(url: string): Promise<boolean> {
    try {
      const response = await firstValueFrom(
        this.httpService.head(url, { timeout: 5000 })
      );
      return response.status >= 200 && response.status < 400;
    } catch {
      return false;
    }
  }
}
