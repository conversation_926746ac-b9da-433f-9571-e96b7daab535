import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import { ExecutorRegistry } from './xstate/executors/node-executor.factory';
import { ALL_NODE_EXECUTORS } from './xstate/executors';
import { INodeExecutor } from './xstate/executors/base/node-executor.interface';

/**
 * Service để tự động đăng ký tất cả node executors khi module khởi tạo
 */
@Injectable()
export class ExecutorRegistrationService implements OnModuleInit {
  private readonly logger = new Logger(ExecutorRegistrationService.name);

  constructor(
    private readonly moduleRef: ModuleRef,
    private readonly executorRegistry: ExecutorRegistry
  ) {}

  /**
   * Đăng ký tất cả executors khi module khởi tạo
   */
  async onModuleInit(): Promise<void> {
    this.logger.log('Starting executor registration...');
    
    try {
      // Đăng ký tất cả executors từ ALL_NODE_EXECUTORS array
      for (const ExecutorClass of ALL_NODE_EXECUTORS) {
        try {
          // Get executor instance từ DI container
          const executor = this.moduleRef.get<INodeExecutor>(ExecutorClass, { strict: false });
          
          if (executor) {
            // Đăng ký executor
            this.executorRegistry.register(executor);
            this.logger.log(`✅ Registered executor: ${executor.executorName} v${executor.version}`);
            this.logger.debug(`   - Supported node types: ${executor.supportedNodeTypes.join(', ')}`);
          } else {
            this.logger.warn(`❌ Could not get executor instance for ${ExecutorClass.name}`);
          }
        } catch (error) {
          this.logger.error(`❌ Failed to register executor ${ExecutorClass.name}:`, error);
        }
      }

      // Log tổng kết
      const registeredTypes = this.executorRegistry.getSupportedNodeTypes();
      this.logger.log(`🎉 Executor registration completed!`);
      this.logger.log(`📊 Total registered executors: ${this.executorRegistry.getAll().length}`);
      this.logger.log(`📋 Supported node types: ${registeredTypes.join(', ')}`);
      
    } catch (error) {
      this.logger.error('❌ Failed to register executors:', error);
      throw error;
    }
  }

  /**
   * Lấy thông tin về các executors đã đăng ký
   */
  getRegistrationInfo(): {
    totalExecutors: number;
    supportedNodeTypes: string[];
    executors: Array<{
      name: string;
      version: string;
      nodeTypes: string[];
    }>;
  } {
    const executors = this.executorRegistry.getAll();
    
    return {
      totalExecutors: executors.length,
      supportedNodeTypes: this.executorRegistry.getSupportedNodeTypes(),
      executors: executors.map(executor => ({
        name: executor.executorName,
        version: executor.version,
        nodeTypes: executor.supportedNodeTypes
      }))
    };
  }
}
