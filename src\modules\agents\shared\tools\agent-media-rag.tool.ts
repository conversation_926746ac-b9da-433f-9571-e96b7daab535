import { Injectable, Logger } from '@nestjs/common';
import { StructuredTool, ToolRunnableConfig } from '@langchain/core/tools';
import { CallbackManagerForToolRun } from '@langchain/core/callbacks/manager';
import { z } from 'zod';
import { RagEngineService } from 'src/infra';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AssistantSpendingType } from '../enums';
import { UserBillingService } from '../services';
import { MediaData } from '../entities/data';
import { GraphConfigType } from '../interfaces';

@Injectable()
export class AgentMediaRAGTool extends StructuredTool {
  name: string;
  description: string;
  schema: z.ZodSchema;
  private readonly rPointEmbeddingRate = 2;
  private readonly logger = new Logger(AgentMediaRAGTool.name);

  constructor(
    private readonly ragEngineService: RagEngineService,
    @InjectRepository(MediaData)
    private readonly mediaDataRepository: Repository<MediaData>,
    private readonly userBilling: UserBillingService,
  ) {
    super();
    this.logger.log('Initializing AgentMediaRAGTool');
    this.name = 'search_image_content';
    this.description =
      'Search through image content associated with this agent to find relevant information. ' +
      'Use this tool when you need to find specific information from the images you have access to. ' +
      "Generate appropriate search queries based on the user's question and the context of the images.";

    this.schema = z.object({
      query: z
        .string()
        .describe(
          'The search query to find relevant information in the image content',
        ),
      media_ids: z
        .array(z.string())
        .optional()
        .describe(
          'Optional array of specific image IDs to search within. If not provided, all accessible images will be searched.',
        ),
      media_type: z
        .array(z.enum(['image']))
        .optional()
        .describe('The type of media to search within (default: image)'),
      max_results: z
        .number()
        .min(1)
        .max(100)
        .optional()
        .describe('Optional maximum number of results to return (default: 10)'),
    });
  }

  protected async _call(
    arg: {
      query: string;
      media_ids?: string[];
      media_type?: ('image' | 'video' | 'audio')[];
      max_results?: number;
    },
    runManager?: CallbackManagerForToolRun,
    parentConfig?: ToolRunnableConfig<GraphConfigType>, // Using a generic config type
  ): Promise<string> {
    try {
      const owner = parentConfig?.configurable?.executorAgent?.owner;
      const agentId = parentConfig?.configurable?.executorAgent?.id;
      const { userId, employeeId } = owner || {};

      // An owner (user or employee) and agentId must be present to use the tool
      if (!agentId || (!userId && !employeeId)) {
        const errorMsg =
          'Internal Error: Agent ID and User or Employee ID are required to search image content.';
        this.logger.error(errorMsg);
        return errorMsg;
      }

      // If specific media_ids aren't provided, get all media linked to the agent
      const mediaIdsToSearch =
        arg.media_ids && arg.media_ids.length > 0
          ? arg.media_ids
          : await this.getAllMediaIdsByAgentId(agentId);

      if (mediaIdsToSearch.length === 0) {
        return `No images are associated with this agent. Cannot perform search for query: "${arg.query}"`;
      }

      // Call the RAG engine service to search media (hardcoded to 'image' type)
      const searchResult = await this.ragEngineService.searchMedia({
        query: arg.query,
        media_ids: mediaIdsToSearch,
        limit: arg.max_results || 10,
        media_type: arg.media_type || ['image'], // Default to 'image' if not provided
      });

      // --- CONDITIONAL BILLING LOGIC ---
      // Only execute billing if a userId is present. Employees do not incur charges.
      if (userId) {
        const usageToken = searchResult.token_usage?.embedding_tokens || 0;
        const usageRPoint = usageToken * this.rPointEmbeddingRate;

        if (usageRPoint > 0) {
          this.logger.debug(
            `User ${userId} will be charged ${usageRPoint} R-Points for image search query "${arg.query}"`,
          );
          await this.userBilling.updateUserPointBalance(
            userId,
            usageRPoint,
            AssistantSpendingType.IN_APP,
          );
          await this.userBilling.createSpendingRecords(
            [
              {
                agentId: agentId as string,
                model: 'jina-embedding-v4', // Or the actual embedding model used
                inputTokens: usageToken,
                outputTokens: 0,
                totalTokens: usageToken,
                pointCost: usageRPoint,
              },
            ],
            userId,
            AssistantSpendingType.IN_APP,
            parentConfig?.configurable?.run_id || '',
          );
        }
      }

      // Format the results for the LLM
      if (!searchResult.success || searchResult.results.length === 0) {
        return `No relevant image content found for query: "${arg.query}"`;
      }

      const formattedResults = searchResult.results
        .map((result, index) => {
          let resultText = `--- Image Result ${index + 1} ---\n`;
          resultText += `Similarity Score: ${result.similarity.toFixed(3)}\n`;
          if (result.rerank_score) {
            resultText += `Rerank Score: ${result.rerank_score.toFixed(3)}\n`;
          }
          if (result.name) {
            resultText += `Image Name: ${result.name}\n`;
          }
          if (result.description) {
            resultText += `Description: ${result.description}\n`;
          }
          if (result.tags && result.tags.length > 0) {
            resultText += `Tags: ${result.tags.join(', ')}\n`;
          }
          resultText += `Content: ${result.content}\n`;
          return resultText;
        })
        .join('\n');

      const summary = `Found ${searchResult.total_found} relevant image results for "${arg.query}" (showing top ${searchResult.results.length}):\n\n${formattedResults}`;

      runManager?.handleText(
        `Image search executed in ${searchResult.execution_time_ms}ms.`,
      );
      return summary;
    } catch (error) {
      const errorMessage = `Error searching image content: ${error.message}`;
      this.logger.error(errorMessage, error.stack);
      runManager?.handleText(errorMessage);
      throw new Error(errorMessage);
    }
  }

  /**
   * --- MODIFIED MEDIA RETRIEVAL LOGIC ---
   * Retrieves all media IDs associated with a specific agent
   * by joining through the 'agents_mcp' pivot table.
   * @param agentId The UUID of the agent.
   * @returns A promise that resolves to an array of media ID strings.
   */
  async getAllMediaIdsByAgentId(agentId: string): Promise<string[]> {
    if (!agentId) {
      this.logger.warn('getAllMediaIdsByAgentId called with no agentId.');
      return [];
    }

    try {
      this.logger.debug(`Fetching all media IDs for agent ${agentId}`);
      // This query joins MediaData ('md') with the pivot table 'agents_mcp' ('am')
      // and filters by the agent_id. This assumes 'mcp_id' in the pivot table
      // refers to the primary key of the MediaData entity, which is 'id'.
      const queryBuilder = this.mediaDataRepository
        .createQueryBuilder('md')
        .innerJoin(
          'agents_mcp', // The name of the pivot table
          'am', // Alias for the pivot table
          'am.mcp_id = md.id', // The JOIN condition
        )
        .select('md.media_id', 'media_id')
        .where('am.agent_id = :agentId', { agentId })
        .andWhere('md.id IS NOT NULL');

      const result = await queryBuilder.getRawMany();
      const mediaIds = result.map((item) => item.media_id).filter(Boolean);

      this.logger.debug(
        `Found ${mediaIds.length} associated media IDs for agent ${agentId}`,
      );
      return mediaIds;
    } catch (error) {
      this.logger.error(
        `Error fetching media IDs for agent ${agentId}: ${error.message}`,
        error.stack,
      );
      return []; // Return an empty array on error to prevent crashing the agent.
    }
  }
}
