/**
 * Interface cho backup tokens trong trường hợp encryption bị lỗi
 */
export interface ZaloOABackupTokens {
  accessToken: string;
  refreshToken: string;
  appId?: string;
  appSecret?: string;
  backupCreatedAt: string;
}

/**
 * Interface cho metadata của Zalo Official Account trong Integration entity
 */
export interface ZaloOAMetadata {
  /**
   * ID của Official Account trên <PERSON>
   */
  oaId: string;

  /**
   * Tên của Official Account
   */
  name: string;

  /**
   * Mô tả của Official Account
   */
  description?: string;

  /**
   * URL avatar của Official Account
   */
  avatarUrl?: string;

  /**
   * Thời gian hết hạn của access token (Unix timestamp)
   */
  expiresAt: number;

  /**
   * Trạng thái kết nối (active, inactive, NEEDS_REAUTH)
   */
  status: string;

  /**
   * Thời điểm tạo (Unix timestamp)
   */
  createdAt: number;

  /**
   * Thời điểm cập nhật (Unix timestamp)
   */
  updatedAt: number;

  /**
   * Backup tokens trong trường hợp encryption bị lỗi
   */
  backupTokens?: ZaloOABackupTokens;

  /**
   * Lý do lỗi nếu có
   */
  errorReason?: string;

  /**
   * Thời điểm xảy ra lỗi
   */
  corruptedAt?: string;
}
