import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng agents_rank trong cơ sở dữ liệu
 * Lưu trữ cấu hình các cấp bậc (rank) của agent dựa trên điểm kinh nghiệm (exp).
 * Mỗi cấp bậc được xác định bởi khoảng điểm kinh nghiệm (min_exp và max_exp),
 * cùng với các thông tin liên quan như tên, mô tả và huy hiệu.
 */
@Entity('agents_rank')
export class AgentRank {
  /**
   * Định danh duy nhất cho mỗi cấp bậc, tự động tăng
   */
  @PrimaryGeneratedColumn() id: number;

  /**
   * Tên của cấp bậc (ví dụ: 'Sơ cấp', 'Chuyên gia')
   */
  @Column({ length: 50 }) name: string;

  /**
   * <PERSON><PERSON> tả chi tiết tùy chọn về cấp bậc
   */
  @Column({ type: 'text', nullable: true }) description: string | null;

  /**
   * keyS3 ảnh/huy hiệu đại diện cho cấp bậc
   */
  @Column({ length: 255 }) badge: string;

  /**
   * Điểm kinh nghiệm tối thiểu cần thiết để đạt cấp bậc (bao gồm)
   */
  @Column({ name: 'min_exp', type: 'bigint', default: 0 }) minExp: number;

  /**
   * Điểm kinh nghiệm tối đa cho cấp bậc (không bao gồm, hoặc NULL nếu không có giới hạn trên)
   */
  @Column({ name: 'max_exp', type: 'bigint', default: 1 }) maxExp: number;

  /**
   * Trạng thái kích hoạt
   */
  @Column({ name: 'active', type: 'boolean', default: false }) active: boolean;
}
