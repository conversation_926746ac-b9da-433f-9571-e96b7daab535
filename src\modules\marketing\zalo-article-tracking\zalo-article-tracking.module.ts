import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { ConfigModule } from '@nestjs/config';
import { QueueName } from '../../../queue/queue-name.enum';
import { ZaloArticleTrackingProcessor } from './zalo-article-tracking.processor';
import { ZaloArticleTrackingService } from './zalo-article-tracking.service';
import { ZaloModule } from '../../../shared/services/zalo/zalo.module';

/**
 * Module xử lý tracking bài viết Zalo
 */
@Module({
  imports: [
    ConfigModule,
    // Import queue cho article tracking
    BullModule.registerQueue({
      name: QueueName.ZALO_ARTICLE_TRACKING,
    }),
    // Import Zalo services
    ZaloModule,
  ],
  providers: [
    ZaloArticleTrackingProcessor,
    ZaloArticleTrackingService,
  ],
  exports: [
    ZaloArticleTrackingService,
  ],
})
export class ZaloArticleTrackingModule {}
