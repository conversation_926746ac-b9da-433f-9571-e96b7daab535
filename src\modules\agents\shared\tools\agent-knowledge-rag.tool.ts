import { Injectable, Logger } from '@nestjs/common';
import { StructuredTool, ToolRunnableConfig } from '@langchain/core/tools';
import { CallbackManagerForToolRun } from '@langchain/core/callbacks/manager';
import { z } from 'zod';
import { RagEngineService } from 'src/infra';
import { InjectRepository } from '@nestjs/typeorm';
import {
  KnowledgeFile,
  KnowledgeFileStatus,
} from '../entities/data';
import { Repository } from 'typeorm';
import { AssistantSpendingType } from '../enums';
import { UserBillingService } from '../services';
import { GraphConfigType } from '../interfaces/graph.config';

@Injectable()
export class AgentKnowledgeRAGTool extends StructuredTool {
  name: string;
  description: string;
  schema: z.ZodSchema;
  private readonly rPointEmbeddingRate = 2;
  private readonly logger = new Logger(AgentKnowledgeRAGTool.name);

  constructor(
    private readonly ragEngineService: RagEngineService,
    @InjectRepository(KnowledgeFile)
    private readonly knowledgeFileRepository: Repository<KnowledgeFile>,
    private readonly userBilling: UserBillingService,
  ) {
    super();
    this.logger.log('Initializing AgentKnowledgeRAGTool');
    this.name = 'search_knowledge_files';
    this.description =
      'Search through uploaded knowledge files associated with this agent to find relevant information. ' +
      'Use this tool when you need to find specific information within the documents you have access to. ' +
      "Generate appropriate search queries based on the user's question.";

    this.schema = z.object({
      query: z
        .string()
        .describe(
          'The search query to find relevant information in the knowledge files',
        ),
      file_ids: z
        .array(z.string())
        .optional()
        .describe(
          'Optional array of specific file IDs to search within. If not provided, all accessible files will be searched.',
        ),
      max_results: z
        .number()
        .min(1)
        .max(100)
        .optional()
        .describe('Optional maximum number of results to return (default: 10)'),
    });
  }

  protected async _call(
    arg: {
      query: string;
      file_ids?: string[];
      max_results?: number;
    },
    runManager?: CallbackManagerForToolRun,
    parentConfig?: ToolRunnableConfig<GraphConfigType>,
  ): Promise<string> {
    try {
      const owner = parentConfig?.configurable?.executorAgent?.owner;
      const agentId = parentConfig?.configurable?.executorAgent?.id;
      const { userId, employeeId } = owner || {};

      // An owner (user or employee) and agentId must be present to use the tool
      if (!agentId || (!userId && !employeeId)) {
        const errorMsg =
          'Internal Error: Agent ID and User or Employee ID are required to search knowledge files.';
        this.logger.error(errorMsg);
        return errorMsg;
      }

      // If specific file_ids aren't provided, get all files linked to the agent
      const fileIdsToSearch =
        arg.file_ids && arg.file_ids.length > 0
          ? arg.file_ids
          : await this.getAllFileIdsByAgentId(agentId);

      if (fileIdsToSearch.length === 0) {
        return `No knowledge files are associated with this agent. Cannot perform search for query: "${arg.query}"`;
      }

      // Call the RAG engine service to search files
      const searchResult = await this.ragEngineService.searchFiles({
        query: arg.query,
        file_ids: fileIdsToSearch,
        limit: arg.max_results || 10, // Default to 10 if not provided
      });

      // --- MODIFIED BILLING LOGIC ---
      // Only execute billing if a userId is present. Employees do not incur charges.
      if (userId) {
        const usageToken = searchResult.token_usage?.embedding_tokens || 0;
        const usageRPoint = usageToken * this.rPointEmbeddingRate;

        if (usageRPoint > 0) {
          this.logger.debug(
            `User ${userId} will be charged ${usageRPoint} R-Points for search query "${arg.query}"`,
          );
          await this.userBilling.updateUserPointBalance(
            userId,
            usageRPoint,
            AssistantSpendingType.IN_APP,
          );
          await this.userBilling.createSpendingRecords(
            [
              {
                agentId: agentId as string,
                model: 'jina-embedding-v4', // Or the actual embedding model used
                inputTokens: usageToken,
                outputTokens: 0,
                totalTokens: usageToken,
                pointCost: usageRPoint,
              },
            ],
            userId,
            AssistantSpendingType.IN_APP,
            parentConfig?.configurable?.run_id || '',
          );
        }
      }

      // Format the results for the LLM
      if (!searchResult.success || searchResult.results.length === 0) {
        return `No relevant information found for query: "${arg.query}"`;
      }

      const formattedResults = searchResult.results
        .map((result, index) => {
          let resultText = `--- Result ${index + 1} ---\n`;
          resultText += `Similarity Score: ${result.similarity_score.toFixed(
            3,
          )}\n`;
          if (result.rerank_score) {
            resultText += `Rerank Score: ${result.rerank_score.toFixed(3)}\n`;
          }
          if (result.filename) {
            resultText += `Source: ${result.filename}\n`;
          }
          resultText += `Content: ${result.content}\n`;
          return resultText;
        })
        .join('\n');

      const summary = `Found ${searchResult.total_found} relevant results for "${arg.query}" (showing top ${searchResult.results.length}):\n\n${formattedResults}`;

      if (runManager) {
        runManager.handleText(
          `Search executed in ${searchResult.execution_time_ms}ms.`,
        );
      }

      return summary;
    } catch (error) {
      const errorMessage = `Error searching knowledge files: ${error.message}`;
      this.logger.error(errorMessage, error.stack);
      if (runManager) {
        runManager.handleText(errorMessage); // Inform LangChain about the error
      }
      throw new Error(errorMessage); // Propagate the error
    }
  }

  /**
   * --- MODIFIED FILE RETRIEVAL LOGIC ---
   * Retrieves all non-deleted knowledge file IDs associated with a specific agent
   * by joining through the 'agents_knowledge_file' pivot table.
   * @param agentId The UUID of the agent.
   * @returns A promise that resolves to an array of file ID strings.
   */
  async getAllFileIdsByAgentId(agentId: string): Promise<string[]> {
    if (!agentId) {
      this.logger.warn('getAllFileIdsByAgentId called with no agentId.');
      return [];
    }

    try {
      this.logger.debug(`Fetching all file IDs for agent ${agentId}`);

      // This query joins KnowledgeFile with the pivot table agents_knowledge_file
      // and filters by the agent_id.
      // 'kf' is the alias for the 'knowledge_files' table.
      // 'akf' is the alias for the 'agents_knowledge_file' table.
      const queryBuilder = this.knowledgeFileRepository
        .createQueryBuilder('kf')
        .innerJoin(
          'agents_knowledge_file', // The name of the pivot table in the database
          'akf', // Alias for the pivot table
          'akf.file_id = kf.id', // The JOIN condition (assuming PK of KnowledgeFile is 'id')
        )
        .select('kf.file_id', 'file_id') // Select the file_id column from the knowledge_files table
        .where('akf.agent_id = :agentId', { agentId }) // Filter by the agent's ID
        .andWhere('kf.status != :deletedStatus', {
          deletedStatus: KnowledgeFileStatus.DELETED,
        })
        .andWhere('kf.file_id IS NOT NULL');

      const result = await queryBuilder.getRawMany();

      const fileIds = result.map((item) => item.file_id).filter(Boolean); // Filter out any null/undefined values

      this.logger.debug(
        `Found ${fileIds.length} associated file IDs for agent ${agentId}`,
      );

      return fileIds;
    } catch (error) {
      this.logger.error(
        `Error fetching file IDs for agent ${agentId}: ${error.message}`,
        error.stack,
      );
      // It's safer to return an empty array than to throw an error that might crash the agent.
      return [];
    }
  }
}