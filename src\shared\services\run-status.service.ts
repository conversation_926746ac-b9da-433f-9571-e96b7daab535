import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '../../infra/redis/redis.service';
import { Redis } from 'ioredis';
import { 
  RunStatus, 
  RunStatusType, 
  RunStatusMetadata, 
  CancelReason 
} from '../run-status';

/**
 * Unified Run Status Service
 * 
 * Replaces fragmented ActiveRunService + CancellationService with a single,
 * comprehensive status tracking system using unified Redis keys.
 * 
 * Key Pattern: `run_status:in_app:{threadId}`
 * TTL: 1 hour (3600 seconds)
 */
@Injectable()
export class RunStatusService {
  private readonly logger = new Logger(RunStatusService.name);
  private readonly redis: Redis;
  private readonly defaultTtl = 3600; // 1 hour in seconds

  constructor(private readonly redisService: RedisService) {
    this.redis = this.redisService.getRawClient();
  }

  /**
   * Execute Redis operation with connection retry
   */
  private async executeWithRetry<T>(operation: () => Promise<T>, operationName: string): Promise<T> {
    try {
      return await operation();
    } catch (error) {
      // Check if it's a connection error
      if (error.code === 'ETIMEDOUT' || error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND' || 
          error.message?.includes('Connection is closed') || error.message?.includes('connect ETIMEDOUT')) {
        this.logger.warn(`Redis connection error in ${operationName}, attempting reconnection...`, {
          error: error.message,
          code: error.code,
          syscall: error.syscall
        });
        
        try {
          // Attempt to reconnect
          await this.redisService.reconnect();
          
          // Retry the operation once after reconnection
          this.logger.log(`Retrying ${operationName} after reconnection`);
          return await operation();
        } catch (retryError) {
          this.logger.error(`${operationName} failed even after reconnection`, {
            originalError: error.message,
            retryError: retryError.message
          });
          
          // For critical operations like activateRun, return false instead of throwing
          if (operationName === 'activateRun') {
            return false as unknown as T;
          }
          
          throw retryError;
        }
      }
      
      // If it's not a connection error, just throw it
      throw error;
    }
  }

  /**
   * Create and activate a run with ACTIVE status
   * @param platformThreadId Platform-specific thread identifier (e.g., 'in_app:thread123')
   * @param runId Run identifier for validation
   * @param context Additional context data
   * @returns True if activated successfully
   */
  async activateRun(
    platformThreadId: string, 
    runId: string, 
    context?: Record<string, any>
  ): Promise<boolean> {
    return this.executeWithRetry(async () => {
      // Check if there's already an active run - if so, we need validation
      const currentStatus = await this.getRunStatus(platformThreadId);
      
      if (currentStatus) {
        // If it's already active with the same runId, consider it success
        if (currentStatus.status === RunStatusType.ACTIVE && currentStatus.metadata.runId === runId) {
          this.logger.debug(`Run already active for ${platformThreadId} with same runId ${runId}`);
          return true;
        }
        
        // If it's active with different runId, reject
        if (currentStatus.status === RunStatusType.ACTIVE) {
          this.logger.error(`Cannot activate run - another run is already active for ${platformThreadId}`, {
            existingRunId: currentStatus.metadata.runId,
            existingStatus: currentStatus.status,
            newRunId: runId
          });
          return false;
        }
        
        // If status is terminal (COMPLETED, CANCELLED, FAILED), we can proceed with new run
        this.logger.debug(`Previous run finished with status ${currentStatus.status}, proceeding with new run`, {
          previousRunId: currentStatus.metadata.runId,
          previousStatus: currentStatus.status,
          newRunId: runId
        });
      }

      // Create new run status as ACTIVE
      const now = new Date().toISOString();
      const runStatus: RunStatus = {
        status: RunStatusType.ACTIVE,
        metadata: {
          createdAt: now,
          updatedAt: now,
          threadId: platformThreadId,
          runId,
          context
        }
      };

      const key = this.generateRunStatusKey(platformThreadId);
      const result = await this.redis.setex(
        key, 
        this.defaultTtl, 
        JSON.stringify(runStatus)
      );

      if (result === 'OK') {
        this.logger.log(`Created and activated run: ${platformThreadId}:${runId} -> ACTIVE`);
        return true;
      }
      
      return false;
    }, 'activateRun');
  }

  /**
   * Complete a run by transitioning to COMPLETED status
   * @param platformThreadId Platform-specific thread identifier
   * @param runId Run identifier for validation
   * @param context Additional completion context
   * @returns True if completed successfully
   */
  async completeRun(
    platformThreadId: string, 
    runId: string, 
    context?: Record<string, any>
  ): Promise<boolean> {
    try {
      const currentStatus = await this.getRunStatus(platformThreadId);
      
      if (!currentStatus) {
        this.logger.warn(`Cannot complete run - no status found for ${platformThreadId}`);
        return false;
      }

      if (currentStatus.metadata.runId !== runId) {
        this.logger.warn(`Cannot complete run - run ID mismatch for ${platformThreadId}`);
        return false;
      }

      const updatedStatus: RunStatus = {
        ...currentStatus,
        status: RunStatusType.COMPLETED,
        metadata: {
          ...currentStatus.metadata,
          updatedAt: new Date().toISOString(),
          context: { ...currentStatus.metadata.context, ...context }
        }
      };

      const success = await this.setRunStatus(platformThreadId, updatedStatus);
      
      if (success) {
        this.logger.log(`Completed run: ${platformThreadId}:${runId} -> COMPLETED`);
      }
      
      return success;
    } catch (error) {
      this.logger.error(`Failed to complete run for ${platformThreadId}:${runId}`, error);
      return false;
    }
  }

  /**
   * Fail a run by transitioning to FAILED status
   * @param platformThreadId Platform-specific thread identifier
   * @param runId Run identifier for validation
   * @param errorMessage Error message
   * @param errorStack Error stack trace
   * @returns True if failed successfully
   */
  async failRun(
    platformThreadId: string, 
    runId: string, 
    errorMessage: string,
    errorStack?: string
  ): Promise<boolean> {
    try {
      const currentStatus = await this.getRunStatus(platformThreadId);
      
      if (!currentStatus) {
        this.logger.warn(`Cannot fail run - no status found for ${platformThreadId}`);
        return false;
      }

      if (currentStatus.metadata.runId !== runId) {
        this.logger.warn(`Cannot fail run - run ID mismatch for ${platformThreadId}`);
        return false;
      }

      const updatedStatus: RunStatus = {
        ...currentStatus,
        status: RunStatusType.FAILED,
        metadata: {
          ...currentStatus.metadata,
          updatedAt: new Date().toISOString(),
          errorMessage,
          errorStack
        }
      };

      const success = await this.setRunStatus(platformThreadId, updatedStatus);
      
      if (success) {
        this.logger.error(`Failed run: ${platformThreadId}:${runId} -> FAILED: ${errorMessage}`);
      }
      
      return success;
    } catch (error) {
      this.logger.error(`Failed to set run as failed for ${platformThreadId}:${runId}`, error);
      return false;
    }
  }

  /**
   * Cancel a run by transitioning to CANCELLED status
   * @param platformThreadId Platform-specific thread identifier
   * @param reason Cancellation reason
   * @param runId Optional run identifier for validation
   * @returns True if cancelled successfully
   */
  async cancelRun(
    platformThreadId: string, 
    reason: CancelReason,
    runId?: string
  ): Promise<boolean> {
    try {
      const currentStatus = await this.getRunStatus(platformThreadId);
      
      if (!currentStatus) {
        this.logger.warn(`Cannot cancel run - no status found for ${platformThreadId}`);
        return false;
      }

      if (runId && currentStatus.metadata.runId !== runId) {
        this.logger.warn(`Cannot cancel run - run ID mismatch for ${platformThreadId}`);
        return false;
      }

      const updatedStatus: RunStatus = {
        ...currentStatus,
        status: RunStatusType.CANCELLED,
        metadata: {
          ...currentStatus.metadata,
          updatedAt: new Date().toISOString(),
          cancelReason: reason
        }
      };

      const success = await this.setRunStatus(platformThreadId, updatedStatus);
      
      if (success) {
        this.logger.log(`Cancelled run: ${platformThreadId}:${currentStatus.metadata.runId} -> CANCELLED (${reason})`);
      }
      
      return success;
    } catch (error) {
      this.logger.error(`Failed to cancel run for ${platformThreadId}`, error);
      return false;
    }
  }

  /**
   * Check if a run is currently active
   * @param platformThreadId Platform-specific thread identifier
   * @returns True if run is active
   */
  async isRunActive(platformThreadId: string): Promise<boolean> {
    try {
      const status = await this.getRunStatus(platformThreadId);
      return status?.status === RunStatusType.ACTIVE;
    } catch (error) {
      this.logger.error(`Failed to check if run is active for ${platformThreadId}`, error);
      return false;
    }
  }

  /**
   * Check if a run has been cancelled
   * @param platformThreadId Platform-specific thread identifier
   * @returns True if run is cancelled
   */
  async isRunCancelled(platformThreadId: string): Promise<boolean> {
    try {
      const status = await this.getRunStatus(platformThreadId);
      return status?.status === RunStatusType.CANCELLED;
    } catch (error) {
      this.logger.error(`Failed to check if run is cancelled for ${platformThreadId}`, error);
      return false;
    }
  }

  /**
   * Get the current run ID for a thread
   * @param platformThreadId Platform-specific thread identifier
   * @returns Current run ID or null if no active run
   */
  async getCurrentRunId(platformThreadId: string): Promise<string | null> {
    try {
      const status = await this.getRunStatus(platformThreadId);
      return status?.metadata.runId || null;
    } catch (error) {
      this.logger.error(`Failed to get current run ID for ${platformThreadId}`, error);
      return null;
    }
  }

  /**
   * Get the complete run status object
   * @param platformThreadId Platform-specific thread identifier
   * @returns Run status object or null if not found
   */
  async getRunStatus(platformThreadId: string): Promise<RunStatus | null> {
    return this.executeWithRetry(async () => {
      const key = this.generateRunStatusKey(platformThreadId);
      const data = await this.redis.get(key);
      
      if (!data) {
        return null;
      }

      return JSON.parse(data) as RunStatus;
    }, 'getRunStatus');
  }

  /**
   * Set the run status object
   * @param platformThreadId Platform-specific thread identifier
   * @param status Run status object
   * @returns True if set successfully
   */
  async setRunStatus(platformThreadId: string, status: RunStatus): Promise<boolean> {
    return this.executeWithRetry(async () => {
      const key = this.generateRunStatusKey(platformThreadId);
      const result = await this.redis.setex(
        key,
        this.defaultTtl,
        JSON.stringify(status)
      );

      return result === 'OK';
    }, 'setRunStatus');
  }

  /**
   * Clear run status for a thread
   * @param platformThreadId Platform-specific thread identifier
   * @returns True if cleared successfully
   */
  async clearRunStatus(platformThreadId: string): Promise<boolean> {
    return this.executeWithRetry(async () => {
      const key = this.generateRunStatusKey(platformThreadId);
      const result = await this.redis.del(key);
      
      this.logger.log(`Cleared run status for ${platformThreadId}`);
      return result > 0;
    }, 'clearRunStatus');
  }

  /**
   * Generate Redis key for run status
   * @param platformThreadId Platform-specific thread identifier (e.g., 'in_app:thread123')
   * @returns Redis key
   */
  generateRunStatusKey(platformThreadId: string): string {
    return `run_status:${platformThreadId}`;
  }
}
