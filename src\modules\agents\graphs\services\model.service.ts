import { Injectable } from '@nestjs/common';
import { ModelProviderEnum } from '../../enums';
import { initChatModel } from 'langchain/chat_models/universal';

@Injectable()
export class ModelService {
  async createModelWithTools(param: {
    modelName: string;
    modelProvider: ModelProviderEnum;
    modelParameters: {
      temperature?: number;
      topP?: number;
      topK?: number;
      maxTokens?: number;
      maxOutputTokens?: number;
    };
    apiKey: string;
    tools?: any[];
    toolChoices?: string | undefined;
  }) {
    const model = await initChatModel(
      `${param.modelProvider.toLowerCase()}:${param.modelName}`,
      {
        configurableFields: Object.keys(param.modelParameters),
        ...param.modelParameters,
        apiKey: param.apiKey,
      },
    );
    const tools = param?.tools || [];
    return model.bindTools(tools.slice(0, 128), {
      tool_choice: param.toolChoices,
    }); // limit to 128 common-tools
  }
}
