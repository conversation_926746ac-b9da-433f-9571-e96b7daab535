/**
 * Interface cho job data gửi chuỗi tin nhắn group hàng loạt
 */
export interface ZaloGroupMessageSequenceJobData {
  /**
   * ID người dùng
   */
  userId: string;

  /**
   * ID Integration
   */
  integrationId: string;

  /**
   * Danh sách ID nhóm chat cần gửi tin nhắn
   */
  groupIds: string[];

  /**
   * Danh sách tin nhắn trong chuỗi
   */
  messages: GroupMessageItemDto[];

  /**
   * Thời gian tạo job
   */
  timestamp: number;

  /**
   * ID để tracking job (optional)
   */
  trackingId?: string;
}

/**
 * Enum định nghĩa các loại tin nhắn group
 */
export enum GroupMessageType {
  TEXT = 'text',
  IMAGE = 'image',
  FILE = 'file',
  STICKER = 'sticker',
  MENTION = 'mention',
}

/**
 * Interface cho một tin nhắn trong chuỗi tin nhắn group
 */
export interface GroupMessageItemDto {
  /**
   * Loại tin nhắn group
   */
  messageType: GroupMessageType;

  /**
   * Thời gian delay trước khi gửi tin nhắn này (giây)
   */
  delaySeconds?: number;

  // TEXT message fields
  /**
   * Nội dung tin nhắn văn bản (bắt buộc cho TEXT)
   */
  content?: string;

  // IMAGE message fields
  /**
   * URL hình ảnh (bắt buộc cho IMAGE nếu không có attachmentId)
   */
  imageUrl?: string;

  /**
   * ID của attachment đã upload (bắt buộc cho IMAGE nếu không có imageUrl)
   */
  attachmentId?: string;

  /**
   * Chú thích cho hình ảnh (tùy chọn cho IMAGE)
   */
  caption?: string;

  // FILE message fields
  /**
   * URL file (bắt buộc cho FILE)
   */
  fileUrl?: string;

  /**
   * Tên file (bắt buộc cho FILE)
   */
  filename?: string;

  // STICKER message fields
  /**
   * ID sticker (bắt buộc cho STICKER)
   */
  stickerId?: string;

  // MENTION message fields
  /**
   * Danh sách User ID được mention (bắt buộc cho MENTION)
   */
  mentionUids?: string[];

  /**
   * Metadata bổ sung
   */
  metadata?: Record<string, any>;
}

/**
 * Interface cho kết quả xử lý job
 */
export interface ZaloGroupMessageSequenceJobResult {
  /**
   * Tổng số tin nhắn đã gửi
   */
  totalSent: number;

  /**
   * Số tin nhắn gửi thành công
   */
  successCount: number;

  /**
   * Số tin nhắn gửi thất bại
   */
  failureCount: number;

  /**
   * Chi tiết kết quả gửi tin nhắn
   */
  results: Array<{
    groupId: string;
    messageIndex: number;
    messageId?: string;
    status: 'sent' | 'failed';
    error?: string;
  }>;

  /**
   * Thời gian bắt đầu xử lý
   */
  startTime: number;

  /**
   * Thời gian kết thúc xử lý
   */
  endTime: number;

  /**
   * Tracking ID
   */
  trackingId?: string;
}
