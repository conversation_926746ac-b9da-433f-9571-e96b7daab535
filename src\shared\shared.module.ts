import { HttpModule } from '@nestjs/axios';
import { Global, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SystemConfiguration } from '../modules/system-configuration/entities/system-configuration.entity';
import { IntegrationProvider } from './entities/integration-provider.entity';
import { Integration } from './entities/integration.entity';
import { AutomationWebService } from './services/automation-web.service';
import { EmailService } from './services/email.service';
import { EncryptionService } from './services/encryption/encryption.service';
import { KeyPairEncryptionService } from './services/encryption/key-pair-encryption.service';
import { FacebookModule } from './services/facebook';
import { GoogleApiModule } from './services/google';
import { RunStatusService } from './services/run-status.service';
import { SystemEmailConfigurationService } from './services/system-email-configuration.service';
import { ZaloModule } from './services/zalo';


/**
 * Module chứa các service dùng chung trong toàn bộ ứng dụng
 */
@Global()
@Module({
  imports: [
    TypeOrmModule.forFeature([
      SystemConfiguration,
      Integration,
      IntegrationProvider,
    ]),
    ConfigModule,
    HttpModule,
    ClientsModule.registerAsync([
      {
        name: 'REDIS_CLIENT',
        imports: [ConfigModule],
        useFactory: async (configService: ConfigService) => {
          const redisUrl = configService.get<string>('REDIS_URL', 'redis://localhost:6379');
          const url = new URL(redisUrl);

          return {
            transport: Transport.REDIS,
            options: {
              host: url.hostname,
              port: parseInt(url.port) || 6379,
              retryAttempts: 5,
              retryDelay: 3000,
              wildcards: true,
            },
          };
        },
        inject: [ConfigService],
      },
    ]), // Commented out temporarily to avoid Redis connection errors
    GoogleApiModule,
    FacebookModule,
    ZaloModule,
  ],
  providers: [
    EmailService,
    RunStatusService,
    SystemEmailConfigurationService,
    EncryptionService,
    KeyPairEncryptionService,
    AutomationWebService,
  ],
  exports: [
    EmailService,
    RunStatusService,
    SystemEmailConfigurationService,
    EncryptionService,
    KeyPairEncryptionService,
    AutomationWebService,
    GoogleApiModule,
    FacebookModule,
    ZaloModule,
  ],
})
export class SharedModule { }
