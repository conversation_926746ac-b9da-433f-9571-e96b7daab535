# ---- Builder Stage ----
# This stage installs all dependencies and builds the application
FROM node:22.17.1-alpine3.21 AS builder
WORKDIR /app

COPY package*.json ./
# Use a cache mount to speed up dependency installation
# Note: Using 'npm ci' is recommended if you have a package-lock.json file.
RUN --mount=type=cache,target=/root/.npm npm install

COPY . .
RUN npm run build


# ---- Production Stage ----
# This stage creates the final, lean image for production
FROM node:22.17.1-alpine3.21
WORKDIR /app

ENV NODE_ENV=production

# Create a dedicated non-root user for better security
RUN addgroup -S appgroup && adduser -S appuser -G appgroup

COPY package*.json ./
# Install only production dependencies with a cache mount
RUN --mount=type=cache,target=/root/.npm npm install --omit=dev

# Copy built app from the builder stage
# The 'dist' folder is a common output; adjust if yours is different.
COPY --from=builder /app/dist ./dist

# Set correct permissions for the new user
RUN chown -R appuser:appgroup /app

# Switch to the non-root user
USER appuser

EXPOSE 3008

# Start the application directly with Node.js
# Replace 'dist/main.js' if your entrypoint is different.
CMD ["node", "dist/main.js"]