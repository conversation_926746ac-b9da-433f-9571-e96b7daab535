/**
 * Interface cho LLM Key Integration với thông tin provider
 * Sử dụng cho query kết hợp giữa integration và integration_providers
 */
export interface LlmKeyIntegrationWithProvider {
  /**
   * UUID của integration
   */
  id: string;

  /**
   * Tên của integration
   */
  integration_name: string;

  /**
   * Thời điểm tạo (epoch milliseconds)
   */
  created_at: number;

  /**
   * Metadata bổ sung dưới dạng JSONB
   */
  metadata: Record<string, any> | null;

  /**
   * Loại provider từ bảng integration_providers
   */
  provider_type: string;
}

/**
 * Interface cho raw result từ TypeORM query
 * Sử dụng khi query với getRawMany() hoặc getRawAndEntities()
 */
export interface LlmKeyIntegrationRaw {
  llm_key_id: string;
  llm_key_integration_name: string;
  llm_key_created_at: number;
  llm_key_metadata: Record<string, any> | null;
  integartion_provider_type: string;
}
