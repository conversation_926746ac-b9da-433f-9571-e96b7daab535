import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';

/**
 * Service để cập nhật trạng thái campaign từ worker về be-app
 */
@Injectable()
export class CampaignStatusUpdateService {
  private readonly logger = new Logger(CampaignStatusUpdateService.name);
  private readonly beAppBaseUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.beAppBaseUrl = this.configService.get<string>('BE_APP_BASE_URL') || 'http://localhost:3000';
  }

  /**
   * Cập nhật trạng thái ZNS campaign
   * @param campaignId ID chiến dịch
   * @param status Trạng thái mới
   * @param sentMessages Số tin nhắn đã gửi
   * @param failedMessages Số tin nhắn thất bại
   */
  async updateZnsCampaignStatus(
    campaignId: number,
    status: string,
    sentMessages?: number,
    failedMessages?: number,
  ): Promise<void> {
    try {
      const updateData: any = {
        status,
      };

      if (sentMessages !== undefined) {
        updateData.sentMessages = sentMessages;
      }

      if (failedMessages !== undefined) {
        updateData.failedMessages = failedMessages;
      }

      const url = `${this.beAppBaseUrl}/api/internal/zns-campaigns/${campaignId}/status`;
      
      this.logger.debug(
        `📡 Updating ZNS campaign ${campaignId} status via ${url}`,
        updateData,
      );

      const response = await lastValueFrom(
        this.httpService.patch(url, updateData, {
          headers: {
            'Content-Type': 'application/json',
            'X-Internal-Service': 'worker',
          },
          timeout: 5000, // 5 second timeout
        }),
      );

      if (response.status === 200) {
        this.logger.log(
          `✅ Successfully updated ZNS campaign ${campaignId} status to ${status}`,
        );
      } else {
        this.logger.warn(
          `⚠️ Unexpected response status ${response.status} when updating campaign ${campaignId}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `❌ Failed to update ZNS campaign ${campaignId} status: ${error.message}`,
        {
          campaignId,
          status,
          sentMessages,
          failedMessages,
          error: error.response?.data || error.message,
        },
      );
      
      // Không throw error để không làm fail job
      // Campaign status update là optional operation
    }
  }

  /**
   * Cập nhật trạng thái SMS campaign
   * @param campaignId ID chiến dịch
   * @param status Trạng thái mới
   * @param sentMessages Số tin nhắn đã gửi
   * @param failedMessages Số tin nhắn thất bại
   */
  async updateSmsCampaignStatus(
    campaignId: number,
    status: string,
    sentMessages?: number,
    failedMessages?: number,
  ): Promise<void> {
    try {
      const updateData: any = {
        status,
      };

      if (sentMessages !== undefined) {
        updateData.sentMessages = sentMessages;
      }

      if (failedMessages !== undefined) {
        updateData.failedMessages = failedMessages;
      }

      const url = `${this.beAppBaseUrl}/api/internal/sms-campaigns/${campaignId}/status`;
      
      this.logger.debug(
        `📡 Updating SMS campaign ${campaignId} status via ${url}`,
        updateData,
      );

      const response = await lastValueFrom(
        this.httpService.patch(url, updateData, {
          headers: {
            'Content-Type': 'application/json',
            'X-Internal-Service': 'worker',
          },
          timeout: 5000,
        }),
      );

      if (response.status === 200) {
        this.logger.log(
          `✅ Successfully updated SMS campaign ${campaignId} status to ${status}`,
        );
      } else {
        this.logger.warn(
          `⚠️ Unexpected response status ${response.status} when updating SMS campaign ${campaignId}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `❌ Failed to update SMS campaign ${campaignId} status: ${error.message}`,
        {
          campaignId,
          status,
          sentMessages,
          failedMessages,
          error: error.response?.data || error.message,
        },
      );
    }
  }

  /**
   * Cập nhật trạng thái Email campaign
   * @param campaignId ID chiến dịch
   * @param status Trạng thái mới
   * @param sentEmails Số email đã gửi
   * @param failedEmails Số email thất bại
   */
  async updateEmailCampaignStatus(
    campaignId: number,
    status: string,
    sentEmails?: number,
    failedEmails?: number,
  ): Promise<void> {
    try {
      const updateData: any = {
        status,
      };

      if (sentEmails !== undefined) {
        updateData.sentEmails = sentEmails;
      }

      if (failedEmails !== undefined) {
        updateData.failedEmails = failedEmails;
      }

      const url = `${this.beAppBaseUrl}/api/internal/email-campaigns/${campaignId}/status`;
      
      this.logger.debug(
        `📡 Updating Email campaign ${campaignId} status via ${url}`,
        updateData,
      );

      const response = await lastValueFrom(
        this.httpService.patch(url, updateData, {
          headers: {
            'Content-Type': 'application/json',
            'X-Internal-Service': 'worker',
          },
          timeout: 5000,
        }),
      );

      if (response.status === 200) {
        this.logger.log(
          `✅ Successfully updated Email campaign ${campaignId} status to ${status}`,
        );
      } else {
        this.logger.warn(
          `⚠️ Unexpected response status ${response.status} when updating Email campaign ${campaignId}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `❌ Failed to update Email campaign ${campaignId} status: ${error.message}`,
        {
          campaignId,
          status,
          sentEmails,
          failedEmails,
          error: error.response?.data || error.message,
        },
      );
    }
  }

  /**
   * Health check để kiểm tra kết nối với be-app
   */
  async healthCheck(): Promise<boolean> {
    try {
      const url = `${this.beAppBaseUrl}/api/health`;
      const response = await lastValueFrom(
        this.httpService.get(url, {
          timeout: 3000,
        }),
      );

      return response.status === 200;
    } catch (error) {
      this.logger.error(
        `❌ Health check failed for be-app: ${error.message}`,
      );
      return false;
    }
  }
}
