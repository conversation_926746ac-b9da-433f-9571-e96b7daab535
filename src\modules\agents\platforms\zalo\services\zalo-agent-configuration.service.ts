import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AgentConfig } from '../../../shared/interfaces/agent-config.interface';
import { UserConvertCustomerMemory } from '../../../domains/external/entities/user-convert-customer-memory.entity';
import { ZaloAgentConfigBuilderService } from './zalo-agent-config-builder.service';
import {
  ThreadKnowledgeFileAttachments,
  ThreadMediaAttachments,
  UserConvertCustomerInfo,
  UserInfo,
} from '../../../shared/interfaces';
import { ZaloInfo } from '../interfaces/zalo-info.interface';
import {
  buildThreadAttachmentsPrompt,
  buildZaloUserContextPrompt,
} from '../../../common-prompt-builders';

/**
 * Zalo Agent Configuration Service
 *
 * Handles Zalo-specific agent configuration logic including:
 * - Dual-agent setup (planner + executor)
 * - Zalo user memory integration
 * - Zalo-specific prompt building using common prompt builders
 * - Business context and Zalo user data integration
 */
@Injectable()
export class ZaloAgentConfigurationService {
  private readonly logger = new Logger(ZaloAgentConfigurationService.name);

  constructor(
    @InjectRepository(UserConvertCustomerMemory)
    private readonly userConvertCustomerMemoryRepository: Repository<UserConvertCustomerMemory>,
    private readonly zaloAgentConfigBuilder: ZaloAgentConfigBuilderService,
  ) {}

  /**
   * Build planner-executor configuration for Zalo platform
   *
   * @param zaloUser The Zalo user/customer
   * @param zaloOwner Zalo OA owner information for API key context
   * @param zaloInfo Rich Zalo context (OA info, user details, etc.)
   * @param threadMediaAttachments Images from the conversation thread
   * @param threadKnowledgeFileAttachments Knowledge files from the conversation thread
   * @returns Object containing both planner and executor agent configurations
   */
  async buildPlannerExecutorConfiguration(param: {
    zaloUser: UserConvertCustomerInfo;
    zaloOwner: UserInfo;
    zaloInfo: ZaloInfo;
    threadMediaAttachments: ThreadMediaAttachments;
    threadKnowledgeFileAttachments?: ThreadKnowledgeFileAttachments;
    plannerAgentId?: string;
    executorAgentId?: string;
  }): Promise<{
    plannerAgent?: AgentConfig;
    executorAgent: AgentConfig;
  }> {
    const {
      zaloUser,
      zaloOwner,
      zaloInfo,
      threadMediaAttachments,
      threadKnowledgeFileAttachments,
      plannerAgentId,
      executorAgentId,
    } = param;

    this.logger.debug(
      'Building planner-executor configuration for Zalo platform',
      {
        zaloUserId: zaloUser.id,
        zaloOwnerId: zaloOwner.userId,
        oaId: zaloInfo.oaId,
        plannerAgentId,
        executorAgentId,
        threadMediaCount: Object.keys(threadMediaAttachments).length,
        threadKnowledgeFileCount: threadKnowledgeFileAttachments
          ? Object.keys(threadKnowledgeFileAttachments).length
          : 0,
      },
    );

    if (!executorAgentId) {
      throw new Error('ExecutorAgentId is required for Zalo platform');
    }

    // Step 1: Build prompt builder map with Zalo-specific prompts using common builders
    const promptBuilderMap = await this.buildPromptBuilderMap({
      zaloUser,
      zaloOwner,
      zaloInfo,
      threadMediaAttachments,
      threadKnowledgeFileAttachments,
      plannerAgentId,
      executorAgentId,
    });

    // Step 2: Prepare agent IDs for configuration
    const agentIds = [
      ...(plannerAgentId ? [{ id: plannerAgentId }] : []),
      { id: executorAgentId },
    ];

    // Step 3: Build agent configurations using our facade service
    const agentConfigs = await this.zaloAgentConfigBuilder.buildAgentConfigs({
      agentIds,
      promptBuilderMap,
      zaloUserId: zaloUser.id,
      zaloOwnerId: zaloOwner.userId,
      zaloData: { zaloOwner, zaloInfo }, // Pass structured data
    });

    // Step 4: Return structured result
    const result = {
      plannerAgent: plannerAgentId ? agentConfigs[plannerAgentId] : undefined,
      executorAgent: agentConfigs[executorAgentId],
    };

    this.logger.debug('Planner-executor configuration built successfully', {
      hasPlanner: !!result.plannerAgent,
      hasExecutor: !!result.executorAgent,
      executorTools: result.executorAgent?.tools?.length || 0,
    });

    return result;
  }

  /**
   * Build prompt builder map with Zalo-specific prompts using common prompt builders
   * Memory is fetched directly within the Zalo user context prompt builder via repository injection
   */
  private async buildPromptBuilderMap(param: {
    zaloUser: UserConvertCustomerInfo;
    zaloOwner: UserInfo;
    zaloInfo: ZaloInfo;
    threadMediaAttachments: ThreadMediaAttachments;
    threadKnowledgeFileAttachments?: ThreadKnowledgeFileAttachments;
    plannerAgentId?: string;
    executorAgentId: string;
  }): Promise<Record<string, Array<() => string | Promise<string>>>> {
    const {
      zaloUser,
      zaloOwner,
      zaloInfo,
      threadMediaAttachments,
      threadKnowledgeFileAttachments,
      plannerAgentId,
      executorAgentId,
    } = param;

    const promptBuilderMap: Record<
      string,
      Array<() => string | Promise<string>>
    > = {};

    // Common prompt builders for both agents using common prompt builders
    const commonPromptBuilders = [
      () =>
        buildZaloUserContextPrompt(
          zaloUser.id,
          this.userConvertCustomerMemoryRepository, // Repository injection pattern
          zaloUser,
          zaloOwner,
          zaloInfo,
        ),
      () =>
        buildThreadAttachmentsPrompt(
          threadMediaAttachments,
          threadKnowledgeFileAttachments,
        ),
    ];

    // Planner-specific prompts
    if (plannerAgentId) {
      promptBuilderMap[plannerAgentId] = [...commonPromptBuilders];
    }

    // Executor-specific prompts
    promptBuilderMap[executorAgentId] = [...commonPromptBuilders];

    this.logger.debug('Built prompt builder map', {
      plannerPrompts: plannerAgentId
        ? promptBuilderMap[plannerAgentId].length
        : 0,
      executorPrompts: promptBuilderMap[executorAgentId].length,
    });

    return promptBuilderMap;
  }
}