import { Injectable, Logger } from '@nestjs/common';
import {
  AddNodeResult,
  GetValueResult,
  JsonContextOptions
} from '../interfaces/workflow-context.interface';

/**
 * ✅ SIMPLIFIED WORKFLOW CONTEXT SERVICE
 * Core service cho việc thao tác với JSON context
 */
@Injectable()
export class WorkflowContextService {
  private readonly logger = new Logger(WorkflowContextService.name);

  /**
   * Thêm node output vào JSON context
   */
  addNodeToJsonContext(
    json: Record<string, Record<string, any>>,
    nodeName: string,
    nodeData: Record<string, any>,
    options: JsonContextOptions = {}
  ): Record<string, Record<string, any>> {

    const {
      overwrite = false,
      validateData = true,
      sanitizeNodeName = true
    } = options;

    // 1. Validate inputs
    if (!nodeName || typeof nodeName !== 'string') {
      throw new Error('Node name must be a non-empty string');
    }

    if (!nodeData || typeof nodeData !== 'object' || Array.isArray(nodeData)) {
      throw new Error('Node data must be a non-null object');
    }

    // 2. Sanitize node name
    let sanitizedNodeName = nodeName;
    if (sanitizeNodeName) {
      sanitizedNodeName = nodeName
        .toLowerCase()
        .replace(/[^a-z0-9_]/g, '_')
        .replace(/_{2,}/g, '_')
        .replace(/^_|_$/g, '');

      if (!sanitizedNodeName) {
        throw new Error(`Invalid node name: "${nodeName}"`);
      }
    }

    // 3. Check for existing node
    if (json[sanitizedNodeName] && !overwrite) {
      throw new Error(`Node "${sanitizedNodeName}" already exists. Use overwrite option to replace.`);
    }

    // 4. Validate node data structure
    if (validateData) {
      try {
        JSON.stringify(nodeData);
      } catch (error) {
        throw new Error(`Node data is not serializable: ${error.message}`);
      }
    }

    // 5. Create updated JSON context
    const updatedJson = { ...json };
    updatedJson[sanitizedNodeName] = { ...nodeData };

    this.logger.debug(`Added node "${sanitizedNodeName}" to JSON context`, {
      nodeName: sanitizedNodeName,
      dataKeys: Object.keys(nodeData)
    });

    return updatedJson;
  }

  /**
   * Lấy dữ liệu từ JSON context theo path
   */
  getValueFromJsonContext(
    json: Record<string, Record<string, any>>,
    path: string,
    options: JsonContextOptions = {}
  ): any {

    const {
      defaultValue = undefined,
      throwOnNotFound = false,
      caseSensitive = true
    } = options;

    // 1. Validate inputs
    if (!path || typeof path !== 'string') {
      if (throwOnNotFound) {
        throw new Error('Path must be a non-empty string');
      }
      return defaultValue;
    }

    if (!json || typeof json !== 'object') {
      if (throwOnNotFound) {
        throw new Error('JSON context must be a valid object');
      }
      return defaultValue;
    }

    // 2. Parse path - remove "json." prefix if present
    let cleanPath = path.trim();
    if (cleanPath.startsWith('json.')) {
      cleanPath = cleanPath.substring(5);
    }

    // 3. Split path into segments
    const pathSegments = cleanPath.split('.');
    if (pathSegments.length === 0) {
      if (throwOnNotFound) {
        throw new Error(`Invalid path: "${path}"`);
      }
      return defaultValue;
    }

    // 4. Navigate through path
    let current: any = json;
    const traversedPath: string[] = [];

    for (let i = 0; i < pathSegments.length; i++) {
      const segment = pathSegments[i];
      traversedPath.push(segment);

      if (current === null || current === undefined) {
        if (throwOnNotFound) {
          throw new Error(`Cannot access property "${segment}" of ${current} at path "${traversedPath.join('.')}"`);
        }
        return defaultValue;
      }

      // Handle case sensitivity
      let actualKey = segment;
      if (!caseSensitive && typeof current === 'object' && !Array.isArray(current)) {
        const keys = Object.keys(current);
        const foundKey = keys.find(key => key.toLowerCase() === segment.toLowerCase());
        if (foundKey) {
          actualKey = foundKey;
        }
      }

      // Handle array access [index]
      if (actualKey.includes('[') && actualKey.includes(']')) {
        const arrayMatch = actualKey.match(/^([^[]+)\[(\d+)\]$/);
        if (arrayMatch) {
          const [, arrayKey, indexStr] = arrayMatch;
          const index = parseInt(indexStr, 10);

          if (!(arrayKey in current)) {
            if (throwOnNotFound) {
              throw new Error(`Property "${arrayKey}" not found at path "${traversedPath.join('.')}"`);
            }
            return defaultValue;
          }

          const arrayValue = current[arrayKey];
          if (!Array.isArray(arrayValue)) {
            if (throwOnNotFound) {
              throw new Error(`Property "${arrayKey}" is not an array at path "${traversedPath.join('.')}"`);
            }
            return defaultValue;
          }

          if (index < 0 || index >= arrayValue.length) {
            if (throwOnNotFound) {
              throw new Error(`Array index ${index} out of bounds for "${arrayKey}" at path "${traversedPath.join('.')}"`);
            }
            return defaultValue;
          }

          current = arrayValue[index];
          continue;
        }
      }

      // Regular property access
      if (typeof current === 'object' && !Array.isArray(current) && actualKey in current) {
        current = current[actualKey];
      } else {
        if (throwOnNotFound) {
          throw new Error(`Property "${actualKey}" not found at path "${traversedPath.join('.')}"`);
        }
        return defaultValue;
      }
    }

    this.logger.debug(`Retrieved value from path "${path}"`, {
      path,
      cleanPath,
      valueType: typeof current
    });

    return current;
  }

  /**
   * Thêm nhiều nodes cùng lúc vào JSON context
   */
  addMultipleNodesToJsonContext(
    json: Record<string, Record<string, any>>,
    nodes: Record<string, Record<string, any>>,
    options: JsonContextOptions = {}
  ): {
    updatedJson: Record<string, Record<string, any>>;
    results: AddNodeResult[];
  } {

    const { skipInvalid = false } = options;
    let updatedJson = { ...json };
    const results: AddNodeResult[] = [];

    for (const [nodeName, nodeData] of Object.entries(nodes)) {
      try {
        updatedJson = this.addNodeToJsonContext(updatedJson, nodeName, nodeData, options);
        results.push({ nodeName, success: true });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        results.push({ nodeName, success: false, error: errorMessage });

        if (!skipInvalid) {
          throw error;
        }
      }
    }

    this.logger.debug(`Added multiple nodes to JSON context`, {
      totalNodes: Object.keys(nodes).length,
      successCount: results.filter(r => r.success).length,
      failureCount: results.filter(r => !r.success).length
    });

    return { updatedJson, results };
  }

  /**
   * Lấy nhiều values cùng lúc từ JSON context
   */
  getMultipleValuesFromJsonContext(
    json: Record<string, Record<string, any>>,
    paths: string[],
    options: JsonContextOptions = {}
  ): Record<string, any> | GetValueResult {

    const { includeMetadata = false } = options;
    const values: Record<string, any> = {};
    const metadata: Record<string, { success: boolean; error?: string }> = {};

    for (const path of paths) {
      try {
        const value = this.getValueFromJsonContext(json, path, options);
        values[path] = value;

        if (includeMetadata) {
          metadata[path] = { success: true };
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);

        if (includeMetadata) {
          values[path] = options.defaultValue;
          metadata[path] = { success: false, error: errorMessage };
        } else {
          throw error;
        }
      }
    }

    this.logger.debug(`Retrieved multiple values from JSON context`, {
      pathCount: paths.length,
      successCount: includeMetadata ? Object.values(metadata).filter(m => m.success).length : paths.length
    });

    return includeMetadata ? { values, metadata } : values;
  }

  /**
   * Kiểm tra xem path có tồn tại trong JSON context không
   */
  hasPathInJsonContext(
    json: Record<string, Record<string, any>>,
    path: string,
    options: JsonContextOptions = {}
  ): boolean {

    const notFoundSymbol = Symbol('NOT_FOUND');

    try {
      const value = this.getValueFromJsonContext(json, path, {
        ...options,
        throwOnNotFound: false,
        defaultValue: notFoundSymbol
      });

      return value !== notFoundSymbol;
    } catch {
      return false;
    }
  }

  /**
   * Lấy tất cả paths có sẵn trong JSON context
   */
  getAllPathsFromJsonContext(
    json: Record<string, Record<string, any>>,
    options: JsonContextOptions = {}
  ): string[] {

    const {
      maxDepth = 5,
      includeArrayIndices = false,
      prefix = 'json'
    } = options;

    const paths: string[] = [];

    const traverse = (obj: any, currentPath: string, depth: number) => {
      if (depth > maxDepth || obj === null || obj === undefined) {
        return;
      }

      if (typeof obj === 'object' && !Array.isArray(obj)) {
        for (const [key, value] of Object.entries(obj)) {
          const newPath = currentPath ? `${currentPath}.${key}` : key;
          paths.push(`${prefix}.${newPath}`);
          traverse(value, newPath, depth + 1);
        }
      } else if (Array.isArray(obj) && includeArrayIndices) {
        obj.forEach((item, index) => {
          const newPath = `${currentPath}[${index}]`;
          paths.push(`${prefix}.${newPath}`);
          traverse(item, newPath, depth + 1);
        });
      }
    };

    traverse(json, '', 0);

    this.logger.debug(`Generated all paths from JSON context`, {
      totalPaths: paths.length,
      maxDepth,
      includeArrayIndices
    });

    return paths;
  }
}
