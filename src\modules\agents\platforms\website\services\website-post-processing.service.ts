import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AIMessage } from '@langchain/core/messages';
import { PlannerExecutorGraph } from '../../../graphs/multi-agents-graphs/planner-executor.graph';
import { TokenUsageCollector } from '../../../utils';
import { UserBillingService } from '../../../services';
import { RunStatusService } from '../../../../../shared/services';
import { RunStatusType } from '../../../../../shared/run-status';
import { RedisService } from 'src/infra/redis';
import { ExternalMessageOperationsService } from './external-message-operations.service';
import { ExternalConversationMessage } from '../../../entities/externals/external-conversation-message.entity';
import { MessageRole } from '../../../enums';
import { WebsiteJobData } from '../interfaces/website-job-data.interface';

/**
 * Website Post-Processing Service
 *
 * Handles post-execution operations for website platform.
 * Performs billing, text finalization, cleanup, and status management.
 *
 * Key Features:
 * - Token usage billing for website owners (business owners pay)
 * - Accumulated text finalization for executor agent
 * - Empty message cleanup for ExternalConversationMessage
 * - Redis key cleanup and run status management
 * - PlannerExecutorGraph state updates
 */
@Injectable()
export class WebsitePostProcessingService {
  private readonly logger = new Logger(WebsitePostProcessingService.name);

  constructor(
    private readonly userBilling: UserBillingService,
    private readonly redisService: RedisService,
    private readonly messageOperations: ExternalMessageOperationsService,
    private readonly plannerExecutorGraph: PlannerExecutorGraph,
    private readonly runStatusService: RunStatusService,
    @InjectRepository(ExternalConversationMessage)
    private readonly messageRepo: Repository<ExternalConversationMessage>,
  ) {}

  /**
   * Perform all post-stream operations for website platform
   * Always executes regardless of success/failure/cancellation
   */
  async performPostStreamOperations(
    jobData: WebsiteJobData,
    tokenUsageCollector: TokenUsageCollector,
    currentMessageId: string | null,
  ): Promise<void> {
    const usageSummary = tokenUsageCollector.getUsageSummary();
    this.logger.debug(
      `Starting post-stream operations for run: ${jobData.runId}`,
      {
        runId: jobData.runId,
        threadId: jobData.threadId,
        visitorId: jobData.humanInfo.websiteVisitor.id,
        websiteOwnerId: jobData.humanInfo.websiteOwner.userId,
        currentMessageId,
        totalAccumulatedTextLength: usageSummary.totalAccumulatedTextLength,
        accumulatedTextByAgent: usageSummary.accumulatedTextByAgent,
      },
    );

    // 1. Process token usage and bill website owner
    await this.processTokenUsageWithErrorHandling(jobData, tokenUsageCollector);

    // 2. Finalize partially accumulated text from executor agent
    await this.finalizePartiallyAccumulatedText(
      jobData,
      tokenUsageCollector,
      currentMessageId,
    );

    // 3. Clean up Redis keys and temporary data
    await this.cleanupTemporaryData(jobData);

    this.logger.debug(
      `Completed post-stream operations for run: ${jobData.runId}`,
    );
  }

  /**
   * Process token usage with error handling - billing failures shouldn't crash
   * Website owner (business owner) pays for visitor interactions
   */
  async processTokenUsageWithErrorHandling(
    jobData: WebsiteJobData,
    tokenUsageCollector: TokenUsageCollector,
  ): Promise<void> {
    try {
      // Bill the website owner (business owner) for visitor interactions
      await this.userBilling.processTokenUsage(
        jobData.runId,
        tokenUsageCollector,
        { userId: jobData.humanInfo.websiteOwner.userId },
        jobData.platform,
      );
      this.logger.debug(
        `Successfully processed token usage for run: ${jobData.runId}`,
        {
          websiteOwnerId: jobData.humanInfo.websiteOwner.userId,
          visitorId: jobData.humanInfo.websiteVisitor.id,
        },
      );
    } catch (error) {
      this.logger.error(
        `Failed to process token usage for run ${jobData.runId}:`,
        {
          runId: jobData.runId,
          websiteOwnerId: jobData.humanInfo.websiteOwner.userId,
          error: (error as Error).message,
          stack: (error as Error).stack,
        },
      );
      // Don't throw - billing failure shouldn't fail the run
    }
  }

  /**
   * Finalize partially accumulated text from executor agent
   * Updates the current message with accumulated text and updates LangGraph state
   */
  async finalizePartiallyAccumulatedText(
    jobData: WebsiteJobData,
    tokenUsageCollector: TokenUsageCollector,
    currentMessageId: string | null,
  ): Promise<void> {
    if (!currentMessageId) {
      this.logger.debug(
        `No current message ID to finalize for run: ${jobData.runId}`,
      );
      return;
    }

    try {
      // Get accumulated text from executor agent (mainAgentId)
      const executorAccumulatedText = tokenUsageCollector.getAccumulatedText(
        jobData.mainAgentId,
      );

      this.logger.debug(
        `Finalizing partially accumulated text for message ${currentMessageId}`,
        {
          runId: jobData.runId,
          messageId: currentMessageId,
          agentId: jobData.mainAgentId,
          textLength: executorAccumulatedText.length,
          textPreview: executorAccumulatedText,
        },
      );

      if (
        !executorAccumulatedText ||
        executorAccumulatedText.trim().length === 0
      ) {
        this.logger.debug(
          `No accumulated text from executor agent ${jobData.mainAgentId} for run: ${jobData.runId}. Will be cleaned up in finally block.`,
          {
            messageId: currentMessageId,
            runId: jobData.runId,
            agentId: jobData.mainAgentId,
          },
        );
        return;
      }

      // Update the message with the accumulated text using MessageOperationsService
      const updateSuccess =
        await this.messageOperations.updateMessageTextWithValidation(
          currentMessageId,
          executorAccumulatedText,
          {
            runId: jobData.runId,
            agentId: jobData.mainAgentId,
            operation: 'finalize_partial_text',
          },
        );

      if (!updateSuccess) {
        this.logger.warn(
          `Failed to update message ${currentMessageId} with accumulated text`,
          {
            runId: jobData.runId,
            messageId: currentMessageId,
            agentId: jobData.mainAgentId,
            textLength: executorAccumulatedText.length,
          },
        );
        return;
      }

      // Update PlannerExecutorGraph state with finalized message
      const aiMessage = new AIMessage({
        content: executorAccumulatedText.trim(),
        id: currentMessageId,
      });

      const workflow = this.plannerExecutorGraph.getWorkflow();

      await workflow.updateState(
        {
          configurable: {
            thread_id: `website:${jobData.threadId}`,
          },
        },
        {
          messages: [aiMessage],
        },
      );

      this.logger.debug(
        `Successfully finalized partially accumulated text for message ${currentMessageId}`,
        {
          runId: jobData.runId,
          messageId: currentMessageId,
          agentId: jobData.mainAgentId,
          textLength: executorAccumulatedText.length,
          textPreview:
            executorAccumulatedText.substring(0, 100) +
            (executorAccumulatedText.length > 100 ? '...' : ''),
        },
      );

      // Clear the accumulated text for this agent after finalizing
      tokenUsageCollector.clearAccumulatedText(jobData.mainAgentId);
    } catch (error) {
      this.logger.error(
        `Failed to finalize accumulated text for message ${currentMessageId}:`,
        {
          runId: jobData.runId,
          messageId: currentMessageId,
          agentId: jobData.mainAgentId,
          error: (error as Error).message,
          stack: (error as Error).stack,
        },
      );
      // Don't throw - text finalization failure shouldn't fail the run
    }
  }

  /**
   * Clean up empty assistant messages in the thread
   * Removes ExternalConversationMessage records that are empty and not tool confirmations
   */
  async cleanupEmptyAssistantMessages(threadId: string): Promise<number> {
    try {
      // Find all empty assistant messages in this thread
      const emptyMessages = await this.messageRepo.find({
        where: {
          externalCustomerPlatformDataId: threadId,
          role: MessageRole.ASSISTANT,
          text: '', // Empty text
          hasAttachments: false, // No attachments
        },
        select: ['id'], // Only need IDs for deletion
      });

      if (emptyMessages.length === 0) {
        this.logger.debug(
          `No empty assistant messages found in thread ${threadId}`,
        );
        return 0;
      }

      // Delete all empty messages
      const messageIds = emptyMessages.map((msg) => msg.id);
      const deleteResult = await this.messageRepo.delete(messageIds);

      this.logger.debug(
        `Cleaned up ${emptyMessages.length} empty assistant messages in thread ${threadId}`,
        {
          threadId,
          deletedMessageIds: messageIds,
          deleteResult: deleteResult.affected,
        },
      );

      return emptyMessages.length;
    } catch (error) {
      this.logger.error(
        `Failed to cleanup empty assistant messages in thread ${threadId}:`,
        {
          threadId,
          error: (error as Error).message,
          stack: (error as Error).stack,
        },
      );
      return 0; // Don't throw - cleanup failure shouldn't fail the run
    }
  }

  /**
   * Clean up temporary Redis keys and data
   * Uses RunStatusService for status management and cleanup
   */
  async cleanupTemporaryData(jobData: WebsiteJobData): Promise<void> {
    const { runStatusKey, streamKey, platformThreadId } = jobData.keys;
    const threadId = jobData.threadId;

    try {
      // Check current run status before cleanup
      const currentStatus =
        await this.runStatusService.getRunStatus(platformThreadId);

      if (currentStatus) {
        this.logger.debug('Current run status before cleanup', {
          threadId,
          runId: jobData.runId,
          status: currentStatus.status,
          runStatusKey,
          visitorId: jobData.humanInfo.websiteVisitor.id,
        });

        // Force complete active runs if needed (safety net)
        if (currentStatus.status === RunStatusType.ACTIVE) {
          this.logger.warn('Force completing active run during cleanup', {
            threadId,
            runId: jobData.runId,
          });
          await this.runStatusService.completeRun(
            platformThreadId,
            jobData.runId,
            {
              forcedCompletion: true,
              cleanupPhase: true,
            },
          );
        }

        try {
          await this.runStatusService.clearRunStatus(platformThreadId);
          this.logger.debug('Run status cleanup completed', {
            threadId,
            runId: jobData.runId,
          });
        } catch (error) {
          this.logger.error('Failed to clear run status during cleanup', {
            threadId,
            error: (error as Error).message,
          });
        }
      }

      // Set TTL on stream key (don't delete - frontend may still be reading)
      const ttlSeconds = 10 * 60; // 10 minutes
      await this.redisService.expire(streamKey, ttlSeconds);

      // Clean up empty assistant messages in this thread
      const deletedMessageCount =
        await this.cleanupEmptyAssistantMessages(threadId);

      this.logger.debug(`Cleaned up temporary data for run: ${jobData.runId}`, {
        runId: jobData.runId,
        threadId,
        streamKey,
        runStatusKey,
        ttlSeconds,
        deletedEmptyMessages: deletedMessageCount,
        currentStatus: currentStatus?.status,
        visitorId: jobData.humanInfo.websiteVisitor.id,
      });
    } catch (error) {
      this.logger.error(
        `Failed to cleanup temporary data for run ${jobData.runId}:`,
        {
          runId: jobData.runId,
          threadId,
          streamKey,
          runStatusKey,
          error: (error as Error).message,
          stack: (error as Error).stack,
        },
      );
    }
  }
}
