import { ReplyToMessagesContext } from "src/modules/agents/interfaces";

/**
 * Reply-To Messages Prompt Builder
 * Generates XML representation of messages being replied to for context
 * Uses attribute-heavy approach for minimal token usage
 */
export function buildReplyToMessagesPrompt(
  replyToMessagesContext?: ReplyToMessagesContext,
): string {
  if (!replyToMessagesContext || replyToMessagesContext.length === 0 || !replyToMessagesContext[0].text) {
    return ''; // No reply context, return empty string
  }

  const parts: string[] = ['<reply-to-messages>'];

  replyToMessagesContext.forEach((replyMessage) => {
    parts.push(`  <message>`);
    
    // Message text content
    if (replyMessage.text) {
      parts.push(`    <text>${replyMessage.text}</text>`);
    }

    // Message attachments (if any)
    const hasMedia = !!replyMessage?.attachments?.images?.length;
    const hasKnowledge = !!replyMessage?.attachments?.knowledgeFiles?.length;

    if (hasMedia || hasKnowledge) {
      parts.push('    <attachments>');

      // Media attachments
      if (hasMedia) {
        replyMessage.attachments.images!.forEach((media) => {
          const attributes: string[] = [
            `id="${media.id}"`,
            `name="${media.name}"`,
          ];

          // Optional description
          if (media.description) {
            attributes.push(`description="${media.description}"`);
          }

          // Optional tags (flatten to comma-separated string)
          if (media.tags && Object.keys(media.tags).length > 0) {
            const tagString = Object.keys(media.tags).join(',');
            attributes.push(`tags="${tagString}"`);
          }

          parts.push(`      <media ${attributes.join(' ')} />`);
        });
      }

      // Knowledge file attachments
      if (hasKnowledge) {
        replyMessage.attachments.knowledgeFiles!.forEach((knowledge) => {
          const attributes: string[] = [
            `id="${knowledge.id}"`,
            `name="${knowledge.name}"`,
            `file-id="${knowledge.fileId}"`,
          ];

          parts.push(`      <knowledge ${attributes.join(' ')} />`);
        });
      }

      parts.push('    </attachments>');
    }

    parts.push('  </message>');
  });

  parts.push('</reply-to-messages>');

  return parts.join('\n');
}
