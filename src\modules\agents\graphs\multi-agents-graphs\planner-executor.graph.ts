import { Injectable, Logger } from '@nestjs/common';
import {
  CompiledStateGraph,
  END,
  START,
  StateGraph,
} from '@langchain/langgraph';
import { GraphConfigType } from '../../runtime';
import { AgentConfig } from '../../interfaces';
import {
  GraphState,
  ReactAgentGraph,
  ReactAgentStateType,
} from '../react-agent.graph';
import { BaseCallbackHandler } from '@langchain/core/callbacks/base';
import { checkpointer } from '../checkpointer';
import { RunnableConfig } from '@langchain/core/runnables';
import { IterableReadableStream } from '@langchain/core/utils/stream';
import { StreamEvent } from '@langchain/core/tracers/log_stream';

export interface PlannerExecutorGraphConfigType extends GraphConfigType {
  plannerAgent?: AgentConfig;
}

export type PlannerExecutorRunnableConfig =
  RunnableConfig<PlannerExecutorGraphConfigType>;
@Injectable()
export class PlannerExecutorGraph {
  private readonly logger = new Logger(PlannerExecutorGraph.name);
  private readonly workflow: CompiledStateGraph<any, any, any>;

  constructor(private readonly reactAgentGraph: ReactAgentGraph) {
    this.workflow = new StateGraph(GraphState)
      .addNode('agent', this.executeAgent.bind(this))
      .addEdge(START, 'agent')
      .addEdge('agent', END)
      .compile({
        checkpointer: checkpointer,
      });
    this.logger.debug(
      `PlannerExecutorGraph initialized with planner executor workflow`,
    );
  }

  getWorkflow() {
    return this.workflow;
  }

  executeStream(param: {
    input: any;
    config: PlannerExecutorGraphConfigType;
    tags: string[];
    callbacks: BaseCallbackHandler[];
    signal: AbortSignal;
  }): IterableReadableStream<StreamEvent> {
    const { input, config, tags, callbacks, signal } = param;
    return this.workflow.streamEvents(input, {
      configurable: config,
      version: 'v2' as const,
      streamMode: ['messages', 'updates'],
      tags,
      callbacks,
      signal,
    });
  }

  private async executeAgent(
    state: ReactAgentStateType,
    config: PlannerExecutorRunnableConfig,
  ): Promise<Partial<ReactAgentStateType>> {
    const plannerAgent = config.configurable?.plannerAgent;
    const executorAgent = config.configurable?.executorAgent;
    if (!executorAgent) {
      throw new Error('Executor agent is not defined in the config');
    }
    let plan: string =
      '<PLAN-SHOULD-FOLLOW>no plan provided</PLAN-SHOULD-FOLLOW>';
    if (plannerAgent) {
      const reactAgentGraphConfigurable: GraphConfigType = {
        executorAgent: plannerAgent,
        thread_id: `planner:${config.configurable?.thread_id}:${plannerAgent.id}`,
        alwaysApproveToolCall: true,
        platform: config.configurable?.platform,
        currentUser: config.configurable?.currentUser,
      };
      const result = await this.reactAgentGraph.executeInvoke(
        {
          messages: [...state.messages],
        },
        reactAgentGraphConfigurable,
        [`agent:${plannerAgent.id}:${plannerAgent.type}`, 'subprocess'],
      );
      plan =
        `<PLAN-SHOULD-FOLLOW>${result.messages?.[0].text}</PLAN-SHOULD-FOLLOW>` ||
        plan;
    }

    executorAgent.promptBuilders = [
      () => plan,
      ...(executorAgent.promptBuilders || []),
    ];

    const reactAgentGraphConfigurable: GraphConfigType = {
      executorAgent,
      thread_id: config.configurable?.thread_id,
      checkpoint_id: config.configurable?.checkpoint_id,
      alwaysApproveToolCall: config.configurable?.alwaysApproveToolCall,
      platform: config.configurable?.platform,
      toolChoices: config.configurable?.toolChoices,
      currentUser: config.configurable?.currentUser,
    };
    const result = await this.reactAgentGraph.executeInvoke(
      state,
      reactAgentGraphConfigurable,
    );
    return result;
  }
}
