import { CallbackManagerForToolRun } from '@langchain/core/callbacks/manager';
import { StructuredTool, ToolRunnableConfig } from '@langchain/core/tools';
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EmployeeMemories, UserMemories } from '../../entities';
import { In, Repository } from 'typeorm';
import { z } from 'zod';
import { InAppSupervisorWorkersConfig } from '../../platforms/in-app/graph-configs/in-app-supervisor-workers.config';

@Injectable()
export class UserMemoryTool extends StructuredTool {
  private readonly logger = new Logger(UserMemoryTool.name);
  name: string;
  description: string;
  schema: any;
  constructor(
    @InjectRepository(UserMemories)
    private readonly userMemoriesRepository: Repository<UserMemories>,
    @InjectRepository(EmployeeMemories)
    private readonly employeeMemoriesRepository: Repository<EmployeeMemories>,
  ) {
    super();
    this.name = 'manage_user_memory';
    this.description = `
- A tool to create, update, or delete long-term memories about the user. 
- Each memory object in the 'memories' array must have an 'action' field.`;

    const createSchema = z.object({
      action: z.literal('create'),
      content: z
        .string()
        .describe('The main content of the memory about the user.'),
      metadata: z
        .record(z.any())
        .optional()
        .describe('Optional metadata for the memory.'),
    });

    const updateSchema = z.object({
      action: z.literal('update'),
      id: z.string().uuid('The ID of the memory to update.'),

      content: z
        .string()
        .optional()
        .describe('The main content of the memory about the user.'),
      metadata: z.record(z.any()).optional(),
    });

    const deleteSchema = z.object({
      action: z.literal('delete'),
      id: z.string().uuid('The ID of the memory to delete.'),
    });

    const memoryActionSchema = z
      .discriminatedUnion('action', [createSchema, updateSchema, deleteSchema])
      .refine(
        (data) => {
          if (data.action === 'update') {
            return !!data.content || !!data.metadata;
          }
          return true;
        },
        {
          message:
            "An 'update' action must provide either 'content' or 'metadata' to modify.",

          path: ['content'],
        },
      );

    this.schema = z.object({
      memories: z
        .array(memoryActionSchema)
        .describe('An array of memory operations to perform about the user.'),
    });
  }

  protected async _call(
    arg: z.infer<typeof this.schema>,
    runManager?: CallbackManagerForToolRun,
    parentConfig?: ToolRunnableConfig<InAppSupervisorWorkersConfig>,
  ): Promise<string> {
    const { memories } = arg as z.infer<typeof this.schema>;
    const userId = parentConfig?.configurable?.currentUser?.user?.userId;
    const employeeId = parentConfig?.configurable?.currentUser?.employee?.employeeId;
    if (!userId && !employeeId) {
      return 'Error: Missing user ID in execution context. Do not retry upon this message';
    }
    if (userId && employeeId) {
      return 'Error: Both user ID and employee ID are provided. Please specify only one.';
    }

    const toCreate = memories.filter((m) => m.action === 'create');
    const toUpdate = memories.filter((m) => m.action === 'update');
    const toDelete = memories.filter((m) => m.action === 'delete');

    if (
      toCreate.length === 0 &&
      toUpdate.length === 0 &&
      toDelete.length === 0
    ) {
      return 'No valid memory operations provided. Please specify at least one create, update, or delete action.';
    }

    if (userId) {
      return this.processUserMemories({ userId, toCreate, toUpdate, toDelete });
    } else if (employeeId) {
      return this.processEmployeeMemories({
        employeeId,
        toCreate,
        toUpdate,
        toDelete,
      });
    }

    return 'Error: Unable to determine user or employee context for memory operations.';
  }

  private async processUserMemories({
    userId,
    toCreate,
    toUpdate,
    toDelete,
  }: {
    userId: number;
    toCreate: any[];
    toUpdate: any[];
    toDelete: any[];
  }): Promise<string> {
    const queryRunner =
      this.userMemoriesRepository.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      if (toCreate.length > 0) {
        const newMemories = toCreate.map((mem) =>
          this.userMemoriesRepository.create({
            content: mem.content,
            metadata: mem.metadata,
            userId: userId,
          }),
        );
        await queryRunner.manager.save(UserMemories, newMemories);
      }

      if (toUpdate.length > 0) {
        const idsToUpdate = toUpdate.map((m) => m.id);
        const count = await queryRunner.manager.count(UserMemories, {
          where: { id: In(idsToUpdate), userId: userId },
        });
        if (count !== idsToUpdate.length) {
          throw new Error(
            'Permission denied or one or more memories not found for update.',
          );
        }

        await queryRunner.manager.save(UserMemories, toUpdate);
      }

      if (toDelete.length > 0) {
        const idsToDelete = toDelete.map((m) => m.id);
        await queryRunner.manager.delete(UserMemories, {
          id: In(idsToDelete),
          userId: userId,
        });
      }

      await queryRunner.commitTransaction();
      return `Successfully processed user memory operations: ${toCreate.length} created, ${toUpdate.length} updated, ${toDelete.length} deleted.`;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(
        `Error in user memory transaction for user ${userId}`,
        error,
      );
      return `Error: The user memory operation failed and all changes were reverted. Reason: ${error.message}`;
    } finally {
      await queryRunner.release();
    }
  }

  private async processEmployeeMemories({
    employeeId,
    toCreate,
    toUpdate,
    toDelete,
  }: {
    employeeId: number;
    toCreate: any[];
    toUpdate: any[];
    toDelete: any[];
  }): Promise<string> {
    const queryRunner =
      this.employeeMemoriesRepository.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      if (toCreate.length > 0) {
        const newMemories = toCreate.map((mem) =>
          this.employeeMemoriesRepository.create({
            content: mem.content,
            metadata: mem.metadata,
            employeeId: employeeId,
          }),
        );
        await queryRunner.manager.save(EmployeeMemories, newMemories);
      }

      if (toUpdate.length > 0) {
        const idsToUpdate = toUpdate.map((m) => m.id);
        const count = await queryRunner.manager.count(EmployeeMemories, {
          where: { id: In(idsToUpdate), employeeId: employeeId },
        });
        if (count !== idsToUpdate.length) {
          throw new Error(
            'Permission denied or one or more memories not found for update.',
          );
        }

        await queryRunner.manager.save(EmployeeMemories, toUpdate);
      }

      if (toDelete.length > 0) {
        const idsToDelete = toDelete.map((m) => m.id);
        await queryRunner.manager.delete(EmployeeMemories, {
          id: In(idsToDelete),
          employeeId: employeeId,
        });
      }

      await queryRunner.commitTransaction();
      return `Successfully processed employee memory operations: ${toCreate.length} created, ${toUpdate.length} updated, ${toDelete.length} deleted.`;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(
        `Error in employee memory transaction for employee ${employeeId}`,
        error,
      );
      return `Error: The employee memory operation failed and all changes were reverted. Reason: ${error.message}`;
    } finally {
      await queryRunner.release();
    }
  }
}
