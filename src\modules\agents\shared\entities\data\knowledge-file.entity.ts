import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { DataSourceEnum } from '../../enums';

export enum OwnerTypeEnum {
  USER = 'USER',
  ADMIN = 'ADMIN',
}

export enum KnowledgeFileStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  DRAFT = 'DRAFT',
  DELETED = 'DELETED',
}

/**
 * Entity đại diện cho bảng knowledge_files trong cơ sở dữ liệu
 * Bảng lưu trữ thông tin về các file tri thức trong hệ thống, bao gồm thông tin về quyền sở hữu và trạng thái bán hàng
 */
@Entity('knowledge_files')
export class KnowledgeFile {
  /**
   * Mã định danh duy nhất cho mỗi file tri thức, tự động tăng
   */
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;

  /**
   * Tên hiển thị của file tri thức, dùng để hiển thị cho người dùng
   */
  @Column({ name: 'name', length: 255 })
  name: string;

  /**
   * Khóa định danh file trên hệ thống lưu trữ, dùng để truy xuất nội dung file từ storage system
   */
  @Column({ name: 'storage_key', length: 512, unique: true })
  storageKey: string;

  /**
   * Loại người sở hữu file: USER (người dùng thông thường) hoặc ADMIN (quản trị viên hệ thống)
   */
  @Column({
    name: 'owner_type',
    type: 'enum',
    enum: OwnerTypeEnum,
    default: OwnerTypeEnum.USER,
  })
  ownerType: OwnerTypeEnum;

  /**
   * ID của người sở hữu file, tham chiếu đến bảng users hoặc employees tùy theo owner_type
   */
  @Column({ name: 'owned_by' })
  ownedBy: number;

  /**
   * Trạng thái sở hữu file: TRUE - người dùng là chủ sở hữu, FALSE - người dùng chỉ được cấp quyền truy cập
   */
  @Column({ name: 'is_owner', default: true })
  isOwner: boolean;

  /**
   * Trạng thái đăng bán file trên chợ tri thức: TRUE - đang được đăng bán, FALSE - không đăng bán
   */
  @Column({ name: 'is_for_sale', default: false })
  isForSale: boolean;

  /**
   * Thời điểm tạo bản ghi file, dạng UNIX timestamp với millisecond
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  createdAt: number;

  /**
   * Dung lượng của file tri thức tính bằng byte, dùng để tính toán không gian lưu trữ và giới hạn
   */
  @Column({ name: 'storage', type: 'bigint', default: 0 })
  storage: number;

  /**
   * Id file tren openai
   */
  @Column({ name: 'file_id', length: 100 })
  fileId: string;

  /**
   * Trạng thái của file: PENDING - đang chờ duyệt, APPROVED - đã được duyệt, REJECTED - bị từ chối, DISABLED - đã bị vô hiệu hóa
   */
  @Column({
    name: 'status',
    type: 'enum',
    enum: KnowledgeFileStatus,
    default: KnowledgeFileStatus.PENDING,
  })
  status: KnowledgeFileStatus;

  /**
   * Nguồn dữ liệu: INTERNAL - từ trong hệ thống, EXTERNAL - từ bên ngoài
   */
  @Column({
    name: 'data_source',
    enum: DataSourceEnum,
    type: 'enum',
    default: DataSourceEnum.INTERNAL,
    comment:
      'Nguồn dữ liệu: INTERNAL - từ trong hệ thống, EXTERNAL - từ bên ngoài',
  })
  dataSource: DataSourceEnum;
}
