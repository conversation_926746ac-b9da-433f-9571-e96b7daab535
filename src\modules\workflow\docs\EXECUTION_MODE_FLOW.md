# Workflow Execution Mode Flow

## Tổng quan

Hệ thống workflow hỗ trợ 2 execution modes:
- **REALTIME**: Push events về Redis để user thấy realtime updates
- **BACKGROUND**: <PERSON><PERSON><PERSON> ngầm không push events, tiết kiệm resources

## Luồng thực thi

### 1. Message Pattern Handler (Controller)
```typescript
@MessagePattern(WorkflowJobType.USER_EXECUTE)
async handleExecute(@Payload() data: IExecutionPayload)
```

**Logic phân biệt:**
- `data.currentNode` có giá trị → Execute single node
- `data.currentNode` null/undefined → Execute full workflow
- **Luôn luôn REALTIME mode** → Push Redis events

### 2. Queue Processor (Background)
```typescript
private async handleExecuteNode(job: Job<WorkflowNodeExecutionJobData>)
```

**Logic:**
- Chỉ x<PERSON> lý single node execution
- **<PERSON>ôn luôn BACKGROUND mode** → Không push Redis events
- Sử dụng `executionConfig.mode` để xác định có push event hay không

## Cấu trúc Payload (Enhanced)

```typescript
interface IExecutionPayload {
    workflowId: string;
    executionId: string;
    userId?: number;
    currentNode?: string | null;  // For single node execution
    startNode?: string | null;    // NEW: For workflow execution from specific node
    initContext?: Record<string, any>;
}
```

## Execution Logic (Enhanced)

### Execution Type Determination
```typescript
// startNode = null && currentNode != null → Execute single node
if (!startNode && currentNode) {
    return 'Single Node';
}

// startNode != null && currentNode = null → Execute workflow from startNode
if (startNode && !currentNode) {
    return 'Workflow From Start';
}

// startNode = null && currentNode = null → Execute full workflow
if (!startNode && !currentNode) {
    return 'Full Workflow';
}

// Invalid: both provided
throw new Error('Cannot specify both startNode and currentNode');
```

## Service Methods

### executeNodeWithMode()
```typescript
async executeNodeWithMode(
    data: IExecutionPayload, 
    executionMode: 'REALTIME' | 'BACKGROUND'
): Promise<any>
```

### executeWorkflowWithMode()
```typescript
async executeWorkflowWithMode(
    data: IExecutionPayload, 
    executionMode: 'REALTIME' | 'BACKGROUND'
): Promise<any>
```

## Event Publishing Logic

### REALTIME Mode
- ✅ Publish workflow/node started events
- ✅ Publish progress updates
- ✅ Publish completion events
- ✅ Publish error events

### BACKGROUND Mode
- ❌ Skip all Redis events
- ✅ Log execution progress
- ✅ Save to database
- ✅ Handle errors internally

## Ví dụ sử dụng (Enhanced)

### 1. Execute Single Node (Realtime)
```typescript
const payload = {
    workflowId: "wf-123",
    executionId: "exec-456",
    userId: 123,
    currentNode: "node-789",  // Execute this node only
    startNode: null,          // Not used
    initContext: { data: "test" }
};
// → Gọi executeNodeWithMode(payload, 'REALTIME')
// → Push Redis events
```

### 2. Execute Workflow From Start Node (Realtime)
```typescript
const payload = {
    workflowId: "wf-123",
    executionId: "exec-456",
    userId: 123,
    currentNode: null,        // Not used
    startNode: "start-node-123", // Start from this node
    initContext: { data: "test" }
};
// → Gọi executeWorkflowWithMode(payload, 'REALTIME')
// → Uses loadWorkflowDefinitionHybrid() for optimization
// → Push Redis events with isPartialExecution: true
```

### 3. Execute Full Workflow (Realtime)
```typescript
const payload = {
    workflowId: "wf-123",
    executionId: "exec-456",
    userId: 123,
    currentNode: null,        // Not used
    startNode: null,          // Not used
    initContext: { data: "test" }
};
// → Gọi executeWorkflowWithMode(payload, 'REALTIME')
// → Uses loadWorkflowDefinition() for complete workflow
// → Push Redis events with isPartialExecution: false
```

### 4. Background Execution (Queue)
```typescript
// Job được add vào queue với executionConfig.mode = 'background'
// → Gọi executeNodeWithMode(payload, 'BACKGROUND')
// → Không push Redis events
// → startNode logic vẫn áp dụng cho background execution
```

## Flow Diagram

```
Request → Controller/Processor
    ↓
Check currentNode
    ↓
currentNode exists? 
    ├─ Yes → executeNodeWithMode()
    └─ No  → executeWorkflowWithMode()
    ↓
Check executionMode
    ├─ REALTIME → Push Redis Events
    └─ BACKGROUND → Skip Events
    ↓
Execute Logic → Return Result
```

## Lợi ích

1. **Performance**: Background jobs không tốn resources cho Redis events
2. **User Experience**: Realtime execution cho feedback tức thì
3. **Flexibility**: Cùng 1 logic xử lý, khác nhau ở event publishing
4. **Maintainability**: Code rõ ràng, dễ debug và mở rộng
