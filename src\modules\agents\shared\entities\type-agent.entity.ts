import {
  BeforeUpdate,
  Column,
  Entity,
  Index,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { TypeAgentEnum } from '../enums';

/**
 * Entity đại diện cho bảng type_agents trong cơ sở dữ liệu
 * Bảng quản lý các loại agent, định nghĩa các nhóm chức năng mà agent có thể thực hiện
 */
@Entity('type_agents')
@Index('idx_type_agents_name', ['name'])
@Index('idx_type_agents_created_by', ['createdBy'])
@Index('idx_type_agents_updated_by', ['updatedBy'])
export class TypeAgent {
  /**
   * Mã định danh duy nhất cho mỗi loại agent, tự động tăng
   */
  @PrimaryGeneratedColumn() id: number;

  /**
   * Tên của loại agent, ví dụ: Hỗ trợ khách hàng, <PERSON><PERSON><PERSON> lý bán hàng
   */
  @Column({ length: 255 }) name: string;

  /**
   * <PERSON>ô tả chi tiết về phạm vi và mục đích của loại agent
   */
  @Column({ type: 'text', nullable: true }) description: string | null;

  /**
   * Mã nhân viên tạo loại agent
   */
  @Column({ name: 'created_by', type: 'integer', nullable: true }) createdBy:
    | number
    | null;

  /**
   * Mã nhân viên cập nhật loại agent gần nhất
   */
  @Column({ name: 'updated_by', type: 'integer', nullable: true }) updatedBy:
    | number
    | null;

  /**
   * Thời điểm tạo, tính bằng mili giây
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật gần nhất, tính bằng mili giây
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  updatedAt: number;
  /**
   * Thời điểm xóa mềm (timestamp millis)
   */
  @Column({
    name: 'deleted_at',
    type: 'bigint',
    nullable: true,
  })
  deletedAt: number | null;
  /**
   * Mã nhân viên xóa loại agent
   */
  @Column({ name: 'deleted_by', type: 'integer', nullable: true }) deletedBy:
    | number
    | null;
  /**
   * Loại của type agent
   */
  @Column({
    type: 'enum',
    enum: TypeAgentEnum,
    default: TypeAgentEnum.ASSISTANT,
  })
  type: TypeAgentEnum;
  /**
   * Trạng thái hoạt động của loại agent
   */
  @Column({ name: 'active', type: 'boolean', default: false }) active: boolean;
  /**
   * Có áp dụng cho tất cả model không
   */
  @Column({ name: 'is_all_model', type: 'boolean', default: true })
  isAllModel: boolean;
  /**
   * Cho phép tùy chỉnh profile
   */
  @Column({
    name: 'enable_profile_customization',
    type: 'boolean',
    default: false,
  })
  enableProfileCustomization: boolean;
  /**
   * Cho phép sử dụng tool
   */
  @Column({ name: 'enable_tool', type: 'boolean', default: false })
  enableTool: boolean;
  /**
   * Cho phép output messenger
   */
  @Column({ name: 'enable_output_messenger', type: 'boolean', default: false })
  enableOutputMessenger: boolean;
  /**
   * Cho phép output livechat
   */
  @Column({ name: 'enable_output_livechat', type: 'boolean', default: false })
  enableOutputLivechat: boolean;
  /**
   * Cho phép output Zalo OA
   */
  @Column({ name: 'enable_output_zalo_oa', type: 'boolean', default: false })
  enableOutputZaloOa: boolean;
  /**
   * Cho phép output payment
   */
  @Column({ name: 'enable_output_payment', type: 'boolean', default: false })
  enableOutputPayment: boolean;
  /**
   * Cho phép chuyển đổi
   */
  @Column({ name: 'enable_convert', type: 'boolean', default: false })
  enableConvert: boolean;
  /**
   * Cho phép sử dụng resources
   */
  @Column({ name: 'enable_resources', type: 'boolean', default: false })
  enableResources: boolean;
  /**
   * Cho phép shipment
   */
  @Column({ name: 'enable_shipment', type: 'boolean', default: false })
  enableShipment: boolean;
  /**
   * Cho phép multi agent
   */
  @Column({ name: 'enable_multi_agent', type: 'boolean', default: false })
  enableMultiAgent: boolean;
  /**
   * Cho phép strategy
   */
  @Column({ name: 'enable_strategy', type: 'boolean', default: false })
  enableStrategy: boolean;

  @BeforeUpdate() updateTimestamp() {
    this.updatedAt = Date.now();
  }
}
