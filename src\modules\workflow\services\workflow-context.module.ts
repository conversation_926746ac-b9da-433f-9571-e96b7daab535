import { Modu<PERSON> } from '@nestjs/common';
import { WorkflowContextService } from './workflow-context.service';
import { WorkflowContextBuilderService } from './workflow-context-builder.service';
import { WorkflowContextManagerService } from './workflow-context-manager.service';
import { WorkflowTemplateProcessorService } from './workflow-template-processor.service';

/**
 * ✅  WORKFLOW CONTEXT MODULE
 * Module chứa tất cả services cho  Workflow Context system
 */
@Module({
  providers: [
    WorkflowContextService,
    WorkflowContextBuilderService,
    WorkflowContextManagerService,
    WorkflowTemplateProcessorService,
  ],
  exports: [
    WorkflowContextService,
    WorkflowContextBuilderService,
    WorkflowContextManagerService,
    WorkflowTemplateProcessorService,
  ],
})
export class WorkflowContextModule {}
