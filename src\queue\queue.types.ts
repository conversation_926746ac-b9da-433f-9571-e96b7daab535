/**
 * Interface cho job đồng bộ người dùng Zalo vào audience
 */
export interface ZaloAudienceSyncJobData {
  /**
   * ID của người dùng thực hiện đồng bộ
   */
  userId: number;

  /**
   * ID của Zalo Official Account
   */
  oaId: string;

  /**
   * Dữ liệu cấu hình đồng bộ
   */
  syncDto: any; // Tạm thời dùng any, sẽ thay bằng SyncZaloUsersToAudienceDto sau

  /**
   * ID để tracking job
   */
  syncId: string;

  /**
   * Thời gian tạo job
   */
  timestamp: number;
}

/**
 * Interface cho job data của Zalo Video Tracking
 */
export interface ZaloVideoTrackingJobData {
  /**
   * Token của video upload từ Zalo API
   */
  token: string;

  /**
   * Access token của Zalo Official Account
   */
  accessToken: string;

  /**
   * ID của user sở hữu video
   */
  userId: number;

  /**
   * ID của integration (Zalo OA)
   */
  integrationId: string;

  /**
   * ID của Official Account (để tương thích với hệ thống cũ)
   */
  oaId?: string;

  /**
   * Thời gian tạo job
   */
  timestamp: number;

  /**
   * Số lần đã check (để tránh check vô hạn)
   */
  checkCount?: number;

  /**
   * Thời gian delay giữa các lần check (milliseconds)
   */
  delayMs?: number;
}

/**
 * Dữ liệu job thực thi workflow
 */
export interface WorkflowExecutionJobData {
  /**
   * ID của workflow execution
   */
  executionId: string;

  /**
   * ID của user thực thi
   */
  userId: number;

  /**
   * ID của workflow
   */
  workflowId: string;

  /**
   * Dữ liệu trigger
   */
  triggerData: any;

  /**
   * Loại trigger (manual, webhook, schedule)
   */
  triggerType: 'manual' | 'webhook' | 'schedule';

  /**
   * Metadata bổ sung
   */
  metadata?: {
    source?: string;
    webhookId?: string;
    scheduleId?: string;
    priority?: number;
  };

  /**
   * Tùy chọn thực thi
   */
  options?: {
    enableSSE?: boolean;
    timeout?: number;
    retryOnFailure?: boolean;
  }
}
/**
* ID của user thực thi
* Interface cho job data đồng bộ tin nhắn Zalo
*/
export interface ZaloMessageSyncJobData {
  /**
   * ID của người dùng
   */
  userId: number;

  /**
   * ID của Integration Zalo OA
  */
  integrationId: string;

  /**
   * Số lượng tin nhắn tối đa cần đồng bộ
   */
  limit: number;

  /**
   * Offset để phân trang
   */
  offset: number;

  /**
   * Chỉ đồng bộ tin nhắn từ những user-audience có zaloSocialId
   */
  onlyExistingAudience: boolean;

  /**
   * Thời gian tạo job
   */
  timestamp: number;

  /**
   * ID để tracking job (optional)
   */
  trackingId?: string;
}

/**
 * Interface cho job data của Zalo AI processing
 * Xử lý webhook events từ Zalo Official Account và tạo phản hồi AI
 */
export interface ZaloJobData {
  /**
   * ID của run (UUID)
   */
  runId: string;

  /**
   * ID của thread - tương ứng với ExternalCustomerPlatformData.id
   */
  threadId: string;

  /**
   * ID của agent chính xử lý conversation
   */
  mainAgentId: string;

  /**
   * ID của planner agent (optional, cho strategy pattern)
   */
  plannerAgentId?: string;

  /**
   * Platform - luôn là ZALO
   */
  platform: 'ZALO';

  /**
   * Redis keys cho run status và streaming
   */
  keys: {
    /**
     * Platform thread ID: "zalo:thread-123"
     */
    platformThreadId: string;
    /**
     * Run status key: "run_status:zalo:thread-123"
     */
    runStatusKey: string;
    /**
     * Stream key: "zalo:agent_stream:thread-123:run-456"
     */
    streamKey: string;
  };

  /**
   * Thông tin con người trong conversation
   */
  humanInfo: {
    /**
     * Thông tin chủ sở hữu Zalo OA (business owner)
     */
    zaloOwner: UserInfo;
    /**
     * Thông tin người dùng Zalo (visitor/customer)
     */
    zaloUser: UserConvertCustomerInfo;
    /**
     * Thông tin context Zalo-specific
     */
    zaloInfo: ZaloContextInfo;
  };
}

/**
 * User Info Interface - matches backend structure
 */
export interface UserInfo {
  userId: number;
  fullName: string;
  email: string;
  gender: 'MALE' | 'FEMALE' | 'OTHER';
  dateOfBirth: Date;
  type: 'INDIVIDUAL' | 'BUSINESS';
  countryCode: number;
  pointsBalance: number;
  isVerifyPhone: boolean;
  address: string;
  timezone?: string;
  currency?: string;
}

/**
 * User Convert Customer Info Interface - matches backend structure
 */
export interface UserConvertCustomerInfo {
  id: string;
  externalPlatformId: string;
  name?: string;
  email?: UserConvertCustomerEmail;
  phone?: string;
  countryCode?: number;
  metadata?: Record<string, any>;
  address?: string;
  tags?: string[];
}

/**
 * User Convert Customer Email Interface
 */
export interface UserConvertCustomerEmail {
  email?: string;
  isVerified?: boolean;
}

/**
 * Interface cho Zalo-specific context information
 */
export interface ZaloContextInfo {
  /**
   * Zalo Official Account ID
   */
  oaId: string;

  /**
   * Tên của Official Account
   */
  oaName: string;

  /**
   * Encrypted config chứa access token và refresh token
   */
  encryptedConfig: string;

  /**
   * Secret key để decrypt config
   */
  secretKey: string;

  /**
   * Chi tiết thông tin người dùng Zalo từ API
   */
  zaloUserDetail: any; // ZaloUserInfo - avoid circular import
}

/**
 * Interface cho job data của Zalo Article Scheduler
 */
export interface ZaloArticleSchedulerJobData {
  /**
   * ID của bài viết trong database
   */
  articleId: string;

  /**
   * ID của user sở hữu bài viết
   */
  userId: number;

  /**
   * ID của Zalo Integration
   */
  integrationId: string;

  /**
   * Token của bài viết từ Zalo API (đã tạo với status hide)
   */
  articleToken: string;

  /**
   * Thời gian lên lịch xuất bản (Unix timestamp)
   */
  scheduledTime: number;

  /**
   * Thời gian tạo job
   */
  timestamp: number;

  /**
   * Metadata bổ sung (optional)
   */
  metadata?: {
    title?: string;
    description?: string;
    [key: string]: any;
  };
}

/**
 * Dữ liệu job thực thi node đơn lẻ
 */
export interface WorkflowNodeExecutionJobData {
  /**
   * ID của workflow execution
   */
  executionId: string;

  /**
   * ID của user thực thi
   */
  userId: number;

  /**
   * ID của node cần thực thi
   */
  nodeId: string;

  /**
   * Loại node
   */
  nodeType: string;

  /**
   * Cấu hình node
   */
  nodeConfig: Record<string, any>;

  /**
   * Dữ liệu đầu vào cho node
   */
  inputData: Record<string, any>;

  /**
   * Context từ các node trước đó
   */
  executionContext: Record<string, any>;

  /**
   * Tùy chọn thực thi
   */
  options?: {
    enableSSE?: boolean;
    timeout?: number;
    skipValidation?: boolean;
  };
}

/**
 * Interface cho job data của Zalo Article Tracking
 */
export interface ZaloArticleTrackingJobData {
  /**
   * Token của bài viết từ Zalo API response
   */
  token: string;

  /**
   * Access token của Zalo Official Account
   */
  accessToken: string;

  /**
   * ID của user sở hữu bài viết
   */
  userId: number;

  /**
   * ID của integration (Zalo OA)
   */
  integrationId: string;

  /**
   * ID của bài viết trong database local (optional)
   */
  localArticleId?: string;

  /**
   * Thời gian tạo job
   */
  timestamp: number;

  /**
   * Số lần đã check (để tránh check vô hạn)
   */
  checkCount?: number;

  /**
   * Thời gian delay giữa các lần check (milliseconds)
   */
  delayMs?: number;

  /**
   * Metadata bổ sung (optional)
   */
  metadata?: {
    title?: string;
    type?: 'normal' | 'video';
    [key: string]: any;
  };
}
