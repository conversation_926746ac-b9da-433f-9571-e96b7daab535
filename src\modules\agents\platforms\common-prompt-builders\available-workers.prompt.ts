import { AgentConfig } from '../../interfaces/agent-config.interface';

/**
 * Available Workers Prompt Builder
 * Generates XML representation of available worker agents for delegation
 * Provides supervisor with context about worker capabilities
 */
export function buildAvailableWorkersPrompt(
  workerAgents?: Record<string, AgentConfig>,
): string {
  if (!workerAgents || Object.keys(workerAgents).length === 0) {
    return ''; // No workers available
  }

  const parts: string[] = ['<available-workers>'];

  Object.entries(workerAgents).forEach(([workerId, workerConfig]) => {
    const attributes: string[] = [
      `id="${workerId}"`,
      `name="${workerConfig.name}"`,
      `type="${workerConfig.type}"`,
    ];

    parts.push(`  <worker ${attributes.join(' ')}>`);

    // Worker instruction/purpose
    if (workerConfig.instruction) {
      parts.push(`    <purpose>${workerConfig.instruction}</purpose>`);
    }

    // Worker description (from specialized config)
    if (workerConfig.specializedConfig?.description) {
      parts.push(
        `    <description>${workerConfig.specializedConfig.description}</description>`,
      );
    }

    // Worker profile/specialization
    const profile = workerConfig.specializedConfig?.profile;
    if (profile) {
      const profileParts: string[] = [];
      if (profile.position) profileParts.push(profile.position);
      if (profile.skills?.length)
        profileParts.push(`Skills: ${profile.skills.join(', ')}`);

      if (profileParts.length > 0) {
        parts.push(
          `    <specialization>${profileParts.join(' | ')}</specialization>`,
        );
      }
    }

    // Model capabilities
    const model = workerConfig.model;
    if (model) {
      parts.push('    <capabilities>');

      if (model.features?.length) {
        parts.push(`      <features>${model.features.join(', ')}</features>`);
      }

      if (model.inputModalities?.length) {
        parts.push(
          `      <input-modalities>${model.inputModalities.join(', ')}</input-modalities>`,
        );
      }

      if (model.outputModalities?.length) {
        parts.push(
          `      <output-modalities>${model.outputModalities.join(', ')}</output-modalities>`,
        );
      }

      parts.push('    </capabilities>');
    }

    // Available tools (MCP tools)
    if (workerConfig.mcpDescriptions?.length) {
      parts.push('    <mcp>');
      workerConfig.mcpDescriptions.forEach((mcp) => {
        parts.push(
          `      <mcp name="${mcp.serverName}">${mcp.description}</mcp>`,
        );
      });
      parts.push('    </mcp>');
    }

    parts.push('  </worker>');
  });

  parts.push('</available-workers>');

  return parts.join('\n');
}
