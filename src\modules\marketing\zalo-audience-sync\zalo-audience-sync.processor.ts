import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger, Injectable } from '@nestjs/common';
import { Job } from 'bullmq';
import { QueueName, ZaloAudienceSyncJobName } from '../../../queue/queue-name.enum';
import { ZaloAudienceSyncJobData, ZaloMessageSyncJobData } from '../../../queue/queue.types';
import { ZaloAudienceSyncService } from './zalo-audience-sync.service';
import { ZaloMessageSyncService } from './zalo-message-sync.service';

/**
 * Processor xử lý queue đồng bộ Zalo audience
 */
@Injectable()
@Processor(QueueName.ZALO_AUDIENCE_SYNC)
export class ZaloAudienceSyncProcessor extends WorkerHost {
  private readonly logger = new Logger(ZaloAudienceSyncProcessor.name);

  constructor(
    private readonly zaloAudienceSyncService: ZaloAudienceSyncService,
    private readonly zaloMessageSyncService: ZaloMessageSyncService,
  ) {
    super();
  }

  /**
   * <PERSON><PERSON> lý job đồng bộ người dùng Zalo vào audience hoặc đồng bộ tin nhắn
   * @param job Job chứa dữ liệu đồng bộ
   */
  async process(job: Job<ZaloAudienceSyncJobData | ZaloMessageSyncJobData, any, string>): Promise<void> {
    this.logger.log(
      `Bắt đầu xử lý job đồng bộ Zalo: ${job.id} - Job name: ${job.name}`,
    );

    try {
      // Xử lý theo loại job
      switch (job.name) {
        case ZaloAudienceSyncJobName.SYNC_ZALO_USERS_TO_AUDIENCE:
          await this.processSyncUsersToAudience(job as Job<ZaloAudienceSyncJobData>);
          break;

        case ZaloAudienceSyncJobName.SYNC_ZALO_MESSAGES:
          await this.processSyncZaloMessages(job as Job<ZaloMessageSyncJobData>);
          break;

        default:
          throw new Error(`Unknown job name: ${job.name}`);
      }

      this.logger.log(`Hoàn thành job đồng bộ Zalo: ${job.id}`);
    } catch (error) {
      this.logger.error(
        `Lỗi khi xử lý job đồng bộ Zalo: ${job.id}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Xử lý job đồng bộ người dùng Zalo vào audience
   */
  private async processSyncUsersToAudience(job: Job<ZaloAudienceSyncJobData>): Promise<void> {
    const { userId, oaId, syncDto, syncId } = job.data;

    try {
      // Gửi event bắt đầu đồng bộ
      await this.zaloAudienceSyncService.emitSyncProgress(syncId, {
        status: 'started',
        message: 'Bắt đầu đồng bộ người dùng Zalo vào audience',
        progress: 0,
        timestamp: Date.now(),
      });

      // Thực hiện đồng bộ
      const result = await this.zaloAudienceSyncService.syncZaloUsersToAudience(
        userId,
        oaId,
        syncDto,
        syncId,
      );

      // Gửi event hoàn thành
      await this.zaloAudienceSyncService.emitSyncProgress(syncId, {
        status: 'completed',
        message: 'Đồng bộ hoàn thành thành công',
        progress: 100,
        result,
        timestamp: Date.now(),
      });

      this.logger.log(
        `Hoàn thành job đồng bộ Zalo audience: ${job.id} - syncId: ${syncId}. ` +
        `Processed: ${result.processedCount}, Created: ${result.newAudienceCreated}, ` +
        `Updated: ${result.existingAudienceUpdated}, Errors: ${result.errorCount}`,
      );

    } catch (error) {
      this.logger.error(
        `Lỗi khi xử lý job đồng bộ Zalo audience: ${job.id} - syncId: ${job.data.syncId}: ${error.message}`,
        error.stack,
      );

      // Gửi event lỗi
      await this.zaloAudienceSyncService.emitSyncProgress(job.data.syncId, {
        status: 'failed',
        message: `Đồng bộ thất bại: ${error.message}`,
        progress: 0,
        error: error.message,
        timestamp: Date.now(),
      });

      throw error;
    }
  }

  /**
   * Xử lý job đồng bộ tin nhắn Zalo vào database
   */
  private async processSyncZaloMessages(job: Job<ZaloMessageSyncJobData>): Promise<void> {
    const { userId, integrationId, limit, offset, onlyExistingAudience, trackingId } = job.data;

    this.logger.log(
      `Bắt đầu đồng bộ tin nhắn Zalo: userId=${userId}, integrationId=${integrationId}, limit=${limit}, offset=${offset}`,
    );

    try {
      // Thực hiện đồng bộ tin nhắn
      const result = await this.zaloMessageSyncService.syncZaloMessages({
        userId,
        integrationId,
        limit,
        offset,
        onlyExistingAudience,
        trackingId,
      });

      this.logger.log(
        `Hoàn thành đồng bộ tin nhắn Zalo: ${job.id} - trackingId: ${trackingId}. ` +
        `Processed: ${result.processedAudiences}, Messages synced: ${result.totalMessagesSynced}, ` +
        `New messages: ${result.newMessagesCreated}, Updated: ${result.existingMessagesUpdated}, ` +
        `Errors: ${result.errorCount}`,
      );

    } catch (error) {
      this.logger.error(
        `Lỗi khi đồng bộ tin nhắn Zalo: ${job.id} - trackingId: ${trackingId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
