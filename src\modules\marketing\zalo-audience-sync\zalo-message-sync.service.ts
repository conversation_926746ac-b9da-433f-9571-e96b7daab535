import { Injectable, Logger } from '@nestjs/common';
import { UserAudienceRepository } from '../repositories/user-audience.repository';
import { ZaloMessageRepository } from '../repositories/zalo-message.repository';
import { ZaloOAWorkerAdapterService } from './services/zalo-oa-worker-adapter.service';
import { ZaloMessageManagementService } from '../../../shared/services/zalo/zalo-message-management.service';
import { Not, IsNull } from 'typeorm';

/**
 * Interface cho tham số đồng bộ tin nhắn
 */
export interface SyncZaloMessagesParams {
  userId: number;
  integrationId: string;
  limit: number;
  offset: number;
  onlyExistingAudience: boolean;
  trackingId?: string;
}

/**
 * Interface cho kết quả đồng bộ tin nhắn
 */
export interface SyncZaloMessagesResult {
  processedAudiences: number;
  totalMessagesSynced: number;
  newMessagesCreated: number;
  existingMessagesUpdated: number;
  errorCount: number;
  errors: string[];
}

/**
 * Service xử lý đồng bộ tin nhắn Zalo vào database
 */
@Injectable()
export class ZaloMessageSyncService {
  private readonly logger = new Logger(ZaloMessageSyncService.name);

  constructor(
    private readonly userAudienceRepository: UserAudienceRepository,
    private readonly zaloMessageRepository: ZaloMessageRepository,
    private readonly zaloOAWorkerAdapterService: ZaloOAWorkerAdapterService,
    private readonly zaloMessageManagementService: ZaloMessageManagementService,
  ) {}

  /**
   * Đồng bộ tin nhắn Zalo vào database
   */
  async syncZaloMessages(params: SyncZaloMessagesParams): Promise<SyncZaloMessagesResult> {
    const { userId, integrationId, limit, offset, onlyExistingAudience, trackingId } = params;
    
    this.logger.log(`Bắt đầu đồng bộ tin nhắn Zalo - trackingId: ${trackingId}`);

    const result: SyncZaloMessagesResult = {
      processedAudiences: 0,
      totalMessagesSynced: 0,
      newMessagesCreated: 0,
      existingMessagesUpdated: 0,
      errorCount: 0,
      errors: [],
    };

    try {
      // 1. Lấy thông tin OA từ integration
      const oa = await this.zaloOAWorkerAdapterService.findByIntegrationId(integrationId);
      if (!oa) {
        throw new Error(`Không tìm thấy Zalo OA với integrationId: ${integrationId}`);
      }

      // 2. Lấy danh sách user-audience có zaloSocialId
      const whereCondition: any = {
        userId,
        zaloSocialId: Not(IsNull()),
      };

      if (onlyExistingAudience) {
        whereCondition.integrationId = integrationId;
      }

      const audiences = await this.userAudienceRepository.find({
        where: whereCondition,
        take: limit,
        skip: offset,
        order: { id: 'ASC' },
      });

      this.logger.log(`Tìm thấy ${audiences.length} user-audience có zaloSocialId`);

      // 3. Đồng bộ tin nhắn cho từng audience
      for (const audience of audiences) {
        try {
          await this.syncMessagesForAudience(audience, oa, result);
          result.processedAudiences++;
        } catch (error) {
          this.logger.error(
            `Lỗi khi đồng bộ tin nhắn cho audience ${audience.id}: ${error.message}`,
            error.stack,
          );
          result.errorCount++;
          result.errors.push(`Audience ${audience.id}: ${error.message}`);
        }
      }

      this.logger.log(
        `Hoàn thành đồng bộ tin nhắn Zalo - trackingId: ${trackingId}. ` +
        `Processed: ${result.processedAudiences}, Messages: ${result.totalMessagesSynced}, ` +
        `New: ${result.newMessagesCreated}, Updated: ${result.existingMessagesUpdated}, ` +
        `Errors: ${result.errorCount}`,
      );

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi đồng bộ tin nhắn Zalo: ${error.message}`, error.stack);
      result.errorCount++;
      result.errors.push(error.message);
      throw error;
    }
  }

  /**
   * Đồng bộ tin nhắn cho một audience cụ thể
   */
  private async syncMessagesForAudience(
    audience: any,
    oa: any,
    result: SyncZaloMessagesResult,
  ): Promise<void> {
    const { zaloSocialId } = audience;
    
    if (!zaloSocialId) {
      return;
    }

    try {
      // Lấy tin nhắn từ Zalo API
      const conversationMessages = await this.zaloMessageManagementService.getConversationMessages(
        oa.accessToken,
        zaloSocialId,
        { count: 20, offset: 0 }, // Lấy 20 tin nhắn gần nhất
      );

      if (!conversationMessages.messages || conversationMessages.messages.length === 0) {
        return;
      }

      // Lưu từng tin nhắn vào database
      for (const message of conversationMessages.messages) {
        try {
          await this.saveMessageToDatabase(message, oa, audience, result);
          result.totalMessagesSynced++;
        } catch (error) {
          this.logger.error(
            `Lỗi khi lưu tin nhắn ${message.message_id}: ${error.message}`,
            error.stack,
          );
          result.errorCount++;
          result.errors.push(`Message ${message.message_id}: ${error.message}`);
        }
      }

    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy tin nhắn cho audience ${audience.id}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lưu tin nhắn vào database
   */
  private async saveMessageToDatabase(
    message: any,
    oa: any,
    audience: any,
    result: SyncZaloMessagesResult,
  ): Promise<void> {
    try {
      // Kiểm tra tin nhắn đã tồn tại chưa
      const existingMessage = await this.zaloMessageRepository.findOne({
        where: {
          messageId: message.message_id,
          oaId: oa.oaId,
          userId: message.from_id === oa.oaId ? message.to_id : message.from_id,
        },
      });

      const messageData = {
        oaId: oa.oaId,
        userId: message.from_id === oa.oaId ? message.to_id : message.from_id,
        messageId: message.message_id,
        messageType: message.type || 'text',
        content: typeof message.message === 'string' ? message.message : JSON.stringify(message.message),
        data: message,
        direction: message.from_id === oa.oaId ? 'outgoing' : 'incoming',
        agentId: undefined, // Changed from null to undefined
        timestamp: message.timestamp || Date.now(),
        integrationId: audience.integrationId,
        systemUserId: audience.userId,
        updatedAt: Date.now(),
      };

      if (existingMessage) {
        // Cập nhật tin nhắn hiện có
        await this.zaloMessageRepository.update(existingMessage.id, messageData);
        result.existingMessagesUpdated++;
        this.logger.debug(`Cập nhật tin nhắn: ${message.message_id}`);
      } else {
        // Tạo tin nhắn mới
        const newMessage = this.zaloMessageRepository.create({
          ...messageData,
          createdAt: Date.now(),
        });
        await this.zaloMessageRepository.save(newMessage);
        result.newMessagesCreated++;
        this.logger.debug(`Tạo tin nhắn mới: ${message.message_id}`);
      }

    } catch (error) {
      this.logger.error(
        `Lỗi khi lưu tin nhắn ${message.message_id}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
