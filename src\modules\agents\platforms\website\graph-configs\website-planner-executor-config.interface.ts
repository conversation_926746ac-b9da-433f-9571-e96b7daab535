import { PlannerExecutorGraphConfigType } from 'src/modules/agents/graphs/multi-agents-graphs/planner-executor.graph';
import { Platform } from 'src/modules/agents/enums';
import {
  UserInfo,
  UserConvertCustomerInfo,
} from 'src/modules/agents/interfaces';
import { WebsiteInfo } from '../interfaces/website-info.interface';

export interface WebsitePlannerExecutorConfig
  extends PlannerExecutorGraphConfigType {
  platform: Platform.WEBSITE;
  currentUser: {
    /**
     * Website visitor information (anonymous lead/customer)
     * Contains basic contact info and metadata collected during interaction
     */
    websiteVisitor: UserConvertCustomerInfo;

    /**
     * Business owner information (for API key authentication)
     * Used to determine which integrations/API keys are available
     */
    websiteOwner: UserInfo;

    /**
     * Rich website context and visitor environment data
     * Includes browser info, device data, geolocation, page context
     */
    websiteInfo: WebsiteInfo;
  };
}
