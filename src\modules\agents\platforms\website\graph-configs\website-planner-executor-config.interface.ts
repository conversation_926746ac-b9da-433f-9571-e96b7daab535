import { PlannerExecutorGraphConfigType } from '../../../shared/graphs/planner-executor.graph';
import { Platform } from '../../../shared/enums';
import {
  UserInfo,
  UserConvertCustomerInfo,
} from '../../../shared/interfaces';
import { WebsiteInfo } from '../interfaces/website-info.interface';

export interface WebsitePlannerExecutorConfig
  extends PlannerExecutorGraphConfigType {
  platform: Platform.WEBSITE;
  currentUser: {
    /**
     * Website visitor information (anonymous lead/customer)
     * Contains basic contact info and metadata collected during interaction
     */
    visitor: UserConvertCustomerInfo;

    /**
     * Business owner information (for API key authentication)
     * Used to determine which integrations/API keys are available
     */
    owner: UserInfo;

    /**
     * Rich website context and visitor environment data
     * Includes browser info, device data, geolocation, page context
     */
    info: WebsiteInfo;
  };
}
