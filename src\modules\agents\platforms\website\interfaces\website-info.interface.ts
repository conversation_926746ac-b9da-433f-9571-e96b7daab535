export interface WebsiteInfo {
  serverSide: {
    clientIP?: string;
    forwardedIPs?: string[];
    userAgent?: string;
    acceptLanguage?: string;
    acceptEncoding?: string;
    connection?: string;
    host?: string;
    origin?: string;
    referer?: string;
  };
  clientSide: {
    browserInfo?: {
      userAgent?: string;
      platform?: string;
      language?: string;
      languages?: string[];
      cookieEnabled?: boolean;
      onLine?: boolean;
      hardwareConcurrency?: number;
      maxTouchPoints?: number;
      vendor?: string;
      vendorSub?: string;
      product?: string;
      productSub?: string;
    };
    screenInfo?: {
      width?: number;
      height?: number;
      availWidth?: number;
      availHeight?: number;
      colorDepth?: number;
      pixelDepth?: number;
      orientation?: string; // e.g., "portrait-primary"
    };
    windowInfo?: {
      innerWidth?: number;
      innerHeight?: number;
      outerWidth?: number;
      outerHeight?: number;
      devicePixelRatio?: number;
      scrollX?: number;
      scrollY?: number;
    };
    pageInfo?: {
      url?: string;
      pathname?: string;
      search?: string;
      hash?: string;
      host?: string;
      hostname?: string;
      port?: string;
      protocol?: string;
      title?: string;
      referrer?: string;
      domain?: string;
      lastModified?: string;
      charset?: string;
      readyState?: string; // e.g., "complete"
    };
    timeInfo?: {
      timezone?: string;
      timezoneOffset?: number;
      locale?: string;
      timestamp?: number;
      dateString?: string;
    };
    location?: {
      latitude?: number;
      longitude?: number;
      accuracy?: number;
      altitude?: number;
      heading?: number;
      speed?: number;
    };
  };
}
