import { assign, sendParent } from 'xstate';
import { WorkflowContext, NodeExecutionContext } from '../types';
import { RedisPublisherService } from '../../redis-publisher.service';

/**
 * Enhanced workflow actions với Redis integration
 * Các actions này sẽ update context và push updates lên Redis
 */
export const createEnhancedWorkflowActions = (redisPublisher: RedisPublisherService) => ({
  /**
   * Handle node completed với Redis update
   */
  handleNodeCompleted: assign({
    runningNodes: ({ context, event }: { context: WorkflowContext; event: any }) => {
      return (context.runningNodes || []).filter(id => id !== event.nodeId);
    },
    executionData: ({ context, event }: { context: WorkflowContext; event: any }) => {
      const newExecutionData = new Map(context.executionData);
      newExecutionData.set(event.nodeId, event.result.outputData);
      return newExecutionData;
    },
    metadata: ({ context, event }: { context: WorkflowContext; event: any }) => {
      const newMetadata = {
        ...context.metadata,
        completedNodes: (context.metadata?.completedNodes || 0) + 1,
        lastCompletedNode: event.nodeId,
        lastCompletedAt: Date.now(),
      };

      // Push Redis update với focused node completion data
      redisPublisher.publishNodeCompleted(context.executionId, context.metadata?.userId || 0, {
        nodeId: event.nodeId,
        success: true,
        output: event.result.outputData,
        executionTime: event.result.executionTime
      }).catch(error => {
        console.error('Failed to publish node completion to Redis:', error);
      });

      // Push workflow progress summary separately
      redisPublisher.publishWorkflowProgress(context.executionId, context.metadata?.userId || 0, {
        completedNodes: newMetadata.completedNodes,
        totalNodes: newMetadata.totalNodes,
        runningNodes: (context.runningNodes || []).filter(id => id !== event.nodeId).length,
        failedNodes: newMetadata.failedNodes || 0,
        percentage: Math.round((newMetadata.completedNodes / newMetadata.totalNodes) * 100)
      }).catch(error => {
        console.error('Failed to publish workflow progress to Redis:', error);
      });

      return newMetadata;
    },
  }),

  /**
   * Handle node failed với Redis update
   */
  handleNodeFailed: assign({
    runningNodes: ({ context, event }: { context: WorkflowContext; event: any }) => {
      return (context.runningNodes || []).filter(id => id !== event.nodeId);
    },
    errors: ({ context, event }: { context: WorkflowContext; event: any }) => {
      const newErrors = new Map(context.errors);
      newErrors.set(event.nodeId, event.error);
      return newErrors;
    },
    metadata: ({ context, event }: { context: WorkflowContext; event: any }) => {
      const newMetadata = {
        ...context.metadata,
        failedNodes: (context.metadata?.failedNodes || 0) + 1,
        lastFailedNode: event.nodeId,
        lastFailedAt: Date.now(),
      };

      // Push Redis update với focused node failure data
      redisPublisher.publishNodeCompleted(context.executionId, context.metadata?.userId || 0, {
        nodeId: event.nodeId,
        success: false,
        error: event.error,
        executionTime: 0
      }).catch(error => {
        console.error('Failed to publish node failure to Redis:', error);
      });

      // Push workflow progress summary separately
      redisPublisher.publishWorkflowProgress(context.executionId, context.metadata?.userId || 0, {
        completedNodes: context.metadata?.completedNodes || 0,
        totalNodes: newMetadata.totalNodes,
        runningNodes: (context.runningNodes || []).filter(id => id !== event.nodeId).length,
        failedNodes: newMetadata.failedNodes,
        percentage: Math.round(((context.metadata?.completedNodes || 0) + newMetadata.failedNodes) / newMetadata.totalNodes * 100)
      }).catch(error => {
        console.error('Failed to publish workflow progress to Redis:', error);
      });

      return newMetadata;
    },
  }),

  /**
   * Update dependencies và determine ready nodes với Redis update
   */
  updateDependencies: assign({
    readyNodes: ({ context }: { context: WorkflowContext }) => {
      const readyNodes: string[] = [];
      
      if (!context.nodes || !context.dependencyGraph) {
        return readyNodes;
      }

      // Check each waiting node to see if dependencies are met
      for (const nodeId of context.waitingNodes || []) {
        const nodeState = context.nodes.get(nodeId);
        if (!nodeState) continue;

        const dependencies = (context.dependencyGraph as any)?.get?.(nodeId) || [];
        const allDependenciesMet = dependencies.every((depId: string) => {
          const depState = context.nodes?.get(depId);
          return depState?.status === 'completed';
        });

        if (allDependenciesMet) {
          readyNodes.push(nodeId);
        }
      }

      // Push Redis update với minimal ready nodes info
      if (readyNodes.length > 0) {
        redisPublisher.publishTestProgress(context.executionId, context.metadata?.userId || 0, {
          currentStep: context.metadata?.completedNodes || 0,
          totalSteps: context.metadata?.totalNodes || 0,
          percentage: Math.round(((context.metadata?.completedNodes || 0) / (context.metadata?.totalNodes || 1)) * 100),
          description: `${readyNodes.length} nodes are now ready for execution`,
          nodeId: readyNodes[0], // Just the first ready node as reference
          nodeResult: {
            type: 'dependencies_resolved',
            readyNodesCount: readyNodes.length,
            readyNodes: readyNodes
          }
        }).catch(error => {
          console.error('Failed to publish ready nodes to Redis:', error);
        });
      }

      return readyNodes;
    },
    waitingNodes: ({ context }: { context: WorkflowContext }) => {
      const readyNodes: string[] = [];
      
      if (!context.nodes || !context.dependencyGraph) {
        return context.waitingNodes || [];
      }

      // Determine ready nodes (same logic as above)
      for (const nodeId of context.waitingNodes || []) {
        const nodeState = context.nodes.get(nodeId);
        if (!nodeState) continue;

        const dependencies = (context.dependencyGraph as any)?.get?.(nodeId) || [];
        const allDependenciesMet = dependencies.every((depId: string) => {
          const depState = context.nodes?.get(depId);
          return depState?.status === 'completed';
        });

        if (allDependenciesMet) {
          readyNodes.push(nodeId);
        }
      }

      // Remove ready nodes from waiting nodes
      return (context.waitingNodes || []).filter(id => !readyNodes.includes(id));
    },
  }),

  /**
   * Spawn ready nodes với Redis notification
   */
  spawnReadyNodes: assign({
    runningNodes: ({ context }: { context: WorkflowContext }) => {
      const newRunningNodes = [...(context.runningNodes || [])];
      const spawnedNodes: string[] = [];
      
      for (const nodeId of context.readyNodes || []) {
        const nodeState = context.nodes?.get(nodeId);
        if (nodeState && nodeState.status === 'pending') {
          // Update node state
          nodeState.status = 'running';
          nodeState.startTime = Date.now();
          
          newRunningNodes.push(nodeId);
          spawnedNodes.push(nodeId);
        }
      }

      // Push Redis update với minimal spawned nodes info
      if (spawnedNodes.length > 0) {
        redisPublisher.publishTestProgress(context.executionId, context.metadata?.userId || 0, {
          currentStep: context.metadata?.completedNodes || 0,
          totalSteps: context.metadata?.totalNodes || 0,
          percentage: Math.round(((context.metadata?.completedNodes || 0) / (context.metadata?.totalNodes || 1)) * 100),
          description: `Started execution of ${spawnedNodes.length} nodes`,
          nodeId: spawnedNodes[0], // First spawned node as reference
          nodeResult: {
            type: 'nodes_spawned',
            spawnedNodesCount: spawnedNodes.length,
            spawnedNodes: spawnedNodes
          }
        }).catch(error => {
          console.error('Failed to publish spawned nodes to Redis:', error);
        });
      }
      
      return newRunningNodes;
    },
    readyNodes: () => [], // Clear ready nodes after spawning
  }),

  /**
   * Prepare workflow result với final Redis update
   */
  prepareWorkflowResult: assign({
    result: ({ context }: { context: WorkflowContext }) => {
      const totalNodes = context.metadata?.totalNodes || 0;
      const completedNodes = context.metadata?.completedNodes || 0;
      const failedNodes = context.metadata?.failedNodes || 0;

      const result = {
        success: completedNodes === totalNodes && failedNodes === 0,
        totalNodes,
        completedNodes,
        failedNodes,
        executionTime: Date.now() - (context.metadata?.startTime || Date.now()),
        output: Object.fromEntries(context.executionData || new Map()),
        errors: Object.fromEntries(context.errors || new Map()),
        finishedAt: Date.now()
      };

      // Push final result to Redis
      redisPublisher.publishTestCompleted(context.executionId, context.metadata?.userId || 0, {
        success: result.success,
        output: result.output,
        error: result.errors,
        executionTime: result.executionTime,
        nodesExecuted: completedNodes,
        totalNodes: totalNodes
      }).catch(error => {
        console.error('Failed to publish workflow completion to Redis:', error);
      });

      return result;
    },
  } as any),
});

/**
 * Enhanced node execution actions với Redis integration
 */
export const createEnhancedNodeActions = (redisPublisher: RedisPublisherService) => ({
  /**
   * Update node state với Redis notification
   */
  updateNodeState: assign({
    status: ({ event }: { event: any }) => event.status,
    result: ({ event }: { event: any }) => event.result,
    endTime: () => Date.now(),
  }),

  /**
   * Notify parent workflow với Redis update
   */
  notifyExecutionCompleted: sendParent(({ context }: { context: any }) => {
    // Push node completion to Redis
    redisPublisher.publishTestProgress(
      context.nodeContext?.executionId || 'unknown',
      context.nodeContext?.userId || 0,
      {
        currentStep: 1,
        totalSteps: 1,
        percentage: 100,
        description: `Node ${context.nodeContext?.node?.id} completed`,
        nodeId: context.nodeContext?.node?.id,
        nodeResult: context.result
      }
    ).catch(error => {
      console.error('Failed to publish node completion to Redis:', error);
    });

    return {
      type: 'NODE_COMPLETED',
      nodeId: context.metadata?.nodeId || context.nodeContext?.node?.id,
      result: context.result,
    };
  }),

  /**
   * Notify execution failed với Redis update
   */
  notifyExecutionFailed: sendParent(({ context }: { context: any }) => {
    // Push node failure to Redis
    redisPublisher.publishError(
      context.nodeContext?.executionId || 'unknown',
      context.nodeContext?.userId || 0,
      context.error,
      'test'
    ).catch(error => {
      console.error('Failed to publish node failure to Redis:', error);
    });

    return {
      type: 'NODE_FAILED',
      nodeId: context.metadata?.nodeId || context.nodeContext?.node?.id,
      error: context.error,
    };
  }),
});
