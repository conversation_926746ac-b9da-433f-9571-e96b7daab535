export enum GenderEnum {
  MALE = 'MALE',
  FEMALE = 'FEMALE',
  OTHER = 'OTHER'
}

export const GenderUtils = {
  /**
   * Lấy giá trị chuỗi của một giới tính
   * @param gender Giới tính
   * @returns Giá trị chuỗi tương ứng
   */
  getValue(gender: GenderEnum): string {
    return gender;
  },

  /**
   * Lấy enum GenderEnum từ giá trị chuỗi
   * @param value Giá trị chuỗi
   * @returns Giới tính tương ứng
   */
  getGender(value: string): GenderEnum {
    return GenderEnum[value as keyof typeof GenderEnum];
  },
};
