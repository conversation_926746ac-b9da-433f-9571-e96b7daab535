import { Inject, Injectable, Logger } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { RedisChannelBuilder } from '../enums/redis-channels.enum';
import {
    NodeCompletedLifecycleEvent,
    NodeFailedLifecycleEvent,
    NodeProcessingLifecycleEvent,
    NodeStartedLifecycleEvent,
    WorkflowCompletedLifecycleEvent,
    WorkflowFailedLifecycleEvent,
    WorkflowStartedLifecycleEvent,
} from '../interfaces/redis-events.interface';

/**
 * Redis Publisher Service for real-time workflow updates
 * Publishes test và execution events để BE app có thể subscribe
 */
@Injectable()
export class RedisPublisherService {
    private readonly logger = new Logger(RedisPublisherService.name);

    constructor(
        @Inject('REDIS_CLIENT') private readonly redisClient: ClientProxy,
    ) { }

    /**
     * Generic publish method for any channel and data
     */
    async publish(channel: string, data: any): Promise<void> {
        try {
            await this.redisClient.send(
                channel,
                data
            ).toPromise();

            this.logger.debug(`Published to ${channel}:`, data.type || 'event');
        } catch (error) {
            this.logger.error(`Failed to publish to ${channel}:`, error);
            // Don't throw - event publishing failure shouldn't fail execution
        }
    }

    /**
     * Publish node started event for workflow execution
     */
    async publishNodeStarted(
        workflowId: string,
        nodeId: string,
        userId: number,
        executionId: string,
        data?: any,
    ): Promise<void> {
        const event: NodeStartedLifecycleEvent = {
            type: 'NODE_STARTED',
            workflowId,
            nodeId,
            executionId,
            userId,
            data,
            context: data?.context,
            timestamp: Date.now(),
            timestampISO: new Date().toISOString(),
        };

        const channel = RedisChannelBuilder.buildNodeStartedChannel(nodeId);
        await this.publish(channel, event);

        this.logger.debug(`Published node started event for node: ${nodeId}`);
    }

    /**
     * Publish node processing event for workflow execution
     */
    async publishNodeProcessing(
        workflowId: string,
        nodeId: string,
        userId: number,
        executionId: string,
        data?: any,
    ): Promise<void> {
        const event: NodeProcessingLifecycleEvent = {
            type: 'NODE_PROCESSING',
            workflowId,
            nodeId,
            executionId,
            userId,
            status: 'processing',
            data,
            timestamp: Date.now(),
            timestampISO: new Date().toISOString(),
        };

        const channel = RedisChannelBuilder.buildNodeProcessingChannel(nodeId);
        await this.publish(channel, event);

        this.logger.debug(`Published node processing event for node: ${nodeId}`);
    }

    /**
     * Publish node completed event for workflow execution
     */
    async publishNodeCompleted(
        workflowId: string,
        nodeId: string,
        userId: number,
        executionId: string,
        result: any,
    ): Promise<void> {
        const event: NodeCompletedLifecycleEvent = {
            type: 'NODE_COMPLETED',
            workflowId,
            nodeId,
            executionId,
            userId,
            result: result?.output || result,
            executionTime: result?.executionTime,
            status: 'completed',
            timestamp: Date.now(),
            timestampISO: new Date().toISOString(),
        };

        const channel = RedisChannelBuilder.buildNodeCompletedChannel(nodeId);
        await this.publish(channel, event);

        this.logger.debug(`Published node completed event for node: ${nodeId}`);
    }

    /**
     * Publish node failed event for workflow execution
     */
    async publishNodeFailed(
        workflowId: string,
        nodeId: string,
        userId: number,
        executionId: string,
        error: any,
    ): Promise<void> {
        const event: NodeFailedLifecycleEvent = {
            type: 'NODE_FAILED',
            workflowId,
            nodeId,
            executionId,
            userId,
            error: {
                message: error?.message || error,
                code: error?.code || 'NODE_EXECUTION_ERROR',
                stack: error?.stack
            },
            status: 'failed',
            timestamp: Date.now(),
            timestampISO: new Date().toISOString(),
        };

        const channel = RedisChannelBuilder.buildNodeFailedChannel(nodeId);
        await this.publish(channel, event);

        this.logger.debug(`Published node failed event for node: ${nodeId}`);
    }

    /**
     * Publish workflow started event
     */
    async publishWorkflowStarted(
        workflowId: string,
        userId: number,
        executionId: string,
        startNode?: string,
        data?: any
    ): Promise<void> {
        const event: WorkflowStartedLifecycleEvent = {
            type: 'WORKFLOW_STARTED',
            workflowId,
            userId,
            executionId,
            startNode,
            isPartialExecution: !!startNode,
            data,
            timestamp: Date.now(),
            timestampISO: new Date().toISOString(),
        };

        const channel = RedisChannelBuilder.buildWorkflowStartedChannel(workflowId);
        await this.publish(channel, event);

        this.logger.debug(`Published workflow started event for workflow: ${workflowId}`);
    }

    /**
     * Publish workflow completed event
     */
    async publishWorkflowCompleted(
        workflowId: string,
        userId: number,
        executionId: string,
        result: any,
        metadata?: {
            startNode?: string;
            totalNodes?: number;
            executionTime?: number;
            isPartialExecution?: boolean;
        }
    ): Promise<void> {
        const event: WorkflowCompletedLifecycleEvent = {
            type: 'WORKFLOW_COMPLETED',
            workflowId,
            userId,
            executionId,
            result,
            startNode: metadata?.startNode,
            totalNodes: metadata?.totalNodes || 0,
            executionTime: metadata?.executionTime || 0,
            isPartialExecution: metadata?.isPartialExecution || false,
            status: 'completed',
            timestamp: Date.now(),
            timestampISO: new Date().toISOString(),
        };

        const channel = RedisChannelBuilder.buildWorkflowCompletedChannel(workflowId);
        await this.publish(channel, event);

        this.logger.debug(`Published workflow completed event for workflow: ${workflowId}`);
    }

    /**
     * Publish workflow failed event
     */
    async publishWorkflowFailed(
        workflowId: string,
        userId: number,
        executionId: string,
        error: any,
        metadata?: {
            startNode?: string;
            isPartialExecution?: boolean;
        }
    ): Promise<void> {
        const event: WorkflowFailedLifecycleEvent = {
            type: 'WORKFLOW_FAILED',
            workflowId,
            userId,
            executionId,
            error: {
                message: error?.message || error,
                code: error?.code || 'WORKFLOW_EXECUTION_ERROR',
                stack: error?.stack
            },
            startNode: metadata?.startNode,
            isPartialExecution: metadata?.isPartialExecution || false,
            status: 'failed',
            timestamp: Date.now(),
            timestampISO: new Date().toISOString(),
        };

        const channel = RedisChannelBuilder.buildWorkflowFailedChannel(workflowId);
        await this.publish(channel, event);

        this.logger.debug(`Published workflow failed event for workflow: ${workflowId}`);
    }

    /**
     * Legacy method - Publish execution completed event
     * For backward compatibility
     */
    async publishExecutionCompleted(
        executionId: string,
        userId: number,
        result: any,
        metadata?: any
    ): Promise<void> {
        // Map to workflow completed event
        const workflowId = metadata?.workflowId || 'unknown';
        await this.publishWorkflowCompleted(workflowId, userId, executionId, result, metadata);
    }

    /**
     * Legacy method - Publish error event
     * For backward compatibility
     */
    async publishError(
        executionId: string,
        userId: number,
        error: any,
        type: string = 'execution'
    ): Promise<void> {
        // Map to workflow failed event
        const workflowId = 'unknown';
        await this.publishWorkflowFailed(workflowId, userId, executionId, error);
    }
}
