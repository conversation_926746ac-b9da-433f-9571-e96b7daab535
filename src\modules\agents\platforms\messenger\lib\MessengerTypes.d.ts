export interface SendApiTextMessageRequest {
  recipient: {
    id: string;
  };
  message: { text: string };
}

export interface SendApiTextMessageResponse {
  recipient_id: string;
  message_id: string;
}

export interface SendApiMediaMessagePhotoRequest {
  recipient: { id: string };
  message: {
    attachment: {
      type: string;
      payload: { url: string; is_reusable: boolean };
    };
  };
}

export interface SendApiMediaMessagePhotoResponse {
  recipient_id: string;
  message_id: string;
}

export interface SendApiQuickRepliesRequest {
  recipient: { id: string };
  message: {
    text: string;
    quick_replies: {
      content_type: 'text' | 'user_phone_number' | 'user_email';
      title: string;
      payload: string;
      image_url: string;
    }[];
  };
}

export interface SendApiQuickRepliesResponse {
  recipient_id: string;
  message_id: string;
}

export interface SendApiButtonTemplateRequest {
  recipient: { id: string };
  message: {
    attachment: {
      type: 'template';
      payload: {
        template_type: 'button';
        text: string;
        buttons: {
          type:
            | 'web_url'
            | 'postback'
            | 'phone_number'
            | 'account_link'
            | 'account_unlink'
            | 'game_play';
          title: string;
          payload: string;
          image_url: string;
        }[];
      };
    };
  };
}

export interface SendApiButtonTemplateResponse {
  recipient_id: string;
  message_id: string;
}

export interface SendApiGenericTemplateRequest {
  recipient: { id: string };
  message: {
    attachment: {
      type: 'template';
      payload: {
        template_type: 'generic';
        elements: {
          title: string;
          image_url: string;
          subtitle: string;
          default_action?: {
            type: 'web_url';
            url: string;
            webview_height_ratio?: 'compact' | 'tall' | 'full';
          };
          buttons?: {
            type:
              | 'web_url'
              | 'postback'
              | 'phone_number'
              | 'account_link'
              | 'account_unlink';
            title: string;
            url?: string;
            payload?: string;
          }[];
        }[];
      };
    };
  };
}

export interface SendApiGenericTemplateResponse {
  recipient_id: string;
  message_id: string;
}

export interface SendApiMediaTemplateRequest {
  recipient: {
    id: string;
    user_ref?: string;
    post_id?: string;
    comment_id?: string;
  };
  message: {
    attachment: {
      type: 'template';
      payload: {
        template_type: 'media';
        elements: {
          media_type: 'image' | 'video';
          attachment_id: string;
          buttons?: {
            type:
              | 'web_url'
              | 'postback'
              | 'phone_number'
              | 'account_link'
              | 'account_unlink';
            title: string;
            url?: string;
            payload?: string;
          }[];
        }[];
      };
    };
  };
}

export interface SendApiMediaTemplateResponse {
  recipient_id: string;
  message_id: string;
}

export interface SendApiNamespace {
  textMessage(
    pageId: string,
    body: SendApiTextMessageRequest,
  ): Promise<SendApiTextMessageResponse>;
  mediaMessagePhoto(
    pageId: string,
    body: SendApiMediaMessagePhotoRequest,
  ): Promise<SendApiMediaMessagePhotoResponse>;
  quickReplies(
    pageId: string,
    body: SendApiQuickRepliesRequest,
  ): Promise<SendApiQuickRepliesResponse>;
  buttonTemplate(
    pageId: string,
    body: SendApiButtonTemplateRequest,
  ): Promise<SendApiButtonTemplateResponse>;
  genericTemplate(
    pageId: string,
    body: SendApiGenericTemplateRequest,
  ): Promise<SendApiGenericTemplateResponse>;
  mediaTemplate(
    pageId: string,
    body: SendApiMediaTemplateRequest,
  ): Promise<SendApiMediaTemplateResponse>;
}

export interface AttachmentUploadApiAttachmentUploadPhotoRequest {
  message: {
    attachment: {
      type: 'image';
      payload: { is_reusable: boolean; url?: string };
    };
  };
}

export interface AttachmentUploadApiAttachmentUploadPhotoResponse {
  attachment_id: string;
}

export interface AttachmentUploadApiNamespace {
  attachmentUploadPhoto(
    pageId: string,
    body: AttachmentUploadApiAttachmentUploadPhotoRequest,
  ): Promise<AttachmentUploadApiAttachmentUploadPhotoResponse>;
}

export interface ModerateConversationsApiBlockUserRequest {
  user_ids: { id: string }[];
  actions: 'block_user'[];
}

export interface ModerateConversationsApiBlockUserResponse {
  success: boolean;
}

export interface ModerateConversationsApiUnblockUserRequest {
  user_ids: { id: string }[];
  actions: 'unblock_user'[];
}

export interface ModerateConversationsApiUnblockUserResponse {
  success: boolean;
}

export interface ModerateConversationsApiBanUserRequest {
  user_ids: { id: string }[];
  actions: 'ban_user'[];
}

export interface ModerateConversationsApiBanUserResponse {
  success: boolean;
}

export interface ModerateConversationsApiUnbanUserRequest {
  user_ids: { id: string }[];
  actions: 'unban_user'[];
}

export interface ModerateConversationsApiUnbanUserResponse {
  success: boolean;
}

export interface ModerateConversationsApiMoveConversationToSpamRequest {
  user_ids: {
    id: string;
  }[];
  actions: 'move_to_spam'[];
}

export interface ModerateConversationsApiMoveConversationToSpamResponse {
  success: boolean;
}

export interface ModerateConversationsApiNamespace {
  blockUser(
    pageId: string,
    body: ModerateConversationsApiBlockUserRequest,
  ): Promise<ModerateConversationsApiBlockUserResponse>;
  unblockUser(
    pageId: string,
    body: ModerateConversationsApiUnblockUserRequest,
  ): Promise<ModerateConversationsApiUnblockUserResponse>;
  banUser(
    pageId: string,
    body: ModerateConversationsApiBanUserRequest,
  ): Promise<ModerateConversationsApiBanUserResponse>;
  unbanUser(
    pageId: string,
    body: ModerateConversationsApiUnbanUserRequest,
  ): Promise<ModerateConversationsApiUnbanUserResponse>;
  moveConversationToSpam(
    pageId: string,
    body: ModerateConversationsApiMoveConversationToSpamRequest,
  ): Promise<ModerateConversationsApiMoveConversationToSpamResponse>;
}

export interface TemplatesGenericTemplateRequest {
  recipient: { id: string };
  message: {
    attachment: {
      type: 'template';
      payload: {
        template_type: 'generic';
        elements: {
          title: string;
          image_url: string;
          subtitle?: string;
          default_action?: {
            type: 'web_url';
            url: string;
            webview_height_ratio?: 'compact' | 'tall' | 'full';
          };
          buttons?: {
            type:
              | 'web_url'
              | 'postback'
              | 'phone_number'
              | 'account_link'
              | 'account_unlink';
            title: string;
            url?: string;
            payload?: string;
          }[];
        }[];
      };
    };
  };
}

export interface TemplatesGenericTemplateResponse {
  recipient_id: string;
  message_id: string;
}

export interface TemplatesButtonTemplateRequest {
  recipient: { id: string };
  message: {
    attachment: {
      type: 'template';
      payload: {
        template_type: 'button';
        text: string;
        buttons: {
          type:
            | 'web_url'
            | 'postback'
            | 'phone_number'
            | 'account_link'
            | 'account_unlink';
          title: string;
          url?: string;
          payload?: string;
          image_url?: string;
        }[];
      };
    };
  };
}

export interface TemplatesButtonTemplateResponse {
  recipient_id: string;
  message_id: string;
}

export interface TemplatesNamespace {
  genericTemplate(
    pageId: string,
    body: TemplatesGenericTemplateRequest,
  ): Promise<TemplatesGenericTemplateResponse>;
  buttonTemplate(
    pageId: string,
    body: TemplatesButtonTemplateRequest,
  ): Promise<TemplatesButtonTemplateResponse>;
}

export interface Namespace {
  sendApi: SendApiNamespace;
  attachmentUploadApi: AttachmentUploadApiNamespace;
  moderateConversationsApi: ModerateConversationsApiNamespace;
  templates: TemplatesNamespace;
}
