// import { MultiServerMCPClient } from '@langchain/mcp-adapters';

// // Create client and connect to server
// const client = new MultiServerMCPClient({
//   // Global tool configuration options
//   // Whether to throw on errors if a tool fails to load (optional, default: true)
//   throwOnLoadError: true,
//   // Whether to prefix tool names with the server name (optional, default: true)
//   prefixToolNameWithServerName: true,
//   // Optional additional prefix for tool names (optional, default: "mcp")
//   additionalToolNamePrefix: 'mcp',

//   // Use standardized content block format in tool outputs
//   useStandardContentBlocks: true,

//   // Server configuration
//   mcpServers: {
//     // RedAI Affiliate MCP Server
//     'redai-affiliate': {
//       transport: 'http',
//       url: 'http://localhost:8004/mcp',
//       automaticSSEFallback: false,
//       headers: {
//         Authorization: 'Bearer **************************************************************************************************************************
//       }
//     },

//     // RedAI Blog MCP Server
//     'redai-blog': {
//       transport: 'http',
//       url: 'http://localhost:8005/mcp',
//       automaticSSEFallback: false,
//     },

//     // RedAI Model MCP Server
//     'redai-model': {
//       transport: 'http',
//       url: 'http://localhost:8006/mcp',
//       automaticSSEFallback: false,
//     },

//     // RedAI Data MCP Server
//     'redai-data': {
//       transport: 'http',
//       url: 'http://localhost:8007/mcp',
//       automaticSSEFallback: false,
//     },

//     // RedAI Marketplace MCP Server
//     'redai-marketplace': {
//       transport: 'http',
//       url: 'http://localhost:8008/mcp',
//       automaticSSEFallback: false,
//       reconnect: {
//         enabled: true,
//         maxAttempts: 5,
//         delayMs: 2000,
//       },
//     },

//     // RedAI Shipment MCP Server
//     'redai-shipment': {
//       transport: 'http',
//       url: 'http://localhost:8009/mcp',
//       automaticSSEFallback: false,

//     },
//   },
// });

// async function main() {
//   const common-tools = await client.getTools();
//   console.log(`tool size = ${common-tools.length}`);
// }

// main().finally(() => client.close());
