/**
 * HTTP Request Executor Example
 * Demonstrates the new executeNode method with standardized input/output
 */

import { 
  IHttpRequestParameters, 
  IHttpRequestOutput,
  EHttpMethod,
  EAuthType,
  EContentType 
} from '../interfaces/core/http/http-request.interface';

/**
 * Example 1: Simple GET Request
 */
export const simpleGetRequest: IHttpRequestParameters = {
  url: 'https://jsonplaceholder.typicode.com/posts/1',
  method: EHttpMethod.GET,
  auth_type: EAuthType.NONE,
  headers: {
    'Accept': 'application/json',
    'User-Agent': 'RedAI-Workflow/1.0'
  },
  timeout: 30000
};

/**
 * Example 2: POST Request with Bearer Auth
 */
export const postRequestWithAuth: IHttpRequestParameters = {
  url: 'https://api.example.com/users',
  method: EHttpMethod.POST,
  auth_type: EAuthType.BEARER,
  auth_config: {
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
  },
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  body: {
    name: 'John Doe',
    email: '<EMAIL>',
    role: 'user'
  },
  timeout: 30000
};

/**
 * Example 3: API Key Authentication
 */
export const apiKeyRequest: IHttpRequestParameters = {
  url: 'https://api.service.com/data',
  method: EHttpMethod.GET,
  auth_type: EAuthType.API_KEY,
  auth_config: {
    api_key: 'sk-1234567890abcdef',
    api_key_header: 'X-API-Key'
  },
  query_params: {
    limit: '10',
    offset: '0',
    sort: 'created_at'
  },
  timeout: 30000
};

/**
 * Example 4: Basic Authentication
 */
export const basicAuthRequest: IHttpRequestParameters = {
  url: 'https://secure-api.example.com/protected',
  method: EHttpMethod.GET,
  auth_type: EAuthType.BASIC,
  auth_config: {
    username: 'admin',
    password: 'secret123'
  },
  headers: {
    'Accept': 'application/json'
  },
  timeout: 30000
};

/**
 * Example Success Response
 */
export const exampleSuccessResponse: IHttpRequestOutput = {
  code: 200,
  data: {
    success: true,
    status_code: 200,
    status_text: 'OK',
    headers: {
      'content-type': 'application/json; charset=utf-8',
      'content-length': '292',
      'x-request-id': 'req-12345'
    },
    body: {
      id: 1,
      title: 'sunt aut facere repellat provident occaecati excepturi optio reprehenderit',
      body: 'quia et suscipit\nsuscipit recusandae consequuntur expedita et cum\nreprehenderit molestiae ut ut quas totam\nnostrum rerum est autem sunt rem eveniet architecto',
      userId: 1
    },
    response_time: 1250,
    final_url: 'https://jsonplaceholder.typicode.com/posts/1',
    error: null,
    
    response: {
      code: 200,
      status: 'OK',
      headers: {
        'content-type': 'application/json; charset=utf-8',
        'content-length': '292',
        'x-request-id': 'req-12345'
      },
      data: {
        id: 1,
        title: 'sunt aut facere repellat provident occaecati excepturi optio reprehenderit',
        body: 'quia et suscipit\nsuscipit recusandae consequuntur expedita et cum\nreprehenderit molestiae ut ut quas totam\nnostrum rerum est autem sunt rem eveniet architecto',
        userId: 1
      },
      size: 292,
      contentType: 'application/json; charset=utf-8'
    },
    
    request: {
      method: 'GET',
      url: 'https://jsonplaceholder.typicode.com/posts/1',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'RedAI-Workflow/1.0'
      },
      body: null,
      timestamp: 1704067200000
    },
    
    metadata: {
      content_length: 292,
      request_id: 'req-12345',
      execution_time: 1250
    }
  }
};

/**
 * Example Error Response
 */
export const exampleErrorResponse: IHttpRequestOutput = {
  code: 404,
  data: {
    success: false,
    status_code: 404,
    status_text: 'Not Found',
    headers: {
      'content-type': 'application/json'
    },
    body: {
      error: 'Resource not found',
      message: 'The requested resource does not exist'
    },
    response_time: 850,
    final_url: 'https://api.example.com/users/999',
    error: 'Request failed with status code 404',
    
    response: {
      code: 404,
      status: 'Not Found',
      headers: {
        'content-type': 'application/json'
      },
      data: {
        error: 'Resource not found',
        message: 'The requested resource does not exist'
      },
      size: 78,
      contentType: 'application/json'
    },
    
    request: {
      method: 'GET',
      url: 'https://api.example.com/users/999',
      headers: {
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
      },
      body: null,
      timestamp: 1704067200000
    },
    
    metadata: {
      content_length: 78,
      request_id: 'req-12346',
      execution_time: 850
    }
  }
};

/**
 * Usage Example
 */
export const usageExample = `
import { HttpRequestExecutorService } from '../services/http-request-executor.service';
import { IHttpRequestParameters, EHttpMethod, EAuthType } from '../interfaces/core/http/http-request.interface';

// Inject service
constructor(private readonly httpExecutor: HttpRequestExecutorService) {}

// Prepare request parameters
const requestParams: IHttpRequestParameters = {
  url: 'https://api.example.com/users',
  method: EHttpMethod.POST,
  auth_type: EAuthType.BEARER,
  auth_config: {
    token: 'your-jwt-token'
  },
  headers: {
    'Content-Type': 'application/json'
  },
  body: {
    name: 'John Doe',
    email: '<EMAIL>'
  },
  timeout: 30000
};

// Execute request
const result = await this.httpExecutor.executeNode(requestParams);

// Handle response
console.log('HTTP Status Code:', result.code);
console.log('Success:', result.data.success);
console.log('Response Body:', result.data.body);
console.log('Response Time:', result.data.response_time);

// Check status
if (result.code === 200 || result.code === 201) {
  console.log('Request successful!');
  console.log('Created user:', result.data.body);
} else if (result.code >= 400) {
  console.log('Request failed:', result.data.error);
  console.log('Error details:', result.data.body);
}
`;

/**
 * Advanced Usage with Error Handling
 */
export const advancedUsageExample = `
async function executeHttpRequest(params: IHttpRequestParameters): Promise<any> {
  try {
    const result = await httpExecutor.executeNode(params);
    
    // Log request details
    console.log(\`Request: \${result.data.request.method} \${result.data.request.url}\`);
    console.log(\`Response: \${result.code} \${result.data.status_text}\`);
    console.log(\`Duration: \${result.data.response_time}ms\`);
    
    // Handle different status codes
    switch (true) {
      case result.code >= 200 && result.code < 300:
        return { success: true, data: result.data.body };
        
      case result.code >= 400 && result.code < 500:
        throw new Error(\`Client error: \${result.data.error}\`);
        
      case result.code >= 500:
        throw new Error(\`Server error: \${result.data.error}\`);
        
      default:
        throw new Error(\`Network error: \${result.data.error}\`);
    }
    
  } catch (error) {
    console.error('HTTP request failed:', error);
    throw error;
  }
}
`;
