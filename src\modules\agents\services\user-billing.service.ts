import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AssistantSpendingHistory } from '../entities/assistant-spending-history.entity';
import { AssistantSpendingType } from '../enums/assistant-spending-type.enum';
import { Platform } from '../enums/platform.enum';
import {
  TokenUsageCollector,
  DetailedUsage,
} from '../utils/token-usage-collector';
import { User } from '../entities/user/user.entity';

/**
 * User Billing Service
 *
 * Handles user balance updates and spending history for AI agent usage.
 * Combines user point balance management with detailed spending analytics.
 *
 * Key Features:
 * - Updates user point balances with safe SQL (GREATEST pattern)
 * - Creates individual spending records for audit trail
 * - Maps platforms to spending types for proper categorization
 * - Processes only user spending (not employees)
 * - Provides spending analytics and reporting
 *
 * Data Flow:
 * WorkerTokenUsageCollector → processTokenUsage() → User balance update + AssistantSpendingHistory records
 */
@Injectable()
export class UserBillingService {
  private readonly logger = new Logger(UserBillingService.name);

  constructor(
    @InjectRepository(AssistantSpendingHistory)
    private readonly spendingHistoryRepository: Repository<AssistantSpendingHistory>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  /**
   * Process token usage and update user balance + create spending history
   * Main entry point that handles both billing and audit trail
   *
   * @param runId - The run ID for logging context
   * @param tokenUsageCollector - Collector with detailed usage data
   * @param user - User context (only userId will be processed)
   * @param platform - Platform where the spending occurred
   */
  async processTokenUsage(
    runId: string,
    tokenUsageCollector: TokenUsageCollector,
    user: { userId?: number; employeeId?: number },
    platform: Platform,
  ): Promise<void> {
    // Only process billing for users, not employees
    if (!user.userId) {
      this.logger.debug(
        `Skipping token usage processing for employee run ${runId}`,
        {
          runId,
          employeeId: user.employeeId,
        },
      );
      return;
    }

    const usageSummary = tokenUsageCollector.getUsageSummary();

    this.logger.debug('Processing token usage after graph completion', {
      runId,
      userId: user.userId,
      totalCost: usageSummary.totalCost,
      totalTokens: usageSummary.totalTokens,
      totalUsages: usageSummary.totalUsages,
    });

    try {
      // 1. Update user point balance if there's cost
      if (usageSummary.totalCost > 0) {
        await this.updateUserPointBalance(
          user.userId,
          usageSummary.totalCost,
          runId,
        );
      }

      // 2. Create spending history records for audit trail
      await this.processSpendingHistory(
        runId,
        tokenUsageCollector,
        user,
        platform,
      );

      this.logger.debug(
        `Successfully processed token usage for user ${user.userId}`,
        {
          runId,
          userId: user.userId,
          totalCost: usageSummary.totalCost,
          recordCount: tokenUsageCollector.getUsages().length,
        },
      );
    } catch (error) {
      this.logger.error(`Failed to process token usage for run ${runId}:`, {
        runId,
        userId: user.userId,
        totalCost: usageSummary.totalCost,
        error: error.message,
        stack: error.stack,
      });

      // Don't throw - billing failure shouldn't fail the run
      this.logger.warn(`Continuing despite billing failure for run ${runId}`, {
        runId,
        userId: user.userId,
      });
    }
  }

  /**
   * Update user point balance with safe SQL to prevent negative balances
   * Uses GREATEST(0, balance - cost) pattern for safety
   *
   * @param userId - User ID to update balance for
   * @param cost - Points to deduct from balance
   * @param runId - Run ID for logging context
   */
  async updateUserPointBalance(
    userId: number,
    cost: number,
    runId: string,
  ): Promise<void> {
    try {
      await this.userRepository
        .createQueryBuilder()
        .update(User)
        .set({
          pointsBalance: () => `GREATEST(0, points_balance - ${cost})`,
        })
        .where('id = :userId', { userId })
        .execute();

      this.logger.debug(`Updated user ${userId} balance by -${cost} points`, {
        userId,
        cost,
        runId,
      });
    } catch (error) {
      this.logger.error(`Failed to update point balance for user ${userId}:`, {
        userId,
        cost,
        runId,
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  /**
   * Get user's current point balance
   * Utility method for balance checking
   *
   * @param userId - User ID to get balance for
   * @returns Current point balance or null if user not found
   */
  async getUserPointBalance(userId: number): Promise<number | null> {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
        select: ['pointsBalance'],
      });

      return user?.pointsBalance || null;
    } catch (error) {
      this.logger.error(
        `Failed to get point balance for user ${userId}:`,
        error,
      );
      return null;
    }
  }

  /**
   * Check if user has sufficient balance for estimated cost
   * Utility method for pre-execution validation
   *
   * @param userId - User ID to check balance for
   * @param estimatedCost - Estimated cost in points
   * @returns True if user has sufficient balance
   */
  async hasSufficientBalance(
    userId: number,
    estimatedCost: number,
  ): Promise<boolean> {
    try {
      const currentBalance = await this.getUserPointBalance(userId);
      return currentBalance !== null && currentBalance >= estimatedCost;
    } catch (error) {
      this.logger.error(`Failed to check balance for user ${userId}:`, error);
      return false;
    }
  }

  /**
   * Process token usage data and create individual spending history records
   *
   * @param runId - The run ID for logging context
   * @param tokenUsageCollector - Collector with detailed usage data
   * @param user - User context (only userId will create records)
   * @param platform - Platform where the spending occurred
   */
  async processSpendingHistory(
    runId: string,
    tokenUsageCollector: TokenUsageCollector,
    user: { userId?: number; employeeId?: number },
    platform: Platform,
  ): Promise<void> {
    // Only create spending records for users, not employees
    if (!user.userId) {
      this.logger.debug(`Skipping spending history for employee run ${runId}`, {
        runId,
        employeeId: user.employeeId,
      });
      return;
    }

    const detailedUsages = tokenUsageCollector.getUsages();
    const spendingType = this.mapPlatformToSpendingType(platform);

    this.logger.debug(`Processing spending history for user ${user.userId}`, {
      runId,
      userId: user.userId,
      platform,
      spendingType,
      usageCount: detailedUsages.length,
    });

    try {
      // Create individual spending records for each agent usage
      const spendingRecords = await this.createSpendingRecords(
        detailedUsages,
        user.userId,
        spendingType,
        runId,
      );

      this.logger.debug(
        `Successfully created ${spendingRecords.length} spending records`,
        {
          runId,
          userId: user.userId,
          recordCount: spendingRecords.length,
          totalCost: spendingRecords.reduce(
            (sum, record) => sum + record.point,
            0,
          ),
        },
      );
    } catch (error) {
      this.logger.error(
        `Failed to process spending history for run ${runId}:`,
        {
          runId,
          userId: user.userId,
          error: error.message,
          stack: error.stack,
        },
      );

      // Don't throw - spending history failure shouldn't fail the run
      // The main token usage and billing have already been processed
      this.logger.warn(
        `Continuing despite spending history failure for run ${runId}`,
        {
          runId,
          userId: user.userId,
        },
      );
    }
  }

  /**
   * Create individual spending records from detailed usage data
   *
   * @param detailedUsages - Individual usage records from token collector
   * @param userId - User ID to associate with spending
   * @param spendingType - Type of spending based on platform
   * @param runId - Run ID for logging context
   * @returns Array of created spending records
   */
  async createSpendingRecords(
    detailedUsages: DetailedUsage[],
    userId: number,
    spendingType: AssistantSpendingType,
    runId: string,
  ): Promise<AssistantSpendingHistory[]> {
    const spendingRecords: AssistantSpendingHistory[] = [];

    for (const usage of detailedUsages) {
      // Skip zero-cost usages to avoid cluttering the history
      if (usage.pointCost <= 0) {
        continue;
      }

      const spendingRecord = this.spendingHistoryRepository.create({
        agentId: usage.agentId,
        point: Math.round(usage.pointCost), // pointCost is already in points
        type: spendingType,
        modelId: usage.model,
        userId: userId,
        createdAt: Date.now(),
      });

      spendingRecords.push(spendingRecord);
    }

    // Batch insert all records for efficiency
    if (spendingRecords.length > 0) {
      const savedRecords =
        await this.spendingHistoryRepository.save(spendingRecords);

      this.logger.debug(
        `Batch inserted ${savedRecords.length} spending records`,
        {
          runId,
          userId,
          recordIds: savedRecords.map((r) => r.id),
        },
      );

      return savedRecords;
    }

    return [];
  }

  /**
   * Map platform enum to assistant spending type
   *
   * @param platform - Platform where the spending occurred
   * @returns Corresponding assistant spending type
   */
  private mapPlatformToSpendingType(platform: Platform): AssistantSpendingType {
    const platformMapping: Record<Platform, AssistantSpendingType> = {
      [Platform.IN_APP]: AssistantSpendingType.IN_APP,
      [Platform.MESSENGER]: AssistantSpendingType.MESSENGER,
      [Platform.WEBSITE]: AssistantSpendingType.WEBSITE,
      [Platform.ZALO]: AssistantSpendingType.ZALO,
    };

    return platformMapping[platform] || AssistantSpendingType.IN_APP;
  }

  /**
   * Get spending history for a specific user
   * Utility method for analytics and reporting
   *
   * @param userId - User ID to get spending history for
   * @param limit - Maximum number of records to return
   * @returns Array of spending history records
   */
  async getUserSpendingHistory(
    userId: number,
    limit: number = 100,
  ): Promise<AssistantSpendingHistory[]> {
    try {
      return await this.spendingHistoryRepository.find({
        where: { userId },
        order: { createdAt: 'DESC' },
        take: limit,
      });
    } catch (error) {
      this.logger.error(
        `Failed to get spending history for user ${userId}:`,
        error,
      );
      return [];
    }
  }

  /**
   * Get total spending for a user within a time range
   * Utility method for spending analytics
   *
   * @param userId - User ID to calculate spending for
   * @param startTime - Start time (Unix timestamp in milliseconds)
   * @param endTime - End time (Unix timestamp in milliseconds)
   * @returns Total points spent in the time range
   */
  async getUserTotalSpending(
    userId: number,
    startTime: number,
    endTime: number,
  ): Promise<number> {
    try {
      const result = await this.spendingHistoryRepository
        .createQueryBuilder('spending')
        .select('SUM(spending.point)', 'totalSpending')
        .where('spending.userId = :userId', { userId })
        .andWhere('spending.createdAt >= :startTime', { startTime })
        .andWhere('spending.createdAt <= :endTime', { endTime })
        .getRawOne();

      return parseInt(result?.totalSpending || '0', 10);
    } catch (error) {
      this.logger.error(
        `Failed to calculate total spending for user ${userId}:`,
        error,
      );
      return 0;
    }
  }

  /**
   * Get spending breakdown by agent for a user
   * Utility method for agent usage analytics
   *
   * @param userId - User ID to get breakdown for
   * @param startTime - Start time (Unix timestamp in milliseconds)
   * @param endTime - End time (Unix timestamp in milliseconds)
   * @returns Array of agent spending breakdowns
   */
  async getUserSpendingByAgent(
    userId: number,
    startTime: number,
    endTime: number,
  ): Promise<
    Array<{ agentId: string; totalSpending: number; usageCount: number }>
  > {
    try {
      const results = await this.spendingHistoryRepository
        .createQueryBuilder('spending')
        .select([
          'spending.agentId as agentId',
          'SUM(spending.point) as totalSpending',
          'COUNT(*) as usageCount',
        ])
        .where('spending.userId = :userId', { userId })
        .andWhere('spending.createdAt >= :startTime', { startTime })
        .andWhere('spending.createdAt <= :endTime', { endTime })
        .groupBy('spending.agentId')
        .orderBy('totalSpending', 'DESC')
        .getRawMany();

      return results.map((result) => ({
        agentId: result.agentId,
        totalSpending: parseInt(result.totalSpending, 10),
        usageCount: parseInt(result.usageCount, 10),
      }));
    } catch (error) {
      this.logger.error(
        `Failed to get spending breakdown by agent for user ${userId}:`,
        error,
      );
      return [];
    }
  }
}
