/**
 * @file Enhanced Connection Interfaces for Conditional Routing
 * 
 * Định nghĩa interfaces và types để hỗ trợ conditional routing
 * trong workflow engine, đặc biệt cho IF nodes và SWITCH nodes.
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

import { Connection } from '../entities/connection.entity';
import { DetailedNodeExecutionResult } from '../services/xstate/types';

// =================================================================
// SECTION 1: CONDITIONAL CONNECTION TYPES
// =================================================================

/**
 * Enum định nghĩa các loại conditional nodes
 */
export enum EConditionalNodeType {
  IF_CONDITION = 'IF_CONDITION',
  SWITCH = 'SWITCH',
  LOOP = 'LOOP',
  WAIT = 'WAIT',
  HTTP_REQUEST = 'HTTP_REQUEST',
  NORMAL = 'NORMAL'
}

/**
 * Enum định nghĩa activation rules cho connections
 */
export enum EConnectionActivationRule {
  ALWAYS = 'ALWAYS',                    // Luôn activate (normal connections)
  ON_TRUE = 'ON_TRUE',                  // Activate khi condition = true
  ON_FALSE = 'ON_FALSE',                // Activate khi condition = false
  ON_VALUE_MATCH = 'ON_VALUE_MATCH',    // Activate khi value match (SWITCH)
  ON_DEFAULT = 'ON_DEFAULT',            // Activate khi không có match nào (SWITCH default)
  ON_CONTINUE = 'ON_CONTINUE',          // Activate khi loop continues (LOOP)
  ON_BREAK = 'ON_BREAK',                // Activate khi loop breaks (LOOP)
  ON_COMPLETE = 'ON_COMPLETE',          // Activate khi loop completes normally (LOOP)
  ON_TIMEOUT = 'ON_TIMEOUT',            // Activate khi wait timeout (WAIT)
  ON_SUCCESS = 'ON_SUCCESS',            // Activate khi wait completes successfully (WAIT/HTTP)
  ON_SKIP = 'ON_SKIP',                  // Activate khi wait is skipped (WAIT)
  ON_ERROR = 'ON_ERROR',                // Activate khi có lỗi (HTTP_REQUEST)
  ON_STATUS_CODE = 'ON_STATUS_CODE'     // Activate khi status code match (HTTP_REQUEST)
}

/**
 * Interface cho conditional metadata của connection
 */
export interface IConditionalConnectionMetadata {
  /** Loại conditional node */
  nodeType: EConditionalNodeType;
  
  /** Rule để activate connection */
  activationRule: EConnectionActivationRule;
  
  /** Expected value để activate (cho SWITCH nodes) */
  expectedValue?: any;
  
  /** Có phải default path không (cho SWITCH nodes) */
  isDefaultPath?: boolean;
  
  /** Source handle mapping */
  sourceHandleMapping: {
    handle: string;
    condition: any;
  };
}

/**
 * Enhanced Connection với conditional metadata
 */
export interface IEnhancedConnection extends Connection {
  /** Có phải conditional connection không */
  isConditional: boolean;
  
  /** Conditional metadata */
  conditionalMetadata?: IConditionalConnectionMetadata;
  
  /** Có active không (runtime state) */
  isActive?: boolean;
}

// =================================================================
// SECTION 2: CONDITIONAL DEPENDENCY INTERFACES
// =================================================================

/**
 * Interface cho conditional dependency
 */
export interface IConditionalDependency {
  /** Source node ID */
  sourceNodeId: string;
  
  /** Target node ID */
  targetNodeId: string;
  
  /** Connection reference */
  connection: IEnhancedConnection;
  
  /** Activation condition */
  activationCondition: {
    sourceHandle: string;
    rule: EConnectionActivationRule;
    expectedValue?: any;
  };
}

/**
 * Enhanced Dependency Graph với conditional support
 */
export interface IConditionalDependencyGraph {
  /** Basic dependencies (non-conditional) */
  basicDependencies: Map<string, string[]>;
  
  /** Conditional dependencies */
  conditionalDependencies: Map<string, IConditionalDependency[]>;
  
  /** All connections mapped by source node */
  connectionsBySource: Map<string, IEnhancedConnection[]>;
  
  /** All connections mapped by target node */
  connectionsByTarget: Map<string, IEnhancedConnection[]>;
}

// =================================================================
// SECTION 3: ACTIVATION RESULT INTERFACES
// =================================================================

/**
 * Result của connection activation check
 */
export interface IConnectionActivationResult {
  /** Connection có được activate không */
  shouldActivate: boolean;
  
  /** Lý do activation/deactivation */
  reason: string;
  
  /** Source node result được sử dụng */
  sourceNodeResult?: DetailedNodeExecutionResult;
  
  /** Matched condition (nếu có) */
  matchedCondition?: any;
}

/**
 * Result của node readiness check
 */
export interface INodeReadinessResult {
  /** Node có ready để execute không */
  isReady: boolean;
  
  /** Dependencies đã satisfied */
  satisfiedDependencies: string[];
  
  /** Dependencies chưa satisfied */
  unsatisfiedDependencies: string[];
  
  /** Conditional dependencies status */
  conditionalDependenciesStatus: {
    nodeId: string;
    isActive: boolean;
    reason: string;
  }[];
}

// =================================================================
// SECTION 4: UTILITY TYPES
// =================================================================

/**
 * Mapping từ node types đến source handles
 */
export const NODE_TYPE_HANDLE_MAPPING = {
  [EConditionalNodeType.IF_CONDITION]: {
    true: 'true',
    false: 'false'
  },
  [EConditionalNodeType.SWITCH]: {
    // Dynamic based on switch cases
  },
  [EConditionalNodeType.NORMAL]: {
    main: 'main',
    error: 'error'
  }
} as const;

/**
 * Helper type để extract handle types
 */
export type NodeHandleType<T extends EConditionalNodeType> = 
  T extends EConditionalNodeType.IF_CONDITION ? 'true' | 'false' :
  T extends EConditionalNodeType.NORMAL ? 'main' | 'error' :
  string;

// =================================================================
// SECTION 5: VALIDATION INTERFACES
// =================================================================

/**
 * Validation result cho conditional connections
 */
export interface IConditionalConnectionValidation {
  /** Validation có pass không */
  isValid: boolean;
  
  /** Errors nếu có */
  errors: string[];
  
  /** Warnings nếu có */
  warnings: string[];
  
  /** Suggestions để fix */
  suggestions: string[];
}

/**
 * Configuration cho conditional routing
 */
export interface IConditionalRoutingConfig {
  /** Enable conditional routing */
  enabled: boolean;
  
  /** Strict mode - throw errors on invalid conditions */
  strictMode: boolean;
  
  /** Default behavior khi condition không match */
  defaultBehavior: 'skip' | 'error' | 'continue';
  
  /** Logging level cho conditional routing */
  logLevel: 'debug' | 'info' | 'warn' | 'error';
}
