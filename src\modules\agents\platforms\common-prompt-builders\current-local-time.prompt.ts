/**
 * Current Local Time Prompt Builder
 * Generates XML representation of current date and time in user's timezone
 * Provides temporal context for time-aware agent responses
 */
export function buildCurrentLocalTimePrompt(timezone?: string): string {
  // If no timezone provided, return empty (don't assume UTC)
  if (!timezone) {
    return '';
  }

  try {
    const now = new Date();

    // Use the user's actual timezone (IANA format like "Asia/Ho_Chi_Minh")
    const formatter = new Intl.DateTimeFormat('en-US', {
      timeZone: timezone,
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      timeZoneName: 'short'
    });

    const formattedTime = formatter.format(now);

    // Also provide ISO format for precise timestamp
    const isoTime = now.toISOString();

    return `<current-local-time timezone="${timezone}" iso="${isoTime}">${formattedTime}</current-local-time>`;

  } catch (error) {
    // If timezone is invalid, try to fallback gracefully
    console.warn(`Invalid timezone ${timezone}, skipping current time:`, error);
    return '';
  }
}
