import { HttpModule } from '@nestjs/axios';
import { BullModule } from '@nestjs/bullmq';
import { Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { TypeOrmModule } from '@nestjs/typeorm';
import { QueueName } from '../../queue/queue-name.enum';
import { AdminWorkflowController } from './controllers/admin-workflow.controller';
import { StandaloneNodeExecutionController } from './controllers/standalone-node-execution.controller';
import { UserWorkflowController } from './controllers/user-workflow.controller';
import {
  Connection,
  Execution,
  ExecutionNodeData,
  Node,
  NodeDefinition,
  WebhookRegistry,
  Workflow,
} from './entities';
import { AdminWorkflowProcessor, UserWorkflowProcessor } from './processors';
import {
  ConnectionRepository,
  ExecutionNodeDataRepository,
  ExecutionRepository,
  NodeDefinitionRepository,
  NodeRepository,
  WebhookRegistryRepository,
  WorkflowRepository,
} from './repositories';
import { AdminWorkflowService } from './service/admin-workflow.service';
import { UserWorkflowService } from './service/user-workflow.service';
import { ExecutorRegistrationService } from './services/executor-registration.service';
import { NodeExecutionService } from './services/node-execution.service';
import { RedisPublisherService } from './services/redis-publisher.service';
import { WorkflowContextBuilderService } from './services/workflow-context-builder.service';
import { WorkflowContextManagerService } from './services/workflow-context-manager.service';
import { WorkflowContextModule } from './services/workflow-context.module';
import { WorkflowContextService } from './services/workflow-context.service';
import { WorkflowTemplateProcessorService } from './services/workflow-template-processor.service';
import { ALL_NODE_EXECUTORS, ExecutorRegistry, NodeExecutorFactory } from './services/xstate/executors/node-executor.factory';
import { ConditionEvaluatorService, FieldMapperService, HttpClientService } from './services/xstate/executors/shared';
import { MachineIntegrationService } from './services/xstate/machines/machine-integration.service';
import { MachineServicesProvider } from './services/xstate/machines/machine-services';
import { AgentNodeDetectorService } from './services/xstate/services/agent-node-detector.service';
import { ConditionalConnectionService } from './services/xstate/services/conditional-connection.service';
import { DependencyResolverService } from './services/xstate/services/dependency-resolver.service';
import { LangGraphIntegrationService } from './services/xstate/services/langgraph-integration.service';
import { WorkflowStateManagerService } from './services/xstate/services/workflow-state-manager.service';
import { WorkflowXStateService } from './services/xstate/services/workflow-xstate.service';


/**
 * Workflow Module
 * Chứa tất cả entities, repositories và services cho workflow system
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      Workflow,
      Node,
      Connection,
      Execution,
      ExecutionNodeData,
      NodeDefinition,
      WebhookRegistry,
    ]),
    BullModule.registerQueue({
      name: QueueName.WORKFLOW_EXECUTION,
    }),
    HttpModule.register({
      timeout: 10000,
      maxRedirects: 5,
    }),
    ClientsModule.register([
      {
        name: 'REDIS_CLIENT',
        transport: Transport.REDIS,
        options: {
          host: process.env.REDIS_HOST || 'localhost',
          port: parseInt(process.env.REDIS_PORT || '6379'),
          password: process.env.REDIS_PASSWORD,
          db: parseInt(process.env.REDIS_DB || '0'),
          retryAttempts: 5,
          retryDelay: 3000,
        },
      },
    ]),
    EventEmitterModule,
    WorkflowContextModule
  ],
  controllers: [
    UserWorkflowController,
    AdminWorkflowController,
    StandaloneNodeExecutionController,
  ],
  providers: [
    WorkflowRepository,
    NodeRepository,
    ConnectionRepository,
    ExecutionRepository,
    ExecutionNodeDataRepository,
    NodeDefinitionRepository,
    WebhookRegistryRepository,
    UserWorkflowService,
    AdminWorkflowService,
    NodeExecutionService,
    AdminWorkflowProcessor,
    UserWorkflowProcessor,
    DependencyResolverService,
    ConditionalConnectionService,
    WorkflowStateManagerService,
    ExecutorRegistry,
    NodeExecutorFactory,
    // Register shared services for executors
    HttpClientService,
    ConditionEvaluatorService,
    FieldMapperService,
    // Register all node executors
    ...ALL_NODE_EXECUTORS,
    AgentNodeDetectorService,
    LangGraphIntegrationService,
    MachineServicesProvider,
    WorkflowXStateService,
    MachineIntegrationService,
    RedisPublisherService,
    WorkflowContextService,
    WorkflowContextBuilderService,
    WorkflowContextManagerService,
    WorkflowTemplateProcessorService,
    ExecutorRegistrationService,
  ],
  exports: [
    TypeOrmModule,
    WorkflowRepository,
    NodeRepository,
    ConnectionRepository,
    ExecutionRepository,
    ExecutionNodeDataRepository,
    NodeDefinitionRepository,
    WebhookRegistryRepository,
  ],
})
export class WorkflowModule { }