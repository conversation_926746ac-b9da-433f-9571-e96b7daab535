import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from 'src/infra/redis';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ExternalConversationMessage } from '../../../domains/external/entities/external-conversation-message.entity';
import { WebsiteJobData } from '../interfaces/website-job-data.interface';
import { StreamEvent as ExternalStreamEvent } from '../../../shared/interfaces/stream-event.interface';
import { ExclusiveTags, MessageRole, Platform, StreamEventType } from '../../../shared/enums';
import { StreamEvent } from '@langchain/core/tracers/log_stream';

/**
 * Website Event Processor Service
 *
 * Handles dynamic message creation during PlannerExecutorGraph execution.
 * Transforms LangGraph events into Redis stream events for real-time frontend consumption.
 *
 * Key Features:
 * - Website visitor conversation support (ExternalConversationMessage)
 * - Executor agent message creation (planner is internal)
 * - Event transformation from LangGraph to StreamEventType
 * - Real-time Redis streaming for website chat widgets
 * - No tool call interrupts (PlannerExecutorGraph doesn't need approvals)
 */
@Injectable()
export class WebsiteEventProcessorService {
  private readonly logger = new Logger(WebsiteEventProcessorService.name);

  /**
   * Function map for LangGraph event handlers
   * Simpler than in-app due to no interrupt handling needed
   */
  private readonly eventHandlers = new Map<
    string,
    (
      langGraphEvent: StreamEvent,
      jobData: WebsiteJobData,
      streamKey: string,
      currentMessageId: string | null,
    ) => Promise<{ newMessageId?: string | null; messageUpdated?: boolean }>
  >([
    ['on_chat_model_start', this.handleChatModelStart.bind(this)],
    ['on_chat_model_stream', this.handleChatModelStream.bind(this)],
    ['on_chat_model_end', this.handleChatModelEnd.bind(this)],
    ['on_tool_start', this.handleToolStart.bind(this)],
    ['on_tool_end', this.handleToolEnd.bind(this)],
    // Run lifecycle events
    [StreamEventType.RUN_STARTED, this.handleRunStarted.bind(this)],
    [StreamEventType.RUN_COMPLETE, this.handleRunComplete.bind(this)],
    [StreamEventType.RUN_CANCELLED, this.handleRunCancelled.bind(this)],
    [StreamEventType.RUN_ERROR, this.handleRunError.bind(this)],
  ]);

  constructor(
    private readonly redisService: RedisService,
    @InjectRepository(ExternalConversationMessage)
    private readonly messageRepository: Repository<ExternalConversationMessage>,
  ) {}

  /**
   * Process LangGraph events and transform them into Redis stream events
   * Handles message creation dynamically during streaming for website visitors
   *
   * @param langGraphEvent - Raw LangGraph event
   * @param jobData - Website job data with visitor context
   * @param streamKey - Redis stream key for event publishing
   * @param currentMessageId - Current message ID (null if no active message)
   * @returns Object with newMessageId if a new message was created
   */
  async processLangGraphEvent(
    langGraphEvent: StreamEvent | Record<string, unknown>,
    jobData: WebsiteJobData,
    streamKey: string,
    currentMessageId: string | null,
  ): Promise<{ newMessageId?: string | null; messageUpdated?: boolean }> {
    const eventType = (langGraphEvent as Record<string, unknown>).event as string;

    this.logger.debug(`Processing LangGraph event: ${eventType}`, {
      eventType,
      currentMessageId,
      streamKey,
      runId: jobData.runId,
      threadId: jobData.threadId,
      visitorId: jobData.humanInfo.websiteVisitor.id,
    });

    try {
      // Use function map for cleaner event handling
      const handler = this.eventHandlers.get(eventType);

      if (handler) {
        return await handler(
          langGraphEvent as StreamEvent,
          jobData,
          streamKey,
          currentMessageId,
        );
      } else {
        this.logger.debug(`Unhandled LangGraph event type: ${eventType}`);
        return {};
      }
    } catch (error) {
      this.logger.error(
        `Failed to process LangGraph event ${eventType}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Handle on_chat_model_start events
   * Creates new ExternalConversationMessage for executor agent only
   */
  private async handleChatModelStart(
    langGraphEvent: StreamEvent,
    jobData: WebsiteJobData,
    streamKey: string,
    _currentMessageId: string | null,
  ): Promise<{ newMessageId?: string | null; messageUpdated?: boolean }> {
    // Extract agentId from tags to determine which agent is starting
    const agentTag = langGraphEvent.tags?.find((t) => t.startsWith('agent:'));
    if (!agentTag) {
      this.logger.debug('No agent tag found in chat model start event');
      return {};
    }

    // Parse tag format: agent:agent_id:agent_TYPE
    const tagParts = agentTag.split(':');
    if (tagParts.length < 2) {
      this.logger.warn('Invalid agent tag format in chat model start', {
        agentTag,
      });
      return {};
    }

    const eventAgentId = tagParts[1];

    // Only create messages for executor agent (mainAgentId)
    // Planner agent is internal and doesn't create visible messages
    if (eventAgentId !== jobData.mainAgentId) {
      this.logger.debug(`Skipping message creation for planner agent: ${eventAgentId}`);
      return {};
    }

    // Create new message for executor agent only
    const newMessageId = await this.createNewMessage(jobData);

    // Emit TEXT_MESSAGE_START event
    await this.publishEvent(streamKey, {
      type: StreamEventType.TEXT_MESSAGE_START,
      timestamp: Date.now(),
      messageId: newMessageId,
      isFromSubprocess:
        langGraphEvent.tags?.includes(ExclusiveTags.SUBPROCESS) || false,
    });

    this.logger.log(`New message created: ${newMessageId} for executor agent: ${eventAgentId}`);

    this.logger.debug(
      `Created new message ${newMessageId} and emitted TEXT_MESSAGE_START for executor agent`,
    );
    return { newMessageId };
  }

  /**
   * Handle on_chat_model_stream events
   * Streams text content and tool call arguments to existing messages
   */
  private async handleChatModelStream(
    langGraphEvent: StreamEvent,
    _jobData: WebsiteJobData,
    streamKey: string,
    currentMessageId: string | null,
  ): Promise<{ newMessageId?: string | null; messageUpdated?: boolean }> {
    // Stream content to existing message (message should already exist from on_chat_model_start)
    if (!currentMessageId) {
      this.logger.warn(
        'Received on_chat_model_stream but no current message ID - message should have been created in on_chat_model_start',
      );
      return {};
    }

    const textDelta = langGraphEvent.data?.chunk?.content;
    const toolCallArgsDelta = langGraphEvent.data?.chunk?.tool_call_chunks;

    if (textDelta) {
      await this.publishEvent(streamKey, {
        type: StreamEventType.TEXT_MESSAGE_CONTENT,
        timestamp: Date.now(),
        messageId: currentMessageId,
        delta: textDelta,
        isFromSubprocess:
          langGraphEvent.tags?.includes(ExclusiveTags.SUBPROCESS) || false,
      });

      this.logger.debug(
        `Streamed content to message ${currentMessageId}: "${textDelta.substring(0, 50)}${textDelta.length > 50 ? '...' : ''}"`,
      );
    } else if (toolCallArgsDelta?.length > 0) {
      await this.publishEvent(streamKey, {
        type: StreamEventType.TOOL_CALL_ARGS,
        timestamp: Date.now(),
        isFromSubprocess:
          langGraphEvent.tags?.includes(ExclusiveTags.SUBPROCESS) || false,
      });
    }

    return {};
  }

  /**
   * Handle on_chat_model_end events
   * Finalizes message with complete content and emits completion event
   */
  private async handleChatModelEnd(
    langGraphEvent: StreamEvent,
    _jobData: WebsiteJobData,
    streamKey: string,
    currentMessageId: string | null,
  ): Promise<{ newMessageId?: string | null; messageUpdated?: boolean }> {
    if (!currentMessageId) {
      this.logger.warn('Received on_chat_model_end but no current message ID');
      return {};
    }

    // Get final content from LangGraph event
    const finalContent = langGraphEvent.data?.output?.content || '';

    // Update message in database with final content
    await this.messageRepository.update(currentMessageId, {
      text: finalContent,
    });

    // Emit TEXT_MESSAGE_END event
    await this.publishEvent(streamKey, {
      type: StreamEventType.TEXT_MESSAGE_END,
      timestamp: Date.now(),
      messageId: currentMessageId,
      finalContent: finalContent,
      isFromSubprocess:
        langGraphEvent.tags?.includes(ExclusiveTags.SUBPROCESS) || false,
    });

    this.logger.debug(
      `Finalized message ${currentMessageId} and emitted TEXT_MESSAGE_END`,
    );

    // Reset for next message
    return { newMessageId: null, messageUpdated: true };
  }

  /**
   * Handle on_tool_start events
   * Emits tool execution start event with tool information
   */
  private async handleToolStart(
    langGraphEvent: StreamEvent,
    _jobData: WebsiteJobData,
    streamKey: string,
    _parentMessageId: string | null,
  ): Promise<{ newMessageId?: string | null; messageUpdated?: boolean }> {
    // Extract tool data from LangGraph event
    const toolName = langGraphEvent.name || 'unknown_tool';
    const toolArgs = langGraphEvent.data?.input || {};
    const toolId = langGraphEvent.run_id; // Use run_id for correlation

    await this.publishEvent(streamKey, {
      type: StreamEventType.TOOL_CALL_START,
      timestamp: Date.now(),
      toolName: toolName,
      toolId: toolId,
      toolArgs: toolArgs,
      isFromSubprocess:
        langGraphEvent.tags?.includes(ExclusiveTags.SUBPROCESS) || false,
    });

    this.logger.debug(
      `Emitted TOOL_CALL_START for tool: ${toolName} (${toolId})`,
    );
    return {};
  }

  /**
   * Handle on_tool_end events
   * Emits tool execution completion event with results
   */
  private async handleToolEnd(
    langGraphEvent: StreamEvent,
    _jobData: WebsiteJobData,
    streamKey: string,
    _parentMessageId: string | null,
  ): Promise<{ newMessageId?: string | null; messageUpdated?: boolean }> {
    // Extract tool data from LangGraph event
    const toolName = langGraphEvent.name || 'unknown_tool';
    const toolResult = langGraphEvent.data?.output || {};
    const toolId = langGraphEvent.run_id; // Use run_id for correlation

    await this.publishEvent(streamKey, {
      type: StreamEventType.TOOL_CALL_END,
      timestamp: Date.now(),
      toolName: toolName,
      toolId: toolId,
      toolResult: toolResult,
      isFromSubprocess:
        langGraphEvent.tags?.includes(ExclusiveTags.SUBPROCESS) || false,
    });

    this.logger.debug(
      `Emitted TOOL_CALL_END for tool: ${toolName} (${toolId})`,
    );
    return {};
  }

  /**
   * Handle run_started events
   * Emits RUN_STARTED event when execution begins
   */
  private async handleRunStarted(
    langGraphEvent: StreamEvent,
    jobData: WebsiteJobData,
    streamKey: string,
    _currentMessageId: string | null,
  ): Promise<{ newMessageId?: string | null; messageUpdated?: boolean }> {
    await this.publishEvent(streamKey, {
      type: StreamEventType.RUN_STARTED,
      timestamp: Date.now(),
      runId: jobData.runId,
      isFromSubprocess:
        langGraphEvent.tags?.includes(ExclusiveTags.SUBPROCESS) || false,
    });

    this.logger.debug(`Emitted RUN_STARTED event for run: ${jobData.runId}`);
    return {};
  }

  /**
   * Handle run_complete events
   * Emits RUN_COMPLETE event when run completes successfully
   */
  private async handleRunComplete(
    langGraphEvent: Record<string, unknown>,
    jobData: WebsiteJobData,
    streamKey: string,
    _currentMessageId: string | null,
  ): Promise<{ newMessageId?: string | null; messageUpdated?: boolean }> {
    await this.publishEvent(streamKey, {
      type: StreamEventType.RUN_COMPLETE,
      timestamp: Date.now(),
      totalCost: (langGraphEvent.totalCost as number) || 0,
      isFromSubprocess: false, // Run events are not from subprocess
    });

    this.logger.debug(`Emitted RUN_COMPLETE event for run: ${jobData.runId}`);
    return {};
  }

  /**
   * Handle run_cancelled events
   * Emits RUN_CANCELLED event when run is cancelled
   */
  private async handleRunCancelled(
    langGraphEvent: Record<string, unknown>,
    jobData: WebsiteJobData,
    streamKey: string,
    _currentMessageId: string | null,
  ): Promise<{ newMessageId?: string | null; messageUpdated?: boolean }> {
    await this.publishEvent(streamKey, {
      type: StreamEventType.RUN_CANCELLED,
      timestamp: Date.now(),
      reason: langGraphEvent.reason as string,
      initiatedBy: 'system',
      isFromSubprocess: false, // Run events are not from subprocess
    });

    this.logger.debug(`Emitted RUN_CANCELLED event for run: ${jobData.runId}`);
    return {};
  }

  /**
   * Handle run_error events
   * Emits RUN_ERROR event when run encounters errors
   */
  private async handleRunError(
    langGraphEvent: Record<string, unknown>,
    jobData: WebsiteJobData,
    streamKey: string,
    _currentMessageId: string | null,
  ): Promise<{ newMessageId?: string | null; messageUpdated?: boolean }> {
    const errorInfo = langGraphEvent.error as Record<string, unknown> | undefined;

    await this.publishEvent(streamKey, {
      type: StreamEventType.RUN_ERROR,
      timestamp: Date.now(),
      error: {
        type: errorInfo?.type as string || 'Error',
        message: errorInfo?.message as string || 'Unknown error',
        code: errorInfo?.code as string,
        stack: errorInfo?.stack as string,
      },
      isFromSubprocess: false, // Run events are not from subprocess
    });

    this.logger.debug(`Emitted RUN_ERROR event for run: ${jobData.runId}`);
    return {};
  }

  /**
   * Create new ExternalConversationMessage for website visitor
   * Uses website-specific context and visitor information
   *
   * @param jobData - Website job data with visitor context
   * @returns The created message ID
   */
  private async createNewMessage(jobData: WebsiteJobData): Promise<string> {
    const message = this.messageRepository.create({
      text: '',
      externalCustomerPlatformDataId: jobData.humanInfo.websiteVisitor.externalPlatformId,
      role: MessageRole.ASSISTANT,
      platform: Platform.WEBSITE,
      createdAt: `${Date.now()}`,
      processed: true,
      agentId: jobData.mainAgentId,
    });

    const savedMessage = await this.messageRepository.save(message);
    const messageId = savedMessage.id;

    this.logger.debug(
      `Created new assistant message: ${messageId} for run: ${jobData.runId} in thread: ${jobData.threadId} for visitor: ${jobData.humanInfo.websiteVisitor.id}`,
    );
    return messageId;
  }

  /**
   * Publish event to Redis stream with error handling
   * Uses infrastructure RedisService directly for stream operations
   *
   * @param streamKey - Redis stream key
   * @param event - Stream event to publish
   */
  async publishEvent(
    streamKey: string,
    event: ExternalStreamEvent,
  ): Promise<void> {
    try {
      // Use RedisService.xadd to publish event to stream
      await this.redisService.xadd(streamKey, {
        event: JSON.stringify(event),
      });

      this.logger.debug(`Published event ${event.type} to stream ${streamKey}`);
    } catch (streamingError) {
      this.logger.warn(
        `Redis streaming failed for event ${event.type}:`,
        streamingError,
      );
    }
  }
}