import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Job } from 'bullmq';
import { QueueName, ZaloZnsJobName } from '../../../queue';
import {
  ZaloZnsSingleJobData,
  ZaloZnsCampaignJobData,
  ZaloZnsBatchJobData,
} from './interfaces';
import { ZaloPromotionService } from '../../../shared/services/zalo/zalo-promotion.service';
import { ZaloZnsService } from '../../../shared/services/zalo/zalo-zns.service';
import { ZaloService } from '../../../shared/services/zalo/zalo.service';
import { CampaignStatusUpdateService } from '../../../shared/services/campaign-status-update.service';
import { ZaloOATokenAdapterService } from '../../../shared/services/zalo/zalo-oa-token-adapter.service';
import { ZaloZnsMessage } from '../entities/zalo-zns-message.entity';

/**
 * Processor xử lý queue Zalo ZNS
 * Xử lý các loại job:
 * - SEND_ZNS: Gửi ZNS đơn lẻ
 * - SEND_ZNS_CAMPAIGN: Gửi ZNS theo chiến dịch
 * - SEND_BATCH_ZNS: Gửi batch ZNS
 */
@Injectable()
@Processor(QueueName.ZALO_ZNS, { concurrency: 5 })
export class ZaloZnsProcessor extends WorkerHost {
  private readonly logger = new Logger(ZaloZnsProcessor.name);

  constructor(
    private readonly zaloPromotionService: ZaloPromotionService,
    private readonly zaloZnsService: ZaloZnsService,
    private readonly zaloService: ZaloService,
    private readonly campaignStatusUpdateService: CampaignStatusUpdateService,
    private readonly zaloOATokenAdapterService: ZaloOATokenAdapterService,
    @InjectRepository(ZaloZnsMessage)
    private readonly zaloZnsMessageRepository: Repository<ZaloZnsMessage>,
  ) {
    super();
    this.logger.log(
      '🚀 ZaloZnsProcessor initialized and ready to process jobs',
    );
    this.logger.log(
      `📋 Registered for queue: ${QueueName.ZALO_ZNS} with concurrency: 5`,
    );
    this.logger.log(
      `🎯 Supported job types: ${Object.values(ZaloZnsJobName).join(', ')}`,
    );
  }

  /**
   * Xử lý job từ queue
   */
  async process(
    job: Job<
      ZaloZnsSingleJobData | ZaloZnsCampaignJobData | ZaloZnsBatchJobData
    >,
  ): Promise<void> {
    this.logger.log(`🎯 Processing job ${job.name} with ID ${job.id}`);
    this.logger.debug(`📋 Job data:`, JSON.stringify(job.data, null, 2));

    try {
      switch (job.name) {
        case ZaloZnsJobName.SEND_ZNS:
          this.logger.log(`📱 Processing single ZNS job ${job.id}`);
          await this.processSingleZns(job as Job<ZaloZnsSingleJobData>);
          break;
        case ZaloZnsJobName.SEND_ZNS_CAMPAIGN:
          this.logger.log(`📢 Processing ZNS campaign job ${job.id}`);
          await this.processCampaignZns(job as Job<ZaloZnsCampaignJobData>);
          break;
        case ZaloZnsJobName.SEND_BATCH_ZNS:
          this.logger.log(`📦 Processing batch ZNS job ${job.id}`);
          await this.processBatchZns(job as Job<ZaloZnsBatchJobData>);
          break;
        default:
          this.logger.error(`❌ Unknown job name: ${job.name}`);
          throw new Error(`Unknown job name: ${job.name}`);
      }

      this.logger.log(`✅ Job ${job.id} completed successfully`);
    } catch (error) {
      this.logger.error(
        `❌ Job ${job.id} failed: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Xử lý job gửi ZNS đơn lẻ
   */
  private async processSingleZns(
    job: Job<ZaloZnsSingleJobData>,
  ): Promise<void> {
    const {
      oaId,
      phone,
      templateId,
      templateData,
      messageType,
      trackingId,
      userId,
    } = job.data;

    this.logger.log(
      `Processing single ZNS for phone ${phone} with template ${templateId}`,
    );
    this.logger.debug(`Template data:`, JSON.stringify(templateData, null, 2));

    // Lấy và validate access token
    let accessToken = await this.getValidAccessToken(
      oaId,
      job.data.accessToken,
      job.data.refreshToken,
    );

    // Gửi ZNS message (tất cả loại đều gửi giống nhau)
    await this.sendZnsMessage(
      accessToken,
      phone,
      templateId,
      templateData,
      trackingId,
      userId,
      oaId,
    );
  }

  /**
   * Xử lý job gửi ZNS theo chiến dịch
   */
  private async processCampaignZns(
    job: Job<ZaloZnsCampaignJobData>,
  ): Promise<void> {
    const {
      campaignId,
      oaId,
      templateId,
      templateData,
      phoneList,
      personalizedDataMap,
      messageType,
      batchSize = 10,
      batchDelay = 1000,
      userId,
    } = job.data;

    this.logger.log(
      `🚀 Processing campaign ZNS ${campaignId} for ${phoneList.length} phones`,
    );

    let sentCount = 0;
    let failedCount = 0;

    try {
      // Lấy và validate access token
      let accessToken = await this.getValidAccessToken(
        oaId,
        job.data.accessToken,
        job.data.refreshToken,
      );

      // Chia thành các batch để gửi
      const batches = this.chunkArray(phoneList, batchSize);

      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        this.logger.log(
          `📦 Processing batch ${i + 1}/${batches.length} with ${batch.length} phones`,
        );

        // Gửi từng tin nhắn trong batch
        for (const phone of batch) {
          try {
            const trackingId = `campaign_${campaignId}_${phone}_${Date.now()}`;

            // Lấy dữ liệu cá nhân hóa cho số điện thoại này (nếu có)
            const personalizedData =
              personalizedDataMap?.[phone] || templateData;

            // Gửi ZNS message
            await this.sendZnsMessage(
              accessToken,
              phone,
              templateId,
              personalizedData,
              trackingId,
              userId,
              oaId,
            );

            sentCount++;
            this.logger.debug(`✅ Sent ZNS to ${phone} successfully`);
          } catch (error) {
            failedCount++;
            this.logger.error(
              `❌ Failed to send ZNS to ${phone}: ${error.message}`,
            );
          }
        }

        // Delay giữa các batch
        if (i < batches.length - 1 && batchDelay > 0) {
          this.logger.debug(`⏳ Waiting ${batchDelay}ms before next batch...`);
          await this.delay(batchDelay);
        }
      }

      // Cập nhật trạng thái campaign thành công
      await this.updateCampaignStatus(
        campaignId,
        'SENT',
        sentCount,
        failedCount,
      );

      this.logger.log(
        `✅ Campaign ${campaignId} completed: ${sentCount} sent, ${failedCount} failed`,
      );
    } catch (error) {
      // Cập nhật trạng thái campaign thất bại
      await this.updateCampaignStatus(
        campaignId,
        'FAILED',
        sentCount,
        failedCount,
      );

      this.logger.error(
        `❌ Campaign ${campaignId} failed: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Xử lý job gửi batch ZNS
   */
  private async processBatchZns(job: Job<ZaloZnsBatchJobData>): Promise<void> {
    const { oaId, messages, campaignId, batchIndex, totalBatches, userId } =
      job.data;

    this.logger.log(
      `Processing batch ZNS ${(batchIndex ?? 0) + 1}/${totalBatches ?? 1} with ${messages.length} messages`,
    );

    // Lấy và validate access token
    let accessToken = await this.getValidAccessToken(
      oaId,
      job.data.accessToken,
      job.data.refreshToken,
    );

    // Gửi từng tin nhắn trong batch
    for (const message of messages) {
      try {
        const { phone, templateId, templateData, messageType, trackingId } =
          message;

        // Gửi ZNS message
        await this.sendZnsMessage(
          accessToken,
          phone,
          templateId,
          templateData,
          trackingId,
          userId,
          oaId,
        );
      } catch (error) {
        this.logger.error(
          `Failed to send ZNS to ${message.phone}: ${error.message}`,
        );
        // Tiếp tục gửi cho tin nhắn khác
      }
    }
  }

  /**
   * Gửi ZNS message (method chung cho tất cả loại tin nhắn)
   */
  private async sendZnsMessage(
    accessToken: string,
    phone: string,
    templateId: string,
    templateData: Record<string, any>,
    trackingId?: string,
    userId?: number,
    oaId?: string,
  ): Promise<void> {
    this.logger.log(
      `Sending ZNS message to ${phone} with template ${templateId}`,
    );

    try {
      // Validate và bổ sung template data nếu cần
      const validatedTemplateData = this.validateAndFixTemplateData(
        templateId,
        templateData,
      );

      // Gửi ZNS message
      const result = await this.zaloZnsService.sendZnsMessage(accessToken, {
        phone,
        template_id: templateId,
        template_data: validatedTemplateData,
        tracking_id: trackingId,
      });

      this.logger.log(
        `ZNS message sent successfully. Message ID: ${result.message_id}, Tracking ID: ${trackingId}`,
      );

      // Lưu trạng thái thành công vào database
      await this.saveZnsMessageToDatabase(
        result,
        phone,
        templateId,
        validatedTemplateData,
        trackingId,
        userId,
        oaId,
      );
    } catch (error) {
      this.logger.error(
        `Failed to send ZNS message to ${phone} with template ${templateId}: ${error.message}`,
      );
      this.logger.debug(
        `Template data was:`,
        JSON.stringify(templateData, null, 2),
      );
      throw error;
    }
  }

  /**
   * Lưu thông tin ZNS message vào database sau khi gửi thành công
   */
  private async saveZnsMessageToDatabase(
    result: { message_id: string; tracking_id: string },
    phone: string,
    templateId: string,
    templateData: Record<string, any>,
    trackingId?: string,
    userId?: number,
    oaId?: string,
  ): Promise<void> {
    try {
      const now = Date.now();

      const znsMessage = this.zaloZnsMessageRepository.create({
        userId: userId || 0, // Default userId nếu không có
        oaId: oaId || '', // Default oaId nếu không có
        templateId,
        phone,
        messageId: result.message_id,
        trackingId: trackingId || result.tracking_id,
        templateData,
        status: 'delivered', // Đánh dấu là đã gửi thành công
        deliveredTime: now,
        createdAt: now,
        updatedAt: now,
      });

      await this.zaloZnsMessageRepository.save(znsMessage);

      this.logger.log(
        `✅ Saved ZNS message to database: ID ${znsMessage.id}, Message ID: ${result.message_id}`,
      );
    } catch (error) {
      this.logger.error(
        `❌ Failed to save ZNS message to database: ${error.message}`,
        error.stack,
      );
      // Không throw error để không làm fail job chính
      // Việc gửi tin nhắn đã thành công, chỉ việc lưu DB bị lỗi
    }
  }

  /**
   * Validate và fix template data
   */
  private validateAndFixTemplateData(
    templateId: string,
    templateData: Record<string, any>,
  ): Record<string, any> {
    const fixedData = { ...templateData };

    // Các template thường gặp và fix data
    if (templateId === '471725') {
      // Template OTP
      if (!fixedData.otp) {
        this.logger.warn(
          `Template ${templateId} requires 'otp' parameter, adding default value`,
        );
        fixedData.otp = '123456'; // Default OTP for testing
      }
    }

    // Thêm các template khác nếu cần
    // if (templateId === 'other_template') {
    //   if (!fixedData.required_field) {
    //     fixedData.required_field = 'default_value';
    //   }
    // }

    this.logger.debug(
      `Fixed template data for ${templateId}:`,
      JSON.stringify(fixedData, null, 2),
    );
    return fixedData;
  }

  /**
   * Lấy access token hợp lệ, refresh nếu cần thiết
   */
  private async getValidAccessToken(
    oaId: string,
    currentAccessToken: string,
    refreshToken?: string,
  ): Promise<string> {
    try {
      if (!currentAccessToken) {
        throw new Error(`Access token not provided for OA ${oaId}`);
      }

      // Tạm thời sử dụng token được cung cấp
      // Token expiry sẽ được handle khi gọi API thực tế
      this.logger.debug(`Using provided access token for OA ${oaId}`);

      // TODO: Implement proper token validation và refresh logic
      // Hiện tại return token trực tiếp để tránh overhead
      return currentAccessToken;
    } catch (error) {
      this.logger.error(
        `❌ Error getting valid access token for OA ${oaId}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Validate access token bằng cách gọi API test
   */
  private async validateAccessToken(accessToken: string): Promise<boolean> {
    try {
      // Gọi API đơn giản để test token - sử dụng API có sẵn
      // Thử gửi một ZNS test hoặc gọi API profile
      // Tạm thời return true, implement sau khi có API test phù hợp
      this.logger.debug(`Validating access token...`);

      // TODO: Implement proper token validation
      // Có thể sử dụng API get OA info hoặc API khác để test token
      return true;
    } catch (error) {
      this.logger.debug(`Access token validation failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Refresh access token
   */
  private async refreshAccessToken(
    oaId: string,
    refreshToken: string,
  ): Promise<string | null> {
    try {
      // Tìm integration trong database
      const integration = await this.zaloOATokenAdapterService.findOne({
        where: { oaId },
      });

      if (!integration) {
        this.logger.error(`Integration not found for OA ${oaId}`);
        return null;
      }

      // TODO: Implement Zalo refresh token API call
      // Hiện tại chưa có API refresh token trong ZaloService
      // Cần implement API call để refresh token từ Zalo

      this.logger.warn(
        `Refresh token API not implemented yet for OA ${oaId}. Using current token.`,
      );

      // Tạm thời return null để báo không thể refresh
      // Khi implement xong API refresh token, sẽ:
      // 1. Gọi API refresh token của Zalo
      // 2. Cập nhật token mới vào database
      // 3. Return token mới

      return null;
    } catch (error) {
      this.logger.error(
        `Failed to refresh token for OA ${oaId}: ${error.message}`,
      );
      return null;
    }
  }

  /**
   * Chuyển đổi số điện thoại thành Zalo User ID
   * Cần thiết cho tin nhắn truyền thông
   */
  private async getZaloUserIdFromPhone(
    accessToken: string,
    phone: string,
  ): Promise<string> {
    try {
      // TODO: Implement API call để lấy user ID từ phone number
      // Hiện tại sử dụng phone number làm user ID tạm thời
      this.logger.warn(
        `Using phone ${phone} as user ID. Should implement proper phone-to-userId conversion.`,
      );
      return phone;
    } catch (error) {
      this.logger.error(
        `Failed to get user ID for phone ${phone}: ${error.message}`,
      );
      throw new Error(`Cannot get Zalo user ID for phone ${phone}`);
    }
  }

  /**
   * Chia mảng thành các chunk nhỏ hơn
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * Delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Cập nhật trạng thái campaign trong database
   */
  private async updateCampaignStatus(
    campaignId: number,
    status: string,
    sentMessages: number,
    failedMessages: number,
  ): Promise<void> {
    try {
      this.logger.log(
        `📊 Updating campaign ${campaignId} status: ${status} (sent: ${sentMessages}, failed: ${failedMessages})`,
      );

      await this.campaignStatusUpdateService.updateZnsCampaignStatus(
        campaignId,
        status,
        sentMessages,
        failedMessages,
      );
    } catch (error) {
      this.logger.error(
        `❌ Failed to update campaign ${campaignId} status: ${error.message}`,
        error.stack,
      );
      // Không throw error để không làm fail job
    }
  }
}
