import { Injectable, Logger } from '@nestjs/common';
import { ZaloService } from './zalo.service';
import { ConfigService } from '@nestjs/config';
import { AppException, ErrorCode } from '@common/exceptions';

/**
 * Interface cho response của Zalo video status API
 */
interface ZaloVideoStatusResponse {
  status: number;
  status_message: string;
  convert_percent: number;
  convert_error_code: number;
  video_id?: string;
  video_name?: string;
  video_size?: number;
}

/**
 * Interface cho response của Zalo article verify API
 */
interface ZaloArticleVerifyResponse {
  id: string;
}

/**
 * Service xử lý Zalo Article API trong worker
 */
@Injectable()
export class ZaloArticleService {
  private readonly logger = new Logger(ZaloArticleService.name);
  private readonly baseApiUrl = 'https://openapi.zalo.me/v2.0';
  private readonly appApiUrl: string;

  constructor(
    private readonly zaloService: ZaloService,
    private readonly configService: ConfigService,
  ) {
    // Lấy URL của app API từ config
    this.appApiUrl = this.configService.get<string>('APP_API_URL') || 'http://localhost:3000';
  }

  /**
   * Kiểm tra trạng thái video đã upload
   * @param accessToken Access token của Official Account
   * @param token Token từ response upload video
   * @returns Thông tin trạng thái video
   */
  async checkVideoStatus(
    accessToken: string,
    token: string,
  ): Promise<ZaloVideoStatusResponse> {
    try {
      this.logger.debug(`Checking video status with token: ${token}`);

      if (!token || token.trim() === '') {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Token không được để trống',
        );
      }

      // Sử dụng cách giống app chính (query params) thay vì header
      const params = new URLSearchParams();
      params.append('token', token);

      const fullUrl = `${this.baseApiUrl}/article/upload_video/verify?${params.toString()}`;

      this.logger.debug(`Calling Zalo API: ${fullUrl}`);
      this.logger.debug(`Access token length: ${accessToken?.length || 0}`);
      this.logger.debug(`Video token: ${token.substring(0, 50)}...`);

      // Sử dụng method get thông thường như app chính
      const result = await this.zaloService.get<{
        error: number;
        message: string;
        data: ZaloVideoStatusResponse;
      }>(fullUrl, accessToken);

      // Trả về data từ response (bỏ wrapper error, message)
      return result.data;
    } catch (error) {
      this.logger.error(`Error checking video status: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi kiểm tra trạng thái video',
      );
    }
  }

  /**
   * Cập nhật trạng thái video upload từ Zalo API response
   * Cập nhật trực tiếp vào database
   * @param token Token của video upload
   * @param statusData Dữ liệu trạng thái từ Zalo API
   * @returns Promise<void>
   */
  async updateVideoUploadStatus(token: string, statusData: any): Promise<void> {
    try {
      this.logger.debug(`Updating video upload status for token: ${token}`);
      this.logger.debug('Status data:', JSON.stringify(statusData, null, 2));

      // TODO: Cập nhật trực tiếp vào database
      // const updatedRecord = await this.zaloVideoUploadRepository.updateStatusByToken(
      //   token,
      //   statusData.status,
      //   statusData.status_message,
      //   statusData.convert_percent,
      //   statusData.video_id,
      // );

      // if (updatedRecord) {
      //   this.logger.log(`Successfully updated video status for token: ${token}`, {
      //     status: statusData.status,
      //     videoId: statusData.video_id,
      //     videoName: statusData.video_name,
      //     videoSize: statusData.video_size,
      //     convertPercent: statusData.convert_percent
      //   });
      // } else {
      //   this.logger.warn(`No record found to update for token: ${token}`);
      // }

      this.logger.log(`Video status checked for token: ${token.substring(0, 20)}...`);
    } catch (error) {
      this.logger.error(`Error updating video upload status: ${error.message}`, error.stack);
      // Không throw error để không ảnh hưởng đến flow chính
    }
  }

  /**
   * Verify bài viết và lấy ID bài viết
   * @param accessToken Access token của Official Account
   * @param token Token từ response tạo hoặc cập nhật bài viết
   * @returns ID bài viết
   */
  async verifyArticle(
    accessToken: string,
    token: string,
  ): Promise<ZaloArticleVerifyResponse> {
    try {
      this.logger.debug(`Verifying article with token: ${token}`);

      if (!token || token.trim() === '') {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Token không được để trống',
        );
      }

      const data = {
        token: token,
      };

      const result = await this.zaloService.post<any>(
        `${this.baseApiUrl}/article/verify`,
        accessToken,
        data,
      );

      this.logger.debug(`Verify API result:`, JSON.stringify(result));

      // Zalo API trả về trực tiếp object {id: "..."}, không có wrapper .data
      return result;
    } catch (error) {
      this.logger.error(
        `Error verifying article: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi verify bài viết',
      );
    }
  }
}
