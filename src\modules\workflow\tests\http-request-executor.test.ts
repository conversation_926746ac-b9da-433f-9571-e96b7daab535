/**
 * HTTP Request Executor Service Test
 * Test cases for the new executeNode method
 */

import { Test, TestingModule } from '@nestjs/testing';
import { HttpModule } from '@nestjs/axios';
import { HttpRequestExecutorService } from '../services/http-request-executor.service';
import { 
  IHttpRequestParameters, 
  EHttpMethod, 
  EAuthType 
} from '../interfaces/core/http/http-request.interface';

describe('HttpRequestExecutorService', () => {
  let service: HttpRequestExecutorService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [HttpModule],
      providers: [HttpRequestExecutorService],
    }).compile();

    service = module.get<HttpRequestExecutorService>(HttpRequestExecutorService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('executeNode', () => {
    it('should execute GET request successfully', async () => {
      const requestParams: IHttpRequestParameters = {
        url: 'https://jsonplaceholder.typicode.com/posts/1',
        method: EHttpMethod.GET,
        auth_type: EAuthType.NONE,
        headers: {
          'Accept': 'application/json'
        },
        timeout: 30000
      };

      const result = await service.executeNode(requestParams);

      expect(result).toBeDefined();
      expect(result.code).toBe(200);
      expect(result.data.success).toBe(true);
      expect(result.data.body).toBeDefined();
      expect(result.data.response.code).toBe(200);
      expect(result.data.request.method).toBe('GET');
      expect(result.data.request.url).toBe(requestParams.url);
    });

    it('should handle 404 error correctly', async () => {
      const requestParams: IHttpRequestParameters = {
        url: 'https://jsonplaceholder.typicode.com/posts/999999',
        method: EHttpMethod.GET,
        auth_type: EAuthType.NONE,
        timeout: 30000
      };

      const result = await service.executeNode(requestParams);

      expect(result).toBeDefined();
      expect(result.code).toBe(404);
      expect(result.data.success).toBe(false);
      expect(result.data.error).toBeDefined();
      expect(result.data.response.code).toBe(404);
    });

    it('should validate parameters correctly', async () => {
      const invalidParams: IHttpRequestParameters = {
        url: '', // Invalid URL
        method: EHttpMethod.GET,
        auth_type: EAuthType.NONE,
        timeout: 30000
      };

      await expect(service.executeNode(invalidParams)).rejects.toThrow();
    });

    it('should handle Bearer authentication', async () => {
      const requestParams: IHttpRequestParameters = {
        url: 'https://httpbin.org/bearer',
        method: EHttpMethod.GET,
        auth_type: EAuthType.BEARER,
        auth_config: {
          token: 'test-token-123'
        },
        timeout: 30000
      };

      const result = await service.executeNode(requestParams);

      expect(result).toBeDefined();
      expect(result.data.request.headers['Authorization']).toBe('Bearer test-token-123');
    });

    it('should handle POST request with body', async () => {
      const requestParams: IHttpRequestParameters = {
        url: 'https://jsonplaceholder.typicode.com/posts',
        method: EHttpMethod.POST,
        auth_type: EAuthType.NONE,
        headers: {
          'Content-Type': 'application/json'
        },
        body: {
          title: 'Test Post',
          body: 'This is a test post',
          userId: 1
        },
        timeout: 30000
      };

      const result = await service.executeNode(requestParams);

      expect(result).toBeDefined();
      expect(result.code).toBe(201);
      expect(result.data.success).toBe(true);
      expect(result.data.body).toBeDefined();
      expect(result.data.request.body).toEqual(requestParams.body);
    });

    it('should handle query parameters', async () => {
      const requestParams: IHttpRequestParameters = {
        url: 'https://jsonplaceholder.typicode.com/posts',
        method: EHttpMethod.GET,
        auth_type: EAuthType.NONE,
        query_params: {
          userId: '1',
          _limit: '5'
        },
        timeout: 30000
      };

      const result = await service.executeNode(requestParams);

      expect(result).toBeDefined();
      expect(result.code).toBe(200);
      expect(result.data.success).toBe(true);
      expect(Array.isArray(result.data.body)).toBe(true);
      expect(result.data.body.length).toBeLessThanOrEqual(5);
    });

    it('should handle API key authentication', async () => {
      const requestParams: IHttpRequestParameters = {
        url: 'https://httpbin.org/headers',
        method: EHttpMethod.GET,
        auth_type: EAuthType.API_KEY,
        auth_config: {
          api_key: 'test-api-key-123',
          api_key_header: 'X-API-Key'
        },
        timeout: 30000
      };

      const result = await service.executeNode(requestParams);

      expect(result).toBeDefined();
      expect(result.data.request.headers['X-API-Key']).toBe('test-api-key-123');
    });

    it('should handle network errors', async () => {
      const requestParams: IHttpRequestParameters = {
        url: 'https://invalid-domain-that-does-not-exist.com',
        method: EHttpMethod.GET,
        auth_type: EAuthType.NONE,
        timeout: 5000
      };

      const result = await service.executeNode(requestParams);

      expect(result).toBeDefined();
      expect(result.code).toBe(0);
      expect(result.data.success).toBe(false);
      expect(result.data.error).toBeDefined();
      expect(result.data.response.status).toBe('Network Error');
    });
  });

  describe('validateParameters', () => {
    it('should validate valid parameters', () => {
      const validParams: IHttpRequestParameters = {
        url: 'https://api.example.com',
        method: EHttpMethod.GET,
        auth_type: EAuthType.NONE,
        timeout: 30000
      };

      const errors = service.validateParameters(validParams);
      expect(errors).toHaveLength(0);
    });

    it('should return errors for invalid parameters', () => {
      const invalidParams: IHttpRequestParameters = {
        url: '', // Invalid
        method: EHttpMethod.GET,
        auth_type: EAuthType.NONE,
        timeout: 500 // Too low
      };

      const errors = service.validateParameters(invalidParams);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors).toContain('URL is required');
      expect(errors).toContain('Timeout must be between 1000 and 300000 milliseconds');
    });
  });

  describe('testConnection', () => {
    it('should test connection successfully', async () => {
      const result = await service.testConnection('https://jsonplaceholder.typicode.com');
      expect(result).toBe(true);
    });

    it('should fail connection test for invalid URL', async () => {
      const result = await service.testConnection('https://invalid-domain.com');
      expect(result).toBe(false);
    });
  });
});

/**
 * Integration Test Example
 */
export const integrationTestExample = `
describe('HttpRequestExecutorService Integration', () => {
  let service: HttpRequestExecutorService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [HttpModule],
      providers: [HttpRequestExecutorService],
    }).compile();

    service = module.get<HttpRequestExecutorService>(HttpRequestExecutorService);
  });

  it('should handle real API calls', async () => {
    const params: IHttpRequestParameters = {
      url: 'https://api.github.com/users/octocat',
      method: EHttpMethod.GET,
      auth_type: EAuthType.NONE,
      headers: {
        'User-Agent': 'RedAI-Test'
      },
      timeout: 30000
    };

    const result = await service.executeNode(params);
    
    expect(result.code).toBe(200);
    expect(result.data.body.login).toBe('octocat');
    expect(result.data.response_time).toBeGreaterThan(0);
  });
});
`;
