import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng admin_audience trong cơ sở dữ liệu
 * Bảng khách hàng của admin
 */
@Entity('admin_audience')
export class AdminAudience {
  /**
   * ID của audience
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * Tên của khách hàng
   */
  @Column({
    name: 'name',
    length: 255,
    nullable: true,
    comment: 'Tên khách hàng',
  })
  name: string;

  /**
   * Email của khách hàng
   */
  @Column({
    name: 'email',
    length: 255,
    nullable: true,
    comment: 'Email người dùng',
  })
  email: string;

  /**
   * Mã quốc gia (số)
   * @example 84
   */
  @Column({
    name: 'country_code',
    type: 'integer',
    nullable: true,
    comment: 'Mã quốc gia',
  })
  countryCode: number | null;

  /**
   * <PERSON><PERSON> điện thoại (không bao gồm mã quốc gia)
   * @example "912345678"
   */
  @Column({
    name: 'phone_number',
    type: 'varchar',
    length: 20,
    nullable: true,
    comment: 'Số điện thoại không bao gồm mã quốc gia',
  })
  phoneNumber: string | null;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', comment: 'Ngày tạo' })
  createdAt: number;

  /**
   * URL avatar của khách hàng (S3 key)
   */
  @Column({
    name: 'avatar',
    type: 'varchar',
    length: 500,
    nullable: true,
    comment: 'URL avatar của khách hàng',
  })
  avatar: string | null;

  /**
   * Số điện thoại (legacy field - sử dụng phoneNumber thay thế)
   */
  @Column({
    name: 'phone',
    type: 'varchar',
    length: 20,
    nullable: true,
    comment: 'Số điện thoại',
  })
  phone: string | null;

  /**
   * Địa chỉ của khách hàng
   */
  @Column({
    name: 'address',
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Địa chỉ của khách hàng',
  })
  address: string | null;

  /**
   * Zalo Social ID của khách hàng
   * @example "zalo_123456789"
   */
  @Column({
    name: 'zalo_social_id',
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Zalo Social ID của khách hàng',
  })
  zaloSocialId: string | null;

  /**
   * Danh sách avatar URLs từ các nguồn bên ngoài
   */
  @Column({
    name: 'avatars_external',
    type: 'jsonb',
    nullable: true,
    comment: 'Danh sách avatar URLs từ các nguồn bên ngoài',
  })
  avatarsExternal: any | null;

  /**
   * Nguồn import của audience
   * @example "ZALO"
   */
  @Column({
    name: 'import_resource',
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Nguồn import của audience',
  })
  importResource: string | null;

  /**
   * ID của Zalo Official Account
   * @example "123"
   */
  @Column({
    name: 'zalo_official_account_id',
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'ID của Zalo Official Account',
  })
  zaloOfficialAccountId: string | null;

  /**
   * Zalo User ID
   * @example "zalo_user_123"
   */
  @Column({
    name: 'zalo_user_id',
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Zalo User ID',
  })
  zaloUserId: string | null;

  /**
   * Trạng thái follow Zalo
   * @example true
   */
  @Column({
    name: 'zalo_user_is_follower',
    type: 'boolean',
    nullable: true,
    comment: 'Trạng thái follow Zalo',
  })
  zaloUserIsFollower: boolean | null;

  /**
   * Ngày tương tác cuối cùng với user (Unix timestamp)
   * @example **********
   */
  @Column({
    name: 'user_last_interaction_date',
    type: 'bigint',
    nullable: true,
    comment: 'Ngày tương tác cuối cùng với user',
  })
  userLastInteractionDate: number | null;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    nullable: true,
    comment: 'Ngày cập nhật',
  })
  updatedAt: number;
}
