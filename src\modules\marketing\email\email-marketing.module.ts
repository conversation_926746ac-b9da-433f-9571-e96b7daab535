import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bullmq';
import { QueueName } from '../../../queue';
import { InfraModule } from '../../../infra';

// Entities
import { UserCampaign } from '../entities/user-campaign.entity';
import { UserAudience } from '../entities/user-audience.entity';
import { UserAudienceCustomField } from '../entities/user-audience-custom-field.entity';
import { UserCampaignHistory } from '../entities/user-campaign-history.entity';
import { UserTemplateEmail } from '../entities/user-template-email.entity';
import { AdminEmailCampaign } from '../entities/admin-email-campaign.entity';
import { AdminAudience } from '../entities/admin-audience.entity';
import { AdminSegment } from '../entities/admin-segment.entity';

// Services
import {
  EmailMarketingService,
  EmailTemplateService,
  EmailTrackingService,
  EmailDeliveryService,
  EmailClickTrackingService,
} from './services';
import { GmailEmailService } from './services/gmail-email.service';
import { AudienceEmailStatusService } from '../services/audience-email-status.service';

// Repositories
import { AdminEmailCampaignRepository } from '../repositories/admin-email-campaign.repository';
import { AdminAudienceRepository } from '../repositories/admin-audience.repository';
import { AdminSegmentRepository } from '../repositories/admin-segment.repository';

// Processor
import { EmailMarketingProcessor } from './email-marketing.processor.clean';

// Controllers
import { EmailTrackingController } from './email-tracking.controller';
import { EmailMarketingController } from './email-marketing.controller';
import { EmailWebhookController } from './email-webhook.controller';
import { AudienceEmailStatusController } from '../controllers/audience-email-status.controller';

/**
 * Module xử lý email marketing
 */
@Module({
  imports: [
    // TypeORM entities
    TypeOrmModule.forFeature([
      UserCampaign,
      UserAudience,
      UserAudienceCustomField,
      UserCampaignHistory,
      UserTemplateEmail,
      AdminEmailCampaign,
      AdminAudience,
      AdminSegment,
    ]),

    // Bull queue
    BullModule.registerQueue({
      name: QueueName.EMAIL_MARKETING,
    }),

    // Infrastructure module (Redis, etc.)
    InfraModule,
  ],
  providers: [
    EmailMarketingService,
    EmailTemplateService,
    EmailTrackingService,
    EmailDeliveryService,
    EmailClickTrackingService,
    GmailEmailService,
    EmailMarketingProcessor,
    AudienceEmailStatusService,

    // Admin repositories
    AdminEmailCampaignRepository,
    AdminAudienceRepository,
    AdminSegmentRepository,
  ],
  controllers: [
    EmailTrackingController,
    EmailMarketingController,
    EmailWebhookController,
    AudienceEmailStatusController,
  ],
  exports: [
    EmailMarketingService,
    EmailTemplateService,
    EmailTrackingService,
    EmailDeliveryService,
    EmailClickTrackingService,
  ],
})
export class EmailMarketingModule {}
