import { Injectable, Logger } from '@nestjs/common';
import { McpConfiguration } from '../interfaces/mcp-config.interface';

/**
 * Raw MCP configuration data from database queries
 */
export interface McpConfigRawData {
  agentId: string;
  nameServer: string; // From Mcp.nameServer
  config: McpConfiguration; // From Mcp.config (JSONB)
  secretKey: string | null; // From Mcp.secret_key
  encryptedHeaders: string | null; // From Mcp.headers (string)
  description: string | null; // From Mcp.description
}

/**
 * MCP Config Builder Service
 *
 * Transforms raw database MCP data into the Record<nameServer, McpConfiguration>
 * format expected by AgentConfigInterface. Handles the many-to-many relationship
 * between agents and MCP servers.
 */
@Injectable()
export class McpConfigBuilderService {
  private readonly logger = new Logger(McpConfigBuilderService.name);

  /**
   * @param mcpData Pre-fetched MCP data from Query 4
   * @returns Map of agent IDs to their MCP configurations
   */
  buildMcpConfigs(
    mcpData: McpConfigRawData[],
  ): Record<string, Record<string, McpConfiguration>> {
    this.logger.debug(`Processing MCP configs for ${mcpData.length} records`);

    const result: Record<string, Record<string, McpConfiguration>> = {};

    const mcpsByAgent = this.groupMcpConfigsByAgent(mcpData);

    // Step 2: Transform each agent's MCP configs (pure transformation)
    for (const [agentId, mcpConfigs] of mcpsByAgent) {
      // Transform MCP configs for this agent
      const agentMcpConfig = this.transformMcpConfigsForAgent(mcpConfigs);
      result[agentId] = agentMcpConfig;
      this.logger.debug(
        `Built MCP config for agent ${agentId} with ${mcpConfigs.length} MCP servers`,
      );
    }

    // Agents not in the map will have null mcpConfig (which is normal and expected)
    this.logger.debug(
      `Successfully processed MCP configs for ${result.size} agents`,
    );
    return result;
  }

  /**
   * Transform MCP configs for a single agent into Record format
   *
   * @param mcpConfigs Array of MCP configs for one agent
   * @returns Record with nameServer as key, McpConfiguration as value
   */
  private transformMcpConfigsForAgent(
    mcpConfigs: McpConfigRawData[],
  ): Record<string, McpConfiguration> {
    const result: Record<string, McpConfiguration> = {};

    for (const mcpConfig of mcpConfigs) {
      // Validate required fields
      if (!mcpConfig.nameServer) {
        this.logger.error(
          `MCP config missing nameServer for agent ${mcpConfig.agentId}, skipping`,
        );
        throw new Error(
          `MCP config missing nameServer for agent ${mcpConfig.agentId}`,
        );
      }

      if (!mcpConfig.config) {
        this.logger.error(
          `MCP config missing config for agent ${mcpConfig.agentId}, nameServer ${mcpConfig.nameServer}, skipping`,
        );
        throw new Error(
          `MCP config missing config for agent ${mcpConfig.agentId}, nameServer ${mcpConfig.nameServer}`,
        );
      }

      const mcpConfiguration: McpConfiguration = {
        ...mcpConfig.config,
        encryptedHeaderString: mcpConfig.encryptedHeaders,
        description: mcpConfig.description || '',
      };

      result[mcpConfig.nameServer] = mcpConfiguration;

      this.logger.debug(
        `Added MCP server ${mcpConfig.nameServer} for agent ${mcpConfig.agentId}${mcpConfig.secretKey ? ' with secret key' : ' without secret key'}`,
        {
          nameServer: mcpConfig.nameServer,
          agentId: mcpConfig.agentId,
          descriptionFromDb: mcpConfig.description,
          descriptionInConfig: mcpConfiguration.description,
          hasDescription: !!mcpConfiguration.description,
        },
      );
    }

    return result;
  }

  /**
   * Group MCP configs by agent ID for efficient processing
   *
   * @param mcpData Array of MCP configuration data
   * @returns Map of agent IDs to their MCP configs
   */
  private groupMcpConfigsByAgent(
    mcpData: McpConfigRawData[],
  ): Map<string, McpConfigRawData[]> {
    const grouped = new Map<string, McpConfigRawData[]>();

    for (const mcp of mcpData) {
      if (!grouped.has(mcp.agentId)) {
        grouped.set(mcp.agentId, []);
      }
      grouped.get(mcp.agentId)!.push(mcp);
    }

    return grouped;
  }
}
