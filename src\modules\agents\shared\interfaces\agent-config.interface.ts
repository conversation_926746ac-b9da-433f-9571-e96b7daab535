import { Repository } from 'typeorm';
import {
  InputModality,
  ModelFeature,
  ModelProviderEnum,
  OutputModality,
  SamplingParameter,
  TypeAgentEnum,
} from '../enums';
import {
  AgentEntityConfigInterface,
  IStrategyContentStep,
} from './agent-entity-config.interface';
import { MultiServerMCPClient } from '@langchain/mcp-adapters';
import { AgentMemories, SystemPrompt } from '../entities';
import { env } from 'src/config';

export interface AgentConfigInterface {
  id: string;
  name: string;
  instruction: string;
  specializedConfig: AgentEntityConfigInterface | null;
  mcpClient: MultiServerMCPClient | null;
  mcpDescriptions:
    | {
        serverName: string;
        description: string;
      }[]
    | null;
  tools?: any[];
  type: TypeAgentEnum;
  owner: {
    userId?: number;
    employeeId?: number;
  };
  model: {
    name: string;
    provider: ModelProviderEnum;
    inputModalities: InputModality[];
    outputModalities: OutputModality[];
    samplingParameters: SamplingParameter[];
    features: ModelFeature[];
    maxTokens: number;
    parameters: {
      temperature?: number;
      topP?: number;
      topK?: number;
      maxTokens?: number;
      maxOutputTokens?: number;
    };
    pricing: {
      inputRate: number;
      outputRate: number;
    };
    encryptedApiKeyPairs: {
      publicKey: string;
      encryptedContent: string;
    }[];
  };
  promptBuilders: Array<() => string | Promise<string>>;
  agentMemoriesRepository: Repository<AgentMemories>;
  systemPromptRepository: Repository<SystemPrompt>;
}
export class AgentConfig {
  id: string;
  name: string;
  instruction: string;
  specializedConfig: AgentEntityConfigInterface | null;
  mcpClient: MultiServerMCPClient | null;
  mcpDescriptions:
    | {
        serverName: string;
        description: string;
      }[]
    | null;
  tools?: any[];
  type: TypeAgentEnum;
  owner: {
    userId?: number;
    employeeId?: number;
  };
  model: {
    name: string;
    provider: ModelProviderEnum;
    inputModalities: InputModality[];
    outputModalities: OutputModality[];
    samplingParameters: SamplingParameter[];
    features: ModelFeature[];
    maxTokens: number;
    parameters: {
      temperature?: number;
      topP?: number;
      topK?: number;
      maxTokens?: number;
      maxOutputTokens?: number;
    };
    pricing: {
      inputRate: number;
      outputRate: number;
    };
    encryptedApiKeyPairs: {
      publicKey: string;
      encryptedContent: string;
    }[];
  };
  promptBuilders: Array<() => string | Promise<string>>;
  agentMemoriesRepository: Repository<AgentMemories>;
  systemPromptRepository: Repository<SystemPrompt>;

  constructor(param: AgentConfigInterface) {
    this.id = param.id;
    this.name = param.name;
    this.instruction = param.instruction;
    this.specializedConfig = param.specializedConfig;
    this.mcpClient = param.mcpClient;
    this.mcpDescriptions = param.mcpDescriptions;
    this.tools = param.tools;
    this.type = param.type;
    this.owner = param.owner;
    this.model = param.model;
    this.promptBuilders = param.promptBuilders;
    this.agentMemoriesRepository = param.agentMemoriesRepository;
    this.systemPromptRepository = param.systemPromptRepository;
  }

  toJSON() {
    const { agentMemoriesRepository, systemPromptRepository, ...serializable } =
      this;
    return serializable;
  }

  async toPrompt(): Promise<string> {
    const parts: string[] = [];
    const agentConfig = this;

    const basePromptOfAgent = await this.systemPromptRepository.findOne({
      where: { active: true, agentType: agentConfig.type },
    });
    const basePromptContent =
      `<master-instruction>${basePromptOfAgent?.prompt || 'You are a helpful AI assistant of RedAI'}</master-instruction>` ||
      '<master-instruction>You are a helpful AI assistant of RedAI</master-instruction>';
    // Prompt builders (conditional)
    const fromPrompt = await Promise.all(
      agentConfig.promptBuilders.map((builder) => builder()),
    );
    parts.push(basePromptContent, ...fromPrompt);
    parts.push('<agent-configuration>');
    const memoriesPromise = this.agentMemoriesRepository.find({
      where: {
        agentId: agentConfig.id,
      },
      take: env.memory.MEMORY_PROMPT_LIMIT,
    });
    // Name (always present)
    parts.push(`<name>${agentConfig.name}</name>`);

    // Instruction (always present, full content)
    parts.push(`<instruction>${agentConfig.instruction}</instruction>`);

    // Profile (conditional)
    const profile = agentConfig.specializedConfig?.profile;
    if (profile) {
      parts.push('<profile>');
      if (profile.position)
        parts.push(`  <position>${profile.position}</position>`);
      if (profile.gender) parts.push(`  <gender>${profile.gender}</gender>`);
      if (profile.education)
        parts.push(`  <education>${profile.education}</education>`);
      if (profile.skills?.length)
        parts.push(`  <skills>${profile.skills.join(', ')}</skills>`);
      if (profile.personality?.length)
        parts.push(
          `  <personality>${profile.personality.join(', ')}</personality>`,
        );
      if (profile.languages?.length)
        parts.push(`  <languages>${profile.languages.join(', ')}</languages>`);
      if (profile.nations)
        parts.push(`  <nationality>${profile.nations}</nationality>`);
      parts.push('</profile>');
    }

    // Conversion settings (conditional)
    const conversion = agentConfig.specializedConfig?.convert;
    if (conversion?.length) {
      parts.push('<conversion-settings>');
      conversion.forEach((field: any) => {
        parts.push(
          `  <field name="${field.name}" type="${field.type}" required="${field.required || false}" />`,
        );
      });
      parts.push('</conversion-settings>');
    }

    // MCP Tools (conditional)
    if (agentConfig.mcpDescriptions?.length) {
      parts.push('<mcp-tools>');
      agentConfig.mcpDescriptions.forEach((mcp) => {
        parts.push(
          `  <tool server="${mcp.serverName}">${mcp.description}</tool>`,
        );
      });
      parts.push('</mcp-tools>');
    }

    // Model configuration (always present)
    const model = agentConfig.model;
    parts.push(`<model name="${model.name}" provider="${model.provider}">`);

    if (model.inputModalities?.length) {
      parts.push(
        `  <input-modalities>${model.inputModalities.join(', ')}</input-modalities>`,
      );
    }

    if (model.outputModalities?.length) {
      parts.push(
        `  <output-modalities>${model.outputModalities.join(', ')}</output-modalities>`,
      );
    }

    if (model.features?.length) {
      parts.push(`  <features>${model.features.join(', ')}</features>`);
    }

    if (model.parameters) {
      parts.push('  <parameters>');
      if (model.parameters.temperature !== undefined)
        parts.push(
          `    <temperature>${model.parameters.temperature}</temperature>`,
        );
      if (model.parameters.topP !== undefined)
        parts.push(`    <top-p>${model.parameters.topP}</top-p>`);
      if (model.parameters.topK !== undefined)
        parts.push(`    <top-k>${model.parameters.topK}</top-k>`);
      if (model.parameters.maxTokens !== undefined)
        parts.push(
          `    <max-tokens>${model.parameters.maxTokens}</max-tokens>`,
        );
      if (model.parameters.maxOutputTokens !== undefined)
        parts.push(
          `    <max-output-tokens>${model.parameters.maxOutputTokens}</max-output-tokens>`,
        );
      parts.push('  </parameters>');
    }

    parts.push(
      `  <pricing input-rate="${model.pricing.inputRate}" output-rate="${model.pricing.outputRate}" />`,
    );
    parts.push('</model>');

    // Agent type
    parts.push(`<agent-type>${agentConfig.type}</agent-type>`);

    parts.push('</agent-configuration>');

    parts.push('<agent-memories>');
    const memories = await memoriesPromise;

    memories.forEach((memory) => {
      parts.push(`<memory id="${memory.id}">${memory.content}</memory>`);
    });

    parts.push('</agent-memories>');

    if (
      agentConfig.specializedConfig?.content &&
      agentConfig.specializedConfig.example
    ) {
      parts.push(
        `<strategy-steps>${this.buildStrategyPromptSection({
          content: agentConfig.specializedConfig.content,
          example: agentConfig.specializedConfig.example,
        })}</strategy-steps>`,
      );
    }

    return parts.join('\n');
  }

  buildStrategyPromptSection(strategy: {
    content?: IStrategyContentStep[];
    example?: IStrategyContentStep[];
  }): string {
    // Return an empty string if no strategy data is available
    if (!strategy || (!strategy.content?.length && !strategy.example?.length)) {
      return '';
    }

    const parts: string[] = ['<strategy-steps>'];

    // Add the content strategy section if it exists
    if (strategy.content?.length) {
      parts.push(
        '  <content-strategy description="Follow these steps to structure the core message.">',
      );
      // Sort by stepOrder to ensure correctness
      const sortedContent = [...strategy.content].sort(
        (a, b) => a.stepOrder - b.stepOrder,
      );
      for (const item of sortedContent) {
        parts.push(
          `    <step order="${item.stepOrder}">${item.content}</step>`,
        );
      }
      parts.push('  </content-strategy>');
    }

    // Add the example strategy section if it exists
    if (strategy.example?.length) {
      parts.push(
        '  <example-strategy description="Use this example as a guide for tone and phrasing.">',
      );
      // Sort by stepOrder to ensure correctness
      const sortedExample = [...strategy.example].sort(
        (a, b) => a.stepOrder - b.stepOrder,
      );
      for (const item of sortedExample) {
        parts.push(
          `    <step order="${item.stepOrder}">${item.content}</step>`,
        );
      }
      parts.push('  </example-strategy>');
    }

    parts.push('</strategy-steps>');

    return parts.join('\n');
  }
}
