import { Check, Column, Entity, PrimaryColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng user_multi_agents trong cơ sở dữ liệu
 * Bảng trung gian lưu quan hệ đa cấp giữa các agents, cho phép một agent có nhiều cấp trên hoặc cấp dưới
 */
@Entity('user_multi_agents')
@Check('parent_agent_id <> child_agent_id')
export class UserMultiAgent {
  /**
   * ID của agent đóng vai trò là người đỡ đầu (hoặc cấp trên) trong mối quan hệ đa cấp
   * Là một phần của khóa chính kết hợp
   * Tham chiếu đến bảng agents_user
   */
  @PrimaryColumn('uuid', { name: 'parent_agent_id' })
  parentAgentId: string;

  /**
   * ID của agent đóng vai trò là người được đỡ đầu (hoặc cấp dưới) trong mối quan hệ đa cấp
   * Là một phần của khóa chính kết hợp
   * Tham chiếu đến bảng agents_user
   */
  @PrimaryColumn('uuid', { name: 'child_agent_id' })
  childAgentId: string;

  /**
   * Prompt hoặc hướng dẫn cho mối quan hệ giữa hai agent
   */
  @Column({ type: 'text', nullable: true })
  prompt: string | null;
}
