import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { MessageRole, Platform } from '../../enums';

@Entity('external_conversation_message')
export class ExternalConversationMessage {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    name: 'external_customer_platform_data_id',
    type: 'uuid',
    nullable: false,
  })
  externalCustomerPlatformDataId: string;

  @Column({ type: 'text', nullable: false })
  text: string;

  @Column({
    name: 'has_attachments',
    type: 'boolean',
    nullable: false,
    default: false,
  })
  hasAttachments: boolean;

  @Column({
    type: 'enum',
    enum: MessageRole,
    nullable: false,
    default: MessageRole.USER,
  })
  role: MessageRole;

  @Column({
    type: 'enum',
    enum: Platform,
    nullable: false,
  })
  platform: Platform;

  @Column({ name: 'agent_id', type: 'uuid', nullable: true })
  agentId?: string;

  @Column({ type: 'boolean', nullable: false, default: false })
  processed: boolean;

  @Column({
    name: 'created_at',
    type: 'bigint',
    nullable: false,
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  createdAt: string;

  @Column({
    name: 'updated_at',
    type: 'bigint',
    nullable: false,
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  updatedAt: string;

  @Column({ name: 'deleted_at', type: 'bigint', nullable: true })
  deletedAt?: string | null;

  @Column({ type: 'jsonb', nullable: true })
  raw?: any;

  @Column({ name: 'replying_to_message_id', type: 'uuid', nullable: true })
  replyingToMessageId?: string;
}
