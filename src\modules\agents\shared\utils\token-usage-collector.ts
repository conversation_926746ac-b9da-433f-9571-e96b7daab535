import {
  BaseCallbackHand<PERSON>,
  HandleLLMNewTokenCallbackFields,
  NewTokenIndices,
} from '@langchain/core/callbacks/base';
import { LLMResult } from '@langchain/core/outputs';
import { Logger } from '@nestjs/common';
import { AgentConfig } from '../interfaces';

const logger = new Logger('WorkerTokenUsageCollector');

export interface DetailedUsage {
  agentId: string;
  model: string | null;
  inputTokens: number;
  outputTokens: number;
  totalTokens: number;
  pointCost: number;
}

/**
 * Simplified token usage collector for worker
 * Only collects detailed usage data - no DB calls, no event emissions
 */
export class TokenUsageCollector extends BaseCallbackHandler {
  name = 'TokenUsageCollector';
  private usages: DetailedUsage[] = [];
  private accumulatedText: Record<string, string> = {};

  constructor(
    private readonly agentConfigMap: Record<string, AgentConfig>,
    private readonly owner: {
      userId?: number;
      employeeId?: number;
    },
  ) {
    super();

    // Validate required parameters
    if (!agentConfigMap) {
      throw new Error('agentConfigMap is required');
    }
    if (!owner || (!owner.userId && !owner.employeeId)) {
      throw new Error('owner must have either userId or employeeId');
    }
    if (owner.userId && owner.employeeId) {
      throw new Error('owner cannot have both userId and employeeId');
    }
  }

  public getUsages(): DetailedUsage[] {
    return this.usages;
  }

  public getTotalCost(): number {
    return this.usages.reduce(
      (totalCost, currentUsage) => totalCost + currentUsage.pointCost,
      0,
    );
  }

  public getTotalTokens(): number {
    return this.usages.reduce(
      (totalTokens, currentUsage) => totalTokens + currentUsage.totalTokens,
      0,
    );
  }

  public getOwner(): { userId?: number; employeeId?: number } {
    return this.owner;
  }

  /**
   * Get accumulated text for a specific agent
   * @param agentId - Agent ID to get accumulated text for
   * @returns Accumulated text string or empty string if no text accumulated
   */
  public getAccumulatedText(agentId: string): string {
    return this.accumulatedText[agentId] || '';
  }

  /**
   * Get all accumulated text for all agents
   * @returns Map of agentId to accumulated text
   */
  public getAllAccumulatedText(): Record<string, string> {
    return { ...this.accumulatedText };
  }

  /**
   * Clear accumulated text for a specific agent
   * @param agentId - Agent ID to clear accumulated text for
   */
  public clearAccumulatedText(agentId: string): void {
    delete this.accumulatedText[agentId];
  }

  /**
   * Clear all accumulated text
   */
  public clearAllAccumulatedText(): void {
    this.accumulatedText = {};
  }

  /**
   * Get total accumulated text length across all agents
   * @returns Total character count of all accumulated text
   */
  public getTotalAccumulatedTextLength(): number {
    return Object.values(this.accumulatedText).reduce((total, text) => total + text.length, 0);
  }

  /**
   * Get concatenated text from all agents (useful for debugging)
   * @param separator - Separator between agent texts (default: '\n---\n')
   * @returns Concatenated text from all agents
   */
  public getConcatenatedText(separator: string = '\n---\n'): string {
    return Object.entries(this.accumulatedText)
      .map(([agentId, text]) => `Agent ${agentId}:\n${text}`)
      .join(separator);
  }

  override async handleLLMNewToken(
    token: string,
    _idx: NewTokenIndices,
    runId: string,
    _parentRunId?: string,
    tags?: string[],
    _fields?: HandleLLMNewTokenCallbackFields,
  ) {
    // Extract agentId from tags using the format: agent:agent_id:agent_TYPE
    const agentTag = tags?.find((t) => t.startsWith('agent:'));
    if (!agentTag) {
      logger.debug('No agent tag found in LLM new token. Skipping text accumulation.', {
        runId,
        tags,
      });
      return;
    }

    // Parse tag format: agent:agent_id:agent_TYPE
    const tagParts = agentTag.split(':');
    if (tagParts.length < 2) {
      logger.warn('Invalid agent tag format. Expected agent:agent_id:agent_TYPE', {
        agentTag,
        runId,
      });
      return;
    }

    const agentId = tagParts[1];

    // Initialize accumulated text for this agent if not exists
    if (!this.accumulatedText[agentId]) {
      this.accumulatedText[agentId] = '';
    }

    // Accumulate the token text
    this.accumulatedText[agentId] += token;

    logger.debug(`Accumulated token for agent ${agentId}: "${token}"`, {
      agentId,
      runId,
      currentLength: this.accumulatedText[agentId].length,
    });
  }

  override async handleLLMEnd(
    output: LLMResult,
    _runId: string,
    _parentRunId?: string,
    tags?: string[],
  ): Promise<void> {
    logger.debug('WorkerTokenUsageCollector.handleLLMEnd called', {
      owner: this.owner,
      tags: tags || [],
      hasOutput: !!output,
      hasLLMOutput: !!output.llmOutput,
      hasTokenUsage: !!output.llmOutput?.tokenUsage,
    });

    const tokenUsage = output.llmOutput?.tokenUsage;
    if (!tokenUsage) {
      logger.debug('No token usage data in LLMEnd event. Skipping.');
      return;
    }

    // Extract agentId from tags using the new format: agent:agent_id:agent_TYPE
    const agentTag = tags?.find((t) => t.startsWith('agent:'));
    if (!agentTag) {
      logger.warn(
        'No agent tag found in LLM call. Skipping cost calculation.',
        {
          _runId,
          tags,
        },
      );
      return;
    }

    // Parse tag format: agent:agent_id:agent_TYPE
    const tagParts = agentTag.split(':');
    if (tagParts.length < 2) {
      logger.warn(
        'Invalid agent tag format. Expected agent:agent_id:agent_TYPE',
        {
          agentTag,
          _runId,
        },
      );
      return;
    }

    const agentId = tagParts[1];
    const agentConfig = this.agentConfigMap[agentId];
    if (!agentConfig) {
      logger.error(
        `Could not find configuration for tagged agentId: ${agentId}. Skipping cost calculation.`,
        { agentId, availableAgents: Object.keys(this.agentConfigMap) },
      );
      return;
    }

    let pointCost = 0;
    if (agentConfig.model.pricing) {
      const { inputRate, outputRate } = agentConfig.model.pricing;

      const inputCost = tokenUsage.promptTokens * inputRate;
      const outputCost = tokenUsage.completionTokens * outputRate;
      pointCost = inputCost + outputCost;

      const usage: DetailedUsage = {
        agentId: agentId,
        model: agentConfig.model.name,
        inputTokens: tokenUsage.promptTokens,
        outputTokens: tokenUsage.completionTokens,
        totalTokens: tokenUsage.totalTokens,
        pointCost: pointCost,
      };

      this.usages.push(usage);

      logger.debug(`Calculated point cost for agent ${agentId}: ${pointCost}`, {
        agentId,
        model: agentConfig.model.name,
        inputTokens: tokenUsage.promptTokens,
        outputTokens: tokenUsage.completionTokens,
        totalTokens: tokenUsage.totalTokens,
        pointCost,
        owner: this.owner,
      });
    } else {
      logger.warn(
        `No pricing information found for agent: ${agentId}. Cost will be 0.`,
        { agentId, model: agentConfig.model.name },
      );

      // Still track usage even without pricing
      const usage: DetailedUsage = {
        agentId: agentId,
        model: agentConfig.model.name,
        inputTokens: tokenUsage.promptTokens,
        outputTokens: tokenUsage.completionTokens,
        totalTokens: tokenUsage.totalTokens,
        pointCost: 0,
      };

      this.usages.push(usage);
    }
  }

  /**
   * Get usage summary for logging/debugging
   */
  public getUsageSummary(): {
    totalUsages: number;
    totalCost: number;
    totalTokens: number;
    totalAccumulatedTextLength: number;
    owner: { userId?: number; employeeId?: number };
    usagesByAgent: Record<
      string,
      { cost: number; tokens: number; count: number }
    >;
    accumulatedTextByAgent: Record<string, number>;
  } {
    const usagesByAgent: Record<
      string,
      { cost: number; tokens: number; count: number }
    > = {};

    this.usages.forEach((usage) => {
      if (!usagesByAgent[usage.agentId]) {
        usagesByAgent[usage.agentId] = { cost: 0, tokens: 0, count: 0 };
      }
      usagesByAgent[usage.agentId].cost += usage.pointCost;
      usagesByAgent[usage.agentId].tokens += usage.totalTokens;
      usagesByAgent[usage.agentId].count += 1;
    });

    // Calculate accumulated text statistics
    const accumulatedTextByAgent: Record<string, number> = {};
    Object.entries(this.accumulatedText).forEach(([agentId, text]) => {
      accumulatedTextByAgent[agentId] = text.length;
    });

    return {
      totalUsages: this.usages.length,
      totalCost: this.getTotalCost(),
      totalTokens: this.getTotalTokens(),
      totalAccumulatedTextLength: this.getTotalAccumulatedTextLength(),
      owner: this.owner,
      usagesByAgent,
      accumulatedTextByAgent,
    };
  }
}
