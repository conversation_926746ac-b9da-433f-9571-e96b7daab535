import { Entity, PrimaryColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng agents_mcp trong cơ sở dữ liệu
 * Bảng trung gian lưu quan hệ many-to-many giữa agents và mcps
 */
@Entity('agents_mcp')
export class AgentsMcp {
  /**
   * ID của agent
   * Là một phần của khóa chính kết hợp
   * Tham chiếu đến bảng agents
   */
  @PrimaryColumn({ name: 'agent_id', type: 'uuid' }) agentId: string;

  /**
   * ID của MCP server
   * Là một phần của khóa chính kết hợp
   * Tham chiếu đến bảng mcps
   */
  @PrimaryColumn({ name: 'mcp_id', type: 'uuid' }) mcpId: string;
}
