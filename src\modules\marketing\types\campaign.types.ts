/**
 * Interface cho thông tin audience trong campaign
 */
export interface CampaignAudience {
  /**
   * Tên của audience
   */
  name: string;

  /**
   * Email của audience
   */
  email: string;

  /**
   * Tracking ID cho email tracking (tùy chọn)
   */
  trackingId?: string;

  /**
   * Job ID từ Bull queue (tùy chọn)
   */
  jobId?: string;
}

/**
 * Interface cho thông tin segment trong campaign
 */
export interface CampaignSegment {
  /**
   * ID của segment
   */
  id: number;

  /**
   * Tên của segment
   */
  name: string;

  /**
   * Mô tả của segment (tùy chọn)
   */
  description?: string;
}

/**
 * Interface cho dữ liệu campaign với audience và segment đầy đủ
 */
export interface UserCampaignData {
  /**
   * ID của campaign
   */
  id: number;

  /**
   * ID của người dùng
   */
  userId: number;

  /**
   * Tiêu đề chiến dịch
   */
  title: string;

  /**
   * <PERSON><PERSON> tả chiến dịch
   */
  description: string;

  /**
   * Nền tảng gửi (email, sms, ...)
   */
  platform: string;

  /**
   * Nội dung chiến dịch
   */
  content: string;

  /**
   * Thông tin máy chủ gửi
   */
  server: any;

  /**
   * Thời gian dự kiến gửi chiến dịch (Unix timestamp)
   */
  scheduledAt: number;

  /**
   * Tiêu đề email (chỉ áp dụng cho chiến dịch email)
   */
  subject: string;

  /**
   * Trạng thái chiến dịch
   */
  status: string;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  updatedAt: number;

  /**
   * Thông tin segment (nếu có)
   */
  segment: CampaignSegment | null;

  /**
   * Danh sách audience với thông tin chi tiết
   */
  audiences: CampaignAudience[] | null;

  /**
   * Danh sách ID của job trong queue (để có thể hủy job khi cần)
   */
  jobIds: string[] | null;

  /**
   * Template variables cho campaign (dùng để thay thế trong template)
   */
  templateVariables: Record<string, any> | null;

  /**
   * Thời gian bắt đầu gửi (Unix timestamp)
   */
  startedAt: number | null;

  /**
   * Thời gian hoàn thành (Unix timestamp)
   */
  completedAt: number | null;
}

/**
 * Interface cho lịch sử campaign với audience đầy đủ
 */
export interface UserCampaignHistoryData {
  /**
   * ID của lịch sử
   */
  id: number;

  /**
   * ID của campaign
   */
  campaignId: number;

  /**
   * Thông tin audience
   */
  audience: CampaignAudience | null;

  /**
   * Trạng thái gửi
   */
  status: string;

  /**
   * Thời gian gửi (Unix timestamp)
   */
  sentAt: number;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  createdAt: number;
}
