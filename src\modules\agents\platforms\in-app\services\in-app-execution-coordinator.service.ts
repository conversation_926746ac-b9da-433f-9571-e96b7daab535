import { Injectable, Logger } from '@nestjs/common';
import { AgentConfig } from '../../../shared/interfaces';
import { TokenUsageCollector } from '../../../shared/utils';
import { InAppJobData } from '../interfaces';
import { InAppSupervisorWorkersConfig } from '../graph-configs/in-app-supervisor-workers.config';

@Injectable()
export class InAppExecutionCoordinatorService {
  private readonly logger = new Logger(InAppExecutionCoordinatorService.name);

  /**
   * Build GraphConfigurable object for LangGraph execution
   * Combines supervisor config, worker configs, and execution parameters
   */
  buildGraphConfigurable(param: {
    jobData: InAppJobData;
    supervisorConfig: Record<string, AgentConfig>;
    workerConfigs: Record<string, AgentConfig> | undefined;
    platformThreadId: string;
  }): InAppSupervisorWorkersConfig {
    const { jobData, supervisorConfig, workerConfigs, platformThreadId } =
      param;
    this.logger.debug('Building GraphConfigurable object', {
      agentId: jobData.mainAgentId,
      hasWorkerConfigs:
        !!workerConfigs && Object.keys(workerConfigs).length > 0,
      webSearchEnabled: jobData.webSearchEnabled,
    });

    const graphConfigurable: InAppSupervisorWorkersConfig = {
      executorAgent: supervisorConfig[jobData.mainAgentId] as AgentConfig,
      thread_id: platformThreadId,
      alwaysApproveToolCall: !!jobData.alwaysApproveToolCall,
      platform: jobData.platform,
      workerAgents: workerConfigs,
      toolChoices: jobData.webSearchEnabled ? 'web_search' : undefined,
      currentUser: {
        user: jobData?.humanInfo?.user,
        employee: jobData?.humanInfo?.employee,
      },
    };

    this.logger.debug('GraphConfigurable built successfully', {
      threadId: graphConfigurable.thread_id,
      platform: graphConfigurable.platform,
      hasWorkerAgents: !!graphConfigurable.workerAgents,
    });

    return graphConfigurable;
  }

  /**
   * Initialize token usage collector for billing
   * Creates collector with agent mappings for accurate token tracking
   */
  initializeTokenCollector(
    jobData: InAppJobData,
    graphConfigurable: InAppSupervisorWorkersConfig,
  ): TokenUsageCollector {
    this.logger.debug('Initializing token usage collector', {
      agentId: jobData.mainAgentId,
      hasWorkerAgents: !!graphConfigurable.workerAgents,
    });

    const agentMap = {};
    agentMap[jobData.mainAgentId] = graphConfigurable.executorAgent;

    if (graphConfigurable.workerAgents) {
      Object.entries(graphConfigurable.workerAgents).forEach(
        ([agentId, agentConfig]) => {
          agentMap[agentId] = agentConfig;
        },
      );
    }

    const tokenCollector = new TokenUsageCollector(agentMap, {
      userId: jobData?.humanInfo?.user?.userId,
      employeeId: jobData?.humanInfo?.employee?.employeeId,
    });

    this.logger.debug('Token usage collector initialized', {
      agentCount: Object.keys(agentMap).length,
      userId: jobData?.humanInfo?.user?.userId,
      employeeId: jobData?.humanInfo?.employee?.employeeId,
    });

    return tokenCollector;
  }

  /**
   * Build execution parameters for SupervisorWorkersGraph
   * Creates tags and context information for execution tracking
   */
  buildExecutionParameters(
    jobData: InAppJobData,
    graphConfigurable: InAppSupervisorWorkersConfig,
  ): { tags: string[]; userContext: string; agentTag: string } {
    this.logger.debug('Building execution parameters', {
      runId: jobData.runId,
      platform: jobData.platform,
      agentId: jobData.mainAgentId,
    });

    const userContext = jobData?.humanInfo?.user?.userId
      ? `user:${jobData?.humanInfo?.user?.userId}`
      : `employee:${jobData?.humanInfo?.employee?.employeeId}`;

    const agentType = graphConfigurable?.executorAgent?.type;
    const agentTag = `agent:${jobData.mainAgentId}:${agentType}`; // Required for token usage collection

    const tags = [
      userContext,
      `run:${jobData.runId}`,
      `platform:${jobData.platform}`,
      agentTag,
    ];

    this.logger.debug('Execution parameters built', {
      userContext,
      agentTag,
      tagCount: tags.length,
    });

    return { tags, userContext, agentTag };
  }

  /**
   * Setup LangGraph execution with streaming and token collection
   * Coordinates all execution components and prepares for streaming
   */
  async setupLangGraphExecution(
    jobData: InAppJobData,
    langGraphInput: any,
    graphConfigurable: InAppSupervisorWorkersConfig,
  ): Promise<{
    tokenUsageCollector: TokenUsageCollector;
    input: any;
    tags: string[];
    abortController: AbortController;
  }> {
    this.logger.debug('Setting up LangGraph execution', {
      runId: jobData.runId,
      hasMessages: !!langGraphInput.messages,
      isCommand: !langGraphInput.messages,
    });

    // Initialize token usage collector for billing
    const tokenUsageCollector = this.initializeTokenCollector(
      jobData,
      graphConfigurable,
    );

    // Build LangGraph input from combined messages
    // Handle different input types: Command (tool call decision) vs regular messages
    const input = langGraphInput.messages
      ? { messages: langGraphInput.messages }
      : langGraphInput; // For Command objects, pass as-is

    // Build execution parameters for SupervisorWorkersGraph
    const { tags } = this.buildExecutionParameters(jobData, graphConfigurable);

    const abortController = new AbortController();

    this.logger.debug('LangGraph execution setup complete', {
      runId: jobData.runId,
      inputType: langGraphInput.messages ? 'messages' : 'command',
      tagCount: tags.length,
    });

    return {
      tokenUsageCollector,
      input,
      tags,
      abortController,
    };
  }
}
