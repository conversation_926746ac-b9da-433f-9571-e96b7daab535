# Zalo Webhook Job Names Enum Usage Guide

## Tổng Quan

File `zalo-job-names.enum.ts` định nghĩa enum `ZaloWebhookJobName` để thay thế việc sử dụng string literals trong `routeToProcessor` method của `ZaloWebhookProcessor`.

**Note**: Enum này được đồng bộ với `ZaloQueueJobName` trong `redai-v201-be-app/src/shared/webhook/handlers/zalo-event-handlers/zalo-queue.types.ts`

## Lợi Ích

### ✅ **Trước khi sử dụng Enum (String Literals)**
```typescript
// ❌ Dễ typo, không có IntelliSense, khó maintain
switch (jobName) {
  case 'process-user-message':  // Có thể typo thành 'proces-user-message'
    return this.processUserMessageJob(event, context);
  case 'proces-order':  // ❌ Typo!
    return this.processOrderJob(event, context);
}
```

### ✅ **Sau khi sử dụng Enum**
```typescript
// ✅ Type-safe, IntelliSense support, refactor-friendly
switch (jobName) {
  case ZaloWebhookJobName.PROCESS_USER_MESSAGE:  // Auto-complete
    return this.processUserMessageJob(event, context);
  case ZaloWebhookJobName.PROCESS_ORDER:  // Type-safe
    return this.processOrderJob(event, context);
}
```

## Cấu Trúc Enum

### 📋 **Danh Sách Đầy Đủ Job Names**

#### **Real-time Jobs (High Priority)**
- `PROCESS_USER_MESSAGE` = 'process-user-message'
- `PROCESS_USER_INTERACTION` = 'process-user-interaction'
- `PROCESS_FOLLOW_EVENT` = 'process-follow-event'

#### **Business Logic Jobs (Medium Priority)**
- `PROCESS_ORDER` = 'process-order'
- `PROCESS_USER_INFO` = 'process-user-info'
- `PROCESS_FEEDBACK` = 'process-feedback'
- `PROCESS_CALL_EVENT` = 'process-call-event'
- `PROCESS_CONSENT` = 'process-consent'

#### **Analytics Jobs (Low Priority)**
- `TRACK_MESSAGE_STATUS` = 'track-message-status'
- `TRACK_OA_MESSAGE` = 'track-oa-message'
- `TRACK_INTERACTION` = 'track-interaction'

#### **Background Jobs (Lowest Priority)**
- `PROCESS_TEMPLATE_EVENT` = 'process-template-event'
- `PROCESS_SYSTEM_EVENT` = 'process-system-event'
- `PROCESS_GROUP_MANAGEMENT` = 'process-group-management'

## Cách Sử Dụng

### 1. **Import Enum**
```typescript
import { ZaloWebhookJobName } from './zalo-job-names.enum';
```

### 2. **Sử dụng trong Switch Statement**
```typescript
private async routeToProcessor(jobName: string, event: any, context: any): Promise<any> {
  switch (jobName) {
    // Real-time processing
    case ZaloWebhookJobName.PROCESS_USER_MESSAGE:
      return this.processUserMessageJob(event, context);

    case ZaloWebhookJobName.PROCESS_USER_INTERACTION:
      return this.processUserInteractionJob(event, context);

    // Business logic processing
    case ZaloWebhookJobName.PROCESS_ORDER:
      return this.processOrderJob(event, context);

    // Analytics processing
    case ZaloWebhookJobName.TRACK_MESSAGE_STATUS:
      return this.trackMessageStatusJob(event, context);

    // Background processing
    case ZaloWebhookJobName.PROCESS_TEMPLATE_EVENT:
      return this.processTemplateEventJob(event, context);

    default:
      this.logger.warn(`Unknown job name: ${jobName}`);
      return this.processZaloEvent(event, context.oaId, context.integrationId, context.userId);
  }
}
```

### 3. **Helper Functions**

#### **Get Job Priority**
```typescript
import { getJobPriority } from './zalo-job-names.enum';

const priority = getJobPriority(ZaloWebhookJobName.PROCESS_USER_MESSAGE); // 150
const priority2 = getJobPriority(ZaloWebhookJobName.TRACK_INTERACTION); // 25
```

#### **Get Job Category**
```typescript
import { getJobCategory } from './zalo-job-names.enum';

const category = getJobCategory(ZaloWebhookJobName.PROCESS_USER_MESSAGE); // 'real-time'
const category2 = getJobCategory(ZaloWebhookJobName.PROCESS_ORDER); // 'business-logic'
```

#### **Get All Job Names**
```typescript
import { ALL_ZALO_WEBHOOK_JOB_NAMES } from './zalo-job-names.enum';

// Iterate through all job names
ALL_ZALO_WEBHOOK_JOB_NAMES.forEach(jobName => {
  console.log(`Job: ${jobName}, Priority: ${getJobPriority(jobName)}`);
});
```

#### **Get Jobs by Category**
```typescript
import { ZALO_WEBHOOK_JOBS_BY_CATEGORY } from './zalo-job-names.enum';

// Get all real-time jobs
const realTimeJobs = ZALO_WEBHOOK_JOBS_BY_CATEGORY['real-time'];
// [PROCESS_USER_MESSAGE, PROCESS_USER_INTERACTION, PROCESS_FOLLOW_EVENT]

// Get all analytics jobs
const analyticsJobs = ZALO_WEBHOOK_JOBS_BY_CATEGORY['analytics'];
// [TRACK_MESSAGE_STATUS, TRACK_OA_MESSAGE, TRACK_INTERACTION]
```

## Validation & Type Safety

### **Type-safe Job Name Validation**
```typescript
function isValidZaloJobName(jobName: string): jobName is ZaloWebhookJobName {
  return Object.values(ZaloWebhookJobName).includes(jobName as ZaloWebhookJobName);
}

// Usage
if (isValidZaloJobName(incomingJobName)) {
  // TypeScript biết incomingJobName là ZaloWebhookJobName
  const priority = getJobPriority(incomingJobName);
}
```

## Migration Guide

### **Bước 1: Update Import**
```typescript
// Thêm import
import { ZaloWebhookJobName } from './zalo-job-names.enum';
```

### **Bước 2: Replace String Literals**
```typescript
// Thay thế tất cả string literals bằng enum values
case 'process-user-message' → case ZaloWebhookJobName.PROCESS_USER_MESSAGE
case 'process-order' → case ZaloWebhookJobName.PROCESS_ORDER
// ... và tất cả các cases khác
```

### **Bước 3: Update Documentation**
- Cập nhật JSDoc comments để reference enum
- Cập nhật README và documentation files

## Best Practices

1. **Luôn sử dụng enum** thay vì string literals
2. **Import đúng enum** từ file constants
3. **Sử dụng helper functions** để get priority và category
4. **Validate job names** trước khi xử lý
5. **Update documentation** khi thêm job names mới

## Thêm Job Name Mới

Khi cần thêm job name mới:

1. **Thêm vào enum** trong `zalo-job-names.enum.ts`
2. **Đồng bộ với enum** trong `redai-v201-be-app/src/shared/webhook/handlers/zalo-event-handlers/zalo-queue.types.ts`
3. **Cập nhật helper functions** (getJobPriority, getJobCategory)
4. **Thêm case mới** trong `routeToProcessor` method
5. **Implement processor method** tương ứng
6. **Update documentation** này

```typescript
// Ví dụ thêm job mới
export enum ZaloWebhookJobName {
  // ... existing jobs
  
  /**
   * Job xử lý payment events
   * Priority: Medium - Xử lý thanh toán
   */
  PROCESS_PAYMENT = 'process-payment',
}

// Thêm vào getJobPriority
case ZaloWebhookJobName.PROCESS_PAYMENT:
  return 75; // Medium priority

// Thêm vào getJobCategory  
case ZaloWebhookJobName.PROCESS_PAYMENT:
  return 'business-logic';

// Thêm vào routeToProcessor
case ZaloWebhookJobName.PROCESS_PAYMENT:
  return this.processPaymentJob(event, context);
```
