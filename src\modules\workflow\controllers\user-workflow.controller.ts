import { <PERSON>, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { IExecutionPayload } from '../dto/node-execute.dto';
import { WorkflowJobType } from '../enums/workflow-job-types.enum';
import { RedisPublisherService } from '../services/redis-publisher.service';
import { UserWorkflowService } from './../service/user-workflow.service';

@Controller()
export class UserWorkflowController {
    private readonly logger = new Logger(UserWorkflowController.name);

    constructor(
        private readonly userWorkflowService: UserWorkflowService,
        private readonly redisPublisher: RedisPublisherService
    ) { }

    /**
     * Handle workflow/node execution - Enhanced handler with startNode support
     * Logic:
     * - startNode = null && currentNode != null → Execute single node
     * - startNode != null && currentNode = null → Execute workflow from startNode
     * - startNode = null && currentNode = null → Execute full workflow
     * - Always REALTIME mode (push Redis events)
     */
    @MessagePattern(WorkflowJobType.USER_EXECUTE)
    async handleExecute(@Payload() payload: IExecutionPayload): Promise<any> {
        const { startNode, currentNode } = payload;

        // Determine execution type based on new logic
        const isNodeExecution = this.determineExecutionType(startNode, currentNode);

        this.logger.log(`[REALTIME] Handling ${isNodeExecution} execution: ${this.getExecutionDescription(payload)}`);

        try {
            if (isNodeExecution) {
                // Execute single node - BE APP đợi đến khi hoàn thành
                const result = await this.userWorkflowService.executeNodeSync(payload);
                return result;
            } else {
                // Execute workflow - BE APP không đợi, return ngay
                this.executeWorkflowAsync(payload, !isNodeExecution);

                // Return ngay cho BE APP
                return {
                    success: true,
                    message: 'Workflow execution started',
                    executionId: payload.executionId,
                    startNode: payload.startNode
                };
            }
        } catch (error) {
            this.logger.error(`[REALTIME] ${isNodeExecution} execution failed:`, error);

            return {
                success: false,
                error: {
                    message: error.message,
                    code: 'EXECUTION_ERROR',
                    executionId: payload.executionId
                }
            };
        }
    }

    /**
     * Determine execution type based on startNode and currentNode
     */
    private determineExecutionType(
        startNode: string | null | undefined,
        currentNode: string | null | undefined
    ): boolean {
        // startNode = null && currentNode != null → Execute single node
        if (!startNode && currentNode) {
            return true;
        }

        // startNode != null && currentNode = null → Execute workflow from startNode
        if (startNode && !currentNode) {
            return false;
        }

        // startNode = null && currentNode = null → Error (invalid)
        if (!startNode && !currentNode) {
            throw new Error('Invalid execution parameters: must specify either startNode or currentNode');
        }

        // startNode != null && currentNode != null → Error (invalid combination)
        throw new Error('Invalid execution parameters: cannot specify both startNode and currentNode');
    }

    /**
     * Get execution description for logging
     */
    private getExecutionDescription(payload: IExecutionPayload): string {
        const { startNode, currentNode, workflowId } = payload;

        if (currentNode) {
            return `node ${currentNode}`;
        }

        if (startNode) {
            return `workflow ${workflowId} from start node ${startNode}`;
        }

        return `entire workflow ${workflowId}`;
    }

    /**
     * Execute workflow asynchronously (không đợi hoàn thành)
     * Workflow lifecycle events được handle trong executeWorkflowWithMode
     */
    private async executeWorkflowAsync(payload: IExecutionPayload, _isWorkflowFromStart: boolean): Promise<void> {
        try {
            this.logger.log(`[REALTIME] Starting async workflow execution: ${this.getExecutionDescription(payload)}`);

            // Execute workflow trong background với complete lifecycle events
            await this.userWorkflowService.executeWorkflowWithMode(
                payload,
                'REALTIME'
            );

            this.logger.log(`[REALTIME] Async workflow execution completed: ${payload.executionId}`);

        } catch (error) {
            this.logger.error(`[REALTIME] Async workflow execution failed: ${payload.executionId}`, error);
            // Error events đã được handle trong executeWorkflowWithMode
        }
    }
}