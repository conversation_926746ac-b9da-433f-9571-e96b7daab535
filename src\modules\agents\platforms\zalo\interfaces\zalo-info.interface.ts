import { Platform } from '../../../shared/enums';
import { UserInfo } from '../../../shared/interfaces';
import { UserConvertCustomerInfo } from '../../../shared/interfaces';

/**
 * Zalo Context Information Interface
 * Contains Zalo-specific context data for agent processing
 */
export interface ZaloInfo {
  /**
   * Zalo Official Account ID
   */
  oaId: string;

  /**
   * Name of the Official Account
   */
  oaName: string;

  /**
   * Encrypted configuration containing access tokens and credentials
   */
  encryptedConfig: string;

  /**
   * Secret key for decrypting the configuration
   */
  secretKey: string;

  /**
   * Detailed Zalo user information from API (optional)
   */
  zaloUserDetail?: any;
}

/**
 * Zalo Job Data Interface
 * Extends the base job structure with Zalo-specific context
 */
export interface ZaloJobData {
  /**
   * Run identifier (UUID)
   */
  runId: string;

  /**
   * Thread identifier - corresponds to ExternalCustomerPlatformData.id
   */
  threadId: string;

  /**
   * Main agent ID handling the conversation
   */
  mainAgentId: string;

  /**
   * Optional planner agent ID for planner-executor pattern
   */
  plannerAgentId?: string;

  /**
   * Platform - always ZALO
   */
  platform: Platform.ZALO;

  /**
   * Redis keys for run status and streaming
   */
  keys: {
    /**
     * Platform thread ID: "zalo:thread-123"
     */
    platformThreadId: string;
    /**
     * Run status key: "run_status:zalo:thread-123"
     */
    runStatusKey: string;
    /**
     * Stream key: "zalo:agent_stream:thread-123:run-456"
     */
    streamKey: string;
  };

  /**
   * Human participants in the conversation
   */
  humanInfo: {
    /**
     * Zalo OA owner (business owner who pays for tokens)
     */
    zaloOwner: UserInfo;
    /**
     * Zalo user (customer/visitor interacting with the OA)
     */
    zaloUser: UserConvertCustomerInfo & { zaloUserId: string };
    /**
     * Zalo-specific context information
     */
    zaloInfo: ZaloInfo;
  };
}

/**
 * Example Zalo Job Data structure:
 * {
 *   runId: '8ab6e045-76ab-4ae7-93fb-68882d049e78',
 *   threadId: '0a983b62-cdd0-4ebe-9d9d-b259f97194ac',
 *   mainAgentId: '0c0d5c6d-403a-4ae5-8bd2-2f00b7a09b45',
 *   plannerAgentId: 'agent-456',
 *   platform: Platform.ZALO,
 *   keys: {
 *     platformThreadId: 'zalo:0a983b62-cdd0-4ebe-9d9d-b259f97194ac',
 *     runStatusKey: 'run_status:zalo:0a983b62-cdd0-4ebe-9d9d-b259f97194ac',
 *     streamKey: 'zalo:agent_stream:0a983b62-cdd0-4ebe-9d9d-b259f97194ac:8ab6e045-76ab-4ae7-93fb-68882d049e78',
 *   },
 *   humanInfo: {
 *     zaloOwner: {
 *       userId: 1,
 *       fullName: 'Business Owner',
 *       email: '<EMAIL>',
 *       // ... other UserInfo fields
 *     },
 *     zaloUser: {
 *       id: '864b9263-257d-4999-b468-74ff73ca8d1d',
 *       externalPlatformId: '0a983b62-cdd0-4ebe-9d9d-b259f97194ac',
 *       name: 'Zalo User',
 *       phone: '+***********',
 *       // ... other UserConvertCustomerInfo fields
 *     },
 *     zaloInfo: {
 *       oaId: 'oa_123456789',
 *       oaName: 'My Business OA',
 *       encryptedConfig: 'encrypted_token_data',
 *       secretKey: 'secret_key_for_decryption',
 *       zaloUserDetail: {
 *         user_id: 'zalo_user_123',
 *         display_name: 'User Display Name',
 *         // ... other Zalo API user fields
 *       }
 *     },
 *   },
 * }
 */
