import { Injectable, Logger } from '@nestjs/common';
import { BaseNodeExecutor } from '../../base/base-node.executor';
import { 
  ExecutorContext, 
  ValidationResult,
} from '../../base/node-executor.interface';
import { DetailedNodeExecutionResult, NodeExecutionConfig } from '../../../types';
import { NodeGroupEnum } from '../../../../../enums/node-group.enum';
import { ENodeType } from '../../../../../interfaces/node-manager.interface';
import {
  ILoopParameters,
  ILoopInput,
  ILoopOutput,
  validateLoopParameters
} from '../../../../../interfaces';
import { ConditionEvaluatorService } from '../../shared/condition-evaluator.service';
import { ValidationUtils } from '../../shared/validation.utils';

/**
 * Executor for LOOP node type
 * Handles iterative logic with various loop types
 */
@Injectable()
export class LoopExecutor extends BaseNodeExecutor {
  readonly nodeGroup = NodeGroupEnum.LOGIC;
  readonly supportedNodeTypes = [ENodeType.LOOP];
  readonly executorName = 'LoopExecutor';
  readonly version = '1.0.0';

  constructor(
    private readonly conditionEvaluatorService: ConditionEvaluatorService
  ) {
    super();
  }

  /**
   * Execute LOOP node
   */
  protected async executeNode(
    context: ExecutorContext,
    config: NodeExecutionConfig
  ): Promise<DetailedNodeExecutionResult> {
    const startTime = Date.now();

    // Parse and validate parameters
    const params = context.node.parameters as ILoopParameters;
    const input = context.inputData as ILoopInput;

    try {
      
      this.logger.debug(`Executing LOOP: ${params.type}`);

      let loopResult: any;

      switch (params.type) {
        case 'for':
          loopResult = await this.executeForLoop(params, input);
          break;

        case 'while':
          loopResult = await this.executeWhileLoop(params, input);
          break;

        case 'for_each':
          loopResult = await this.executeForEachLoop(params, input);
          break;

        case 'range':
          loopResult = await this.executeRangeLoop(params, input);
          break;

        default:
          throw new Error(`Unsupported loop type: ${params.type}`);
      }
      
      const executionTime = Date.now() - startTime;
      
      const output: ILoopOutput = {
        results: loopResult.data,
        loop_metadata: {
          iterations_executed: loopResult.iterations,
          iterations_planned: loopResult.plannedIterations || loopResult.iterations,
          completed: !loopResult.breakConditionMet && !loopResult.maxIterationsReached,
          termination_reason: this.getTerminationReason(loopResult),
          execution_time: executionTime,
          errors: loopResult.errors || [],
          control_actions: loopResult.controlActions || []
        },
        final_context: loopResult.finalContext || {
          index: loopResult.iterations - 1,
          counter: loopResult.iterations,
          total: loopResult.iterations,
          is_first: false,
          is_last: true
        },
        original_data: input.data
      };
      
      return {
        nodeType: 'LOOP',
        success: true,
        outputData: output,
        metadata: {
          executionTime,
          loopType: params.type,
          iterationsCompleted: loopResult.iterations,
          breakConditionMet: loopResult.breakConditionMet || false,
          maxIterationsReached: loopResult.maxIterationsReached || false,
          breakReason: loopResult.breakReason,
          customMetrics: {
            loopType: params.type,
            iterationsCompleted: loopResult.iterations,
            breakConditionMet: loopResult.breakConditionMet || false,
            maxIterationsReached: loopResult.maxIterationsReached || false,
          },
          logs: [
            `LOOP ${params.type} completed`,
            `Iterations: ${loopResult.iterations}`,
            `Break condition met: ${loopResult.breakConditionMet || false}`,
            `Execution time: ${executionTime}ms`,
          ],
        },
      };
      
    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      return {
        nodeType: 'LOOP',
        success: false,
        error,
        metadata: {
          executionTime,
          loopType: params.type,
          iterationsCompleted: 0,
          breakConditionMet: false,
          maxIterationsReached: false,
          logs: [
            `LOOP execution failed: ${error.message}`,
            `Execution time: ${executionTime}ms`,
          ],
        },
      };
    }
  }

  /**
   * Execute FOR loop
   */
  private async executeForLoop(params: ILoopParameters, input: ILoopInput): Promise<any> {
    const start = params.for_config?.start || 0;
    const end = params.for_config?.end || 0;
    const step = params.for_config?.step || 1;
    const maxIterations = params.max_iterations || 1000;
    
    const results: any[] = [];
    let iterations = 0;
    let breakConditionMet = false;
    let breakReason: string | undefined;
    
    for (let i = start; step > 0 ? i < end : i > end; i += step) {
      if (iterations >= maxIterations) {
        breakReason = 'Max iterations reached';
        break;
      }
      
      // Check break conditions if provided
      if (params.break_conditions && params.break_conditions.length > 0) {
        const currentData = { ...input.data, loop_index: i, loop_iteration: iterations };

        for (const breakCondition of params.break_conditions) {
          if (!breakCondition.enabled) continue;

          try {
            const conditionResult = await this.conditionEvaluatorService.evaluateCondition(
              breakCondition.condition,
              currentData
            );

            if (conditionResult.result && breakCondition.action === 'break') {
              breakConditionMet = true;
              breakReason = 'Break condition met';
              break;
            }
          } catch (error) {
            this.logger.warn(`Break condition evaluation failed: ${error.message}`);
          }
        }

        if (breakConditionMet) break;
      }
      
      // Simulate loop body execution (in real implementation, this would execute child nodes)
      results.push({
        iteration: iterations,
        index: i,
        data: { ...input.data, loop_index: i, loop_iteration: iterations }
      });
      
      iterations++;
    }
    
    return {
      iterations,
      data: results,
      breakConditionMet,
      breakReason,
      maxIterationsReached: iterations >= maxIterations
    };
  }

  /**
   * Execute WHILE loop
   */
  private async executeWhileLoop(params: ILoopParameters, input: ILoopInput): Promise<any> {
    if (!params.while_condition) {
      throw new Error('While condition is required for WHILE loop');
    }
    
    const maxIterations = params.max_iterations || 1000;
    const results: any[] = [];
    let iterations = 0;
    let breakConditionMet = false;
    let breakReason: string | undefined;
    
    while (iterations < maxIterations) {
      // Evaluate while condition
      const currentData = { ...input.data, loop_iteration: iterations };
      const whileResult = await this.conditionEvaluatorService.evaluateCondition(
        params.while_condition,
        currentData
      );

      if (!whileResult.result) {
        breakReason = 'While condition became false';
        break;
      }

      // Check break conditions if provided
      if (params.break_conditions && params.break_conditions.length > 0) {
        for (const breakCondition of params.break_conditions) {
          if (!breakCondition.enabled) continue;

          try {
            const breakResult = await this.conditionEvaluatorService.evaluateCondition(
              breakCondition.condition,
              currentData
            );

            if (breakResult.result && breakCondition.action === 'break') {
              breakConditionMet = true;
              breakReason = 'Break condition met';
              break;
            }
          } catch (error) {
            this.logger.warn(`Break condition evaluation failed: ${error.message}`);
          }
        }

        if (breakConditionMet) break;
      }
      
      // Simulate loop body execution
      results.push({
        iteration: iterations,
        data: currentData
      });
      
      iterations++;
    }
    
    return {
      iterations,
      data: results,
      breakConditionMet,
      breakReason,
      maxIterationsReached: iterations >= maxIterations
    };
  }

  /**
   * Execute FOR_EACH loop
   */
  private async executeForEachLoop(params: ILoopParameters, input: ILoopInput): Promise<any> {
    // Use items from input or get from items_field
    let array: any[];

    if (input.items && Array.isArray(input.items)) {
      array = input.items;
    } else if (params.items_field) {
      array = this.getFieldValue(input.data, params.items_field);
      if (!Array.isArray(array)) {
        throw new Error(`Field '${params.items_field}' is not an array`);
      }
    } else {
      throw new Error('Items array or items_field is required for FOR_EACH loop');
    }
    
    const maxIterations = params.max_iterations || 1000;
    const results: any[] = [];
    let iterations = 0;
    let breakConditionMet = false;
    let breakReason: string | undefined;
    
    for (let i = 0; i < array.length && iterations < maxIterations; i++) {
      const currentItem = array[i];
      const currentData = {
        ...input.data,
        loop_iteration: iterations,
        loop_index: i,
        current_item: currentItem
      };
      
      // Check break conditions if provided
      if (params.break_conditions && params.break_conditions.length > 0) {
        for (const breakCondition of params.break_conditions) {
          if (!breakCondition.enabled) continue;

          try {
            const breakResult = await this.conditionEvaluatorService.evaluateCondition(
              breakCondition.condition,
              currentData
            );

            if (breakResult.result && breakCondition.action === 'break') {
              breakConditionMet = true;
              breakReason = 'Break condition met';
              break;
            }
          } catch (error) {
            this.logger.warn(`Break condition evaluation failed: ${error.message}`);
          }
        }

        if (breakConditionMet) break;
      }
      
      // Simulate loop body execution
      results.push({
        iteration: iterations,
        index: i,
        item: currentItem,
        data: currentData
      });
      
      iterations++;
    }
    
    return {
      iterations,
      data: results,
      breakConditionMet,
      breakReason,
      maxIterationsReached: iterations >= maxIterations
    };
  }

  /**
   * Validate LOOP node input
   */
  protected async validateNodeSpecificInput(
    context: ExecutorContext,
    result: ValidationResult
  ): Promise<void> {
    const params = context.node.parameters as ILoopParameters;
    
    // Use existing validation function from interface
    const interfaceValidation = validateLoopParameters(params);
    if (!interfaceValidation.isValid) {
      for (const error of interfaceValidation.errors) {
        ValidationUtils.addError(
          result,
          'INTERFACE_VALIDATION_ERROR',
          error,
          'parameters'
        );
      }
    }

    // Additional custom validations
    this.validateLoopSpecific(params, result);
  }

  /**
   * LOOP specific validations
   */
  private validateLoopSpecific(
    params: ILoopParameters,
    result: ValidationResult
  ): void {
    // Validate max iterations
    if (params.max_iterations) {
      ValidationUtils.validateNumberRange(
        result,
        params.max_iterations,
        'Max iterations',
        1,
        10000,
        'max_iterations'
      );
      
      if (params.max_iterations > 1000) {
        ValidationUtils.addWarning(
          result,
          'HIGH_MAX_ITERATIONS',
          `High max iterations (${params.max_iterations}) may cause performance issues`,
          'max_iterations',
          'Consider using a lower value or optimizing loop logic'
        );
      }
    }

    // Validate loop type specific configurations
    switch (params.type) {
      case 'for':
        this.validateForLoopConfig(params, result);
        break;

      case 'while':
        this.validateWhileLoopConfig(params, result);
        break;

      case 'for_each':
        this.validateForEachLoopConfig(params, result);
        break;

      case 'range':
        this.validateRangeLoopConfig(params, result);
        break;
    }
  }

  private validateForLoopConfig(params: ILoopParameters, result: ValidationResult): void {
    if (!params.for_config) {
      ValidationUtils.addError(
        result,
        'MISSING_FOR_CONFIG',
        'For loop configuration is required for FOR loop type',
        'for_config'
      );
      return;
    }

    const config = params.for_config;
    
    ValidationUtils.validateRequired(result, config.start, 'For loop start', 'for_config.start');
    ValidationUtils.validateRequired(result, config.end, 'For loop end', 'for_config.end');
    
    if (config.step !== undefined && config.step === 0) {
      ValidationUtils.addError(
        result,
        'INVALID_STEP',
        'For loop step cannot be zero',
        'for_config.step',
        config.step
      );
    }
  }

  private validateWhileLoopConfig(params: ILoopParameters, result: ValidationResult): void {
    if (!params.while_condition) {
      ValidationUtils.addError(
        result,
        'MISSING_WHILE_CONDITION',
        'While condition is required for WHILE loop type',
        'while_condition'
      );
    }
  }

  private validateForEachLoopConfig(params: ILoopParameters, result: ValidationResult): void {
    if (!params.items_field) {
      ValidationUtils.addWarning(
        result,
        'MISSING_ITEMS_FIELD',
        'Items field is recommended for FOR_EACH loop type',
        'items_field',
        'Consider specifying items_field or provide items in input'
      );
    } else {
      ValidationUtils.validateFieldPath(
        result,
        params.items_field,
        'Items field',
        'items_field'
      );
    }
  }

  private validateRangeLoopConfig(params: ILoopParameters, result: ValidationResult): void {
    if (!params.range_config) {
      ValidationUtils.addError(
        result,
        'MISSING_RANGE_CONFIG',
        'Range configuration is required for RANGE loop type',
        'range_config'
      );
      return;
    }

    const config = params.range_config;

    ValidationUtils.validateRequired(result, config.start, 'Range loop start', 'range_config.start');
    ValidationUtils.validateRequired(result, config.end, 'Range loop end', 'range_config.end');

    if (config.step !== undefined && config.step === 0) {
      ValidationUtils.addError(
        result,
        'INVALID_STEP',
        'Range loop step cannot be zero',
        'range_config.step',
        config.step
      );
    }
  }

  /**
   * Get termination reason based on loop result
   */
  private getTerminationReason(loopResult: any): 'completed' | 'max_iterations' | 'timeout' | 'break' | 'error' {
    if (loopResult.breakConditionMet) {
      return 'break';
    }
    if (loopResult.maxIterationsReached) {
      return 'max_iterations';
    }
    if (loopResult.error) {
      return 'error';
    }
    return 'completed';
  }

  /**
   * Execute RANGE loop
   */
  private async executeRangeLoop(params: ILoopParameters, input: ILoopInput): Promise<any> {
    if (!params.range_config) {
      throw new Error('Range configuration is required for RANGE loop');
    }

    const { start, end, step } = params.range_config;
    const maxIterations = params.max_iterations || 1000;

    const results: any[] = [];
    let iterations = 0;
    let breakConditionMet = false;
    let breakReason: string | undefined;

    for (let i = start; i <= end; i += step) {
      if (iterations >= maxIterations) {
        breakReason = 'Max iterations reached';
        break;
      }

      // Check break conditions if provided
      if (params.break_conditions && params.break_conditions.length > 0) {
        const currentData = { ...input.data, loop_index: i, loop_iteration: iterations };

        for (const breakCondition of params.break_conditions) {
          if (!breakCondition.enabled) continue;

          try {
            const conditionResult = await this.conditionEvaluatorService.evaluateCondition(
              breakCondition.condition,
              currentData
            );

            if (conditionResult.result && breakCondition.action === 'break') {
              breakConditionMet = true;
              breakReason = 'Break condition met';
              break;
            }
          } catch (error) {
            this.logger.warn(`Break condition evaluation failed: ${error.message}`);
          }
        }

        if (breakConditionMet) break;
      }

      // Simulate loop body execution
      results.push({
        iteration: iterations,
        index: i,
        value: i,
        data: { ...input.data, loop_index: i, loop_iteration: iterations }
      });

      iterations++;
    }

    return {
      iterations,
      data: results,
      breakConditionMet,
      breakReason,
      maxIterationsReached: iterations >= maxIterations,
      plannedIterations: Math.ceil((end - start + 1) / step)
    };
  }

  /**
   * Get field value using dot notation
   */
  private getFieldValue(obj: Record<string, any>, fieldPath: string): any {
    try {
      return fieldPath.split('.').reduce((current, key) => {
        if (current === null || current === undefined) {
          return undefined;
        }
        return current[key];
      }, obj);
    } catch (error) {
      return undefined;
    }
  }
}
