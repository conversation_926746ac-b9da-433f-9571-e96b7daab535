import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Enum cho display mode
 */
export enum DisplayMode {
  CORNER = 'CORNER',
  CENTER = 'CENTER'
}

/**
 * Enum cho side mode
 */
export enum SideMode {
  FLOATING = 'FLOATING',
  FIXED = 'FIXED'
}

/**
 * Entity đại diện cho bảng box_chat_config trong cơ sở dữ liệu
 * Lưu trữ cấu hình cho box chat của website
 */
@Entity('box_chat_config')
export class BoxChatConfig {
  /**
   * ID tự động tăng (serial)
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * Văn bản chào mừng
   */
  @Column({
    name: 'welcome_text',
    type: 'varchar',
    length: 200,
    default: 'Xin chào, tôi có thể giúp gì bạn?',
    comment: 'Văn bản chào mừng'
  })
  welcomeText: string;

  /**
   * Avatar của chatbot
   */
  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Avatar của chatbot'
  })
  avatar: string | null;

  /**
   * Chế độ hiển thị
   * - "CORNER": Hiển thị nút nhỏ ở góc, click để mở chat
   * - "CENTER": Hiển thị widget chat ngay giữa màn hình
   */
  @Column({
    name: 'display_mode',
    type: 'enum',
    enum: DisplayMode,
    default: DisplayMode.CENTER,
    nullable: false,
    comment: 'Chế độ hiển thị - "corner": Hiển thị nút nhỏ ở góc, click để mở chat - "center": Hiển thị widget chat ngay giữa màn hình'
  })
  displayMode: DisplayMode;

  /**
   * Chế độ bên
   */
  @Column({
    name: 'side_mode',
    type: 'enum',
    enum: SideMode,
    default: SideMode.FLOATING,
    nullable: false,
    comment: 'Chế độ bên'
  })
  sideMode: SideMode;

  /**
   * Màu chính
   */
  @Column({
    name: 'color_primary',
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: 'Màu chính'
  })
  colorPrimary: string | null;

  /**
   * Icon
   */
  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Icon'
  })
  icon: string | null;

  /**
   * ID của integration (website được tích hợp)
   */
  @Column({
    name: 'integration_id',
    type: 'uuid',
    nullable: true,
    comment: 'website được tích hợp'
  })
  integrationId: string | null;
}
