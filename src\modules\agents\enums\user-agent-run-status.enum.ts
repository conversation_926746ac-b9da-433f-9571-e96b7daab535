export enum UserAgentRunStatus {
  /**
   * Initial state when run is created
   */
  CREATED = 'created',

  /**
   * When worker starts processing the run
   */
  RUNNING = 'running',

  /**
   * Successful completion of the run
   */
  COMPLETED = 'completed',

  /**
   * Error or failure during run processing
   */
  FAILED = 'failed',

  /**
   * Run was cancelled by user or system
   */
  CANCELLED = 'cancelled',
}
