import { Injectable, Logger } from '@nestjs/common';
import axios, { AxiosInstance } from 'axios';
import { env } from '../../config/env';

/**
 * Interface cho QR Code Session Request
 */
export interface CreateQRCodeSessionRequest {
  integration_id: string;
}

/**
 * Interface cho QR Code Session Response
 */
export interface QRCodeSessionResponse {
  code: number;
  message: string;
  result: {
    session_id: string;
    qr_code_base64: string;
    expires_at: number;
    integration_id: string;
  };
}

/**
 * Interface cho Session Status Response
 */
export interface SessionStatusResponse {
  code: number;
  message: string;
  result: {
    session_id: string;
    status: 'pending' | 'success' | 'expired' | 'error';
    integration_id: string;
    created_at: string;
    expires_at: string;
  };
}

/**
 * Interface cho Browser Automation Request
 */
export interface BrowserAutomationRequest {
  url: string;
  actions: BrowserAction[];
  options?: {
    headless?: boolean;
    timeout?: number;
    viewport?: {
      width: number;
      height: number;
    };
  };
}

/**
 * Interface cho Browser Action
 */
export interface BrowserAction {
  type: 'click' | 'type' | 'wait' | 'screenshot' | 'navigate' | 'extract';
  selector?: string;
  text?: string;
  timeout?: number;
  options?: Record<string, any>;
}

/**
 * Interface cho Browser Automation Response
 */
export interface BrowserAutomationResponse {
  code: number;
  message: string;
  result: {
    success: boolean;
    data?: any;
    screenshots?: string[];
    error?: string;
  };
}

/**
 * Service để thao tác với Automation Web API
 */
@Injectable()
export class AutomationWebService {
  private readonly logger = new Logger(AutomationWebService.name);
  private readonly httpClient: AxiosInstance;
  private readonly baseUrl: string;
  private readonly apiKey: string;
  private readonly timeout: number;

  constructor() {
    this.baseUrl = env.automationWeb.AUTOMATION_WEB_API_URL;
    this.apiKey = env.automationWeb.AUTOMATION_WEB_API_KEY || '';
    this.timeout = env.automationWeb.AUTOMATION_WEB_TIMEOUT;

    this.httpClient = axios.create({
      baseURL: this.baseUrl,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        ...(this.apiKey && { Authorization: `Bearer ${this.apiKey}` }),
      },
    });

    // Request interceptor để log requests
    this.httpClient.interceptors.request.use(
      (config) => {
        this.logger.debug(
          `🚀 Request: ${config.method?.toUpperCase()} ${config.url}`,
        );
        return config;
      },
      (error) => {
        this.logger.error('❌ Request Error:', error);
        return Promise.reject(error);
      },
    );

    // Response interceptor để log responses
    this.httpClient.interceptors.response.use(
      (response) => {
        this.logger.debug(
          `✅ Response: ${response.status} ${response.config.url}`,
        );
        return response;
      },
      (error) => {
        this.logger.error(
          `❌ Response Error: ${error.response?.status} ${error.config?.url}`,
          error.response?.data,
        );
        return Promise.reject(error);
      },
    );

    this.logger.log(
      `🔧 AutomationWebService initialized with base URL: ${this.baseUrl}`,
    );
  }

  /**
   * Tạo QR Code session cho Zalo login
   */
  async createQRCodeSession(
    integrationId: string,
  ): Promise<QRCodeSessionResponse> {
    try {
      this.logger.log(
        `📱 Creating QR code session for integration: ${integrationId}`,
      );

      const response = await this.httpClient.post<QRCodeSessionResponse>(
        '/api/v1/zalo/qr-code',
        {
          integration_id: integrationId,
          keep_browser_open: true, // Giữ browser mở sau khi đăng nhập thành công
        },
      );

      if (response.data.code !== 200) {
        throw new Error(
          `Failed to create QR session: ${response.data.message}`,
        );
      }

      this.logger.log(
        `✅ QR code session created: ${response.data.result.session_id}`,
      );
      return response.data;
    } catch (error) {
      this.logger.error(
        `❌ Error creating QR code session: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy trạng thái session
   */
  async getSessionStatus(
    sessionId: string,
  ): Promise<SessionStatusResponse | null> {
    try {
      this.logger.debug(`🔍 Getting session status: ${sessionId}`);

      const response = await this.httpClient.get<SessionStatusResponse>(
        `/api/v1/zalo/qr-code/status/${sessionId}`,
      );

      if (response.data.code === 404) {
        this.logger.warn(`⚠️ Session not found: ${sessionId}`);
        return null; // Return null thay vì response để caller biết session không tồn tại
      }

      if (response.data.code !== 200) {
        throw new Error(
          `Failed to get session status: ${response.data.message}`,
        );
      }

      this.logger.debug(`📊 Session status: ${response.data.result.status}`);
      return response.data;
    } catch (error) {
      // Nếu là 404 error từ axios, return null thay vì throw
      if (error.response?.status === 404) {
        this.logger.warn(`⚠️ Session not found (HTTP 404): ${sessionId}`);
        return null;
      }

      this.logger.error(
        `❌ Error getting session status: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Thực hiện browser automation
   */
  async executeBrowserAutomation(
    request: BrowserAutomationRequest,
  ): Promise<BrowserAutomationResponse> {
    try {
      this.logger.log(
        `🤖 Executing browser automation for URL: ${request.url}`,
      );

      const response = await this.httpClient.post<BrowserAutomationResponse>(
        '/api/v1/browser/automation',
        request,
      );

      if (response.data.code !== 200) {
        throw new Error(`Browser automation failed: ${response.data.message}`);
      }

      this.logger.log(`✅ Browser automation completed successfully`);
      return response.data;
    } catch (error) {
      this.logger.error(
        `❌ Error executing browser automation: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Chụp screenshot của trang web
   */
  async takeScreenshot(
    url: string,
    options?: {
      fullPage?: boolean;
      viewport?: { width: number; height: number };
    },
  ): Promise<string> {
    try {
      this.logger.log(`📸 Taking screenshot of: ${url}`);

      const request: BrowserAutomationRequest = {
        url,
        actions: [
          {
            type: 'screenshot',
            options: {
              fullPage: options?.fullPage || false,
            },
          },
        ],
        options: {
          headless: true,
          viewport: options?.viewport || { width: 1920, height: 1080 },
        },
      };

      const response = await this.executeBrowserAutomation(request);

      if (!response.result.success || !response.result.screenshots?.length) {
        throw new Error('Failed to capture screenshot');
      }

      this.logger.log(`✅ Screenshot captured successfully`);
      return response.result.screenshots[0];
    } catch (error) {
      this.logger.error(
        `❌ Error taking screenshot: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Extract dữ liệu từ trang web
   */
  async extractData(
    url: string,
    selectors: Record<string, string>,
  ): Promise<Record<string, any>> {
    try {
      this.logger.log(`🔍 Extracting data from: ${url}`);

      const actions: BrowserAction[] = Object.entries(selectors).map(
        ([key, selector]) => ({
          type: 'extract',
          selector,
          options: { key },
        }),
      );

      const request: BrowserAutomationRequest = {
        url,
        actions,
        options: {
          headless: true,
        },
      };

      const response = await this.executeBrowserAutomation(request);

      if (!response.result.success) {
        throw new Error(`Data extraction failed: ${response.result.error}`);
      }

      this.logger.log(`✅ Data extracted successfully`);
      return response.result.data || {};
    } catch (error) {
      this.logger.error(
        `❌ Error extracting data: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Kiểm tra health của automation-web service
   */
  async healthCheck(): Promise<boolean> {
    try {
      this.logger.debug(`🏥 Checking automation-web health`);

      const response = await this.httpClient.get('/api/v1/health', {
        timeout: 5000, // 5 seconds timeout for health check
      });

      const isHealthy = response.status === 200;
      this.logger.debug(
        `💚 Automation-web health: ${isHealthy ? 'OK' : 'FAILED'}`,
      );

      return isHealthy;
    } catch (error) {
      this.logger.warn(
        `⚠️ Automation-web health check failed: ${error.message}`,
      );
      return false;
    }
  }

  /**
   * Crawl danh sách bạn bè từ Zalo Personal
   */
  async crawlZaloFriends(
    zaloUid: string,
    headless: boolean = false,
  ): Promise<any> {
    try {
      this.logger.log(`Crawling Zalo friends for UID: ${zaloUid}`);

      const response = await this.httpClient.get(
        `/api/v1/zalo/${zaloUid}/friends?headless=${headless}`,
      );

      this.logger.log(`Crawl friends completed for UID: ${zaloUid}`, {
        success: response.status === 200,
        friendsCount: response.data?.result?.friends?.length || 0,
      });

      return response.data;
    } catch (error) {
      this.logger.error(`Failed to crawl friends for UID: ${zaloUid}`, error);
      throw error;
    }
  }

  /**
   * Crawl danh sách nhóm từ Zalo Personal
   */
  async crawlZaloGroups(
    zaloUid: string,
    headless: boolean = false,
  ): Promise<any> {
    try {
      this.logger.log(`Crawling Zalo groups for UID: ${zaloUid}`);

      const response = await this.httpClient.get(
        `/api/v1/zalo/${zaloUid}/groups?headless=${headless}`,
      );

      this.logger.log(`Crawl groups completed for UID: ${zaloUid}`, {
        success: response.status === 200,
        groupsCount: response.data?.result?.groups?.length || 0,
      });

      return response.data;
    } catch (error) {
      this.logger.error(`Failed to crawl groups for UID: ${zaloUid}`, error);
      throw error;
    }
  }

  /**
   * Gửi yêu cầu kết bạn hàng loạt
   */
  async sendFriendRequestBatch(
    zaloUid: string,
    phoneNumbers: string[],
    delayBetweenRequests: number = 3,
    headless: boolean = false,
  ): Promise<any> {
    try {
      this.logger.log(`Sending friend request batch for UID: ${zaloUid}`, {
        phoneCount: phoneNumbers.length,
        delay: delayBetweenRequests,
      });

      const response = await this.httpClient.post(
        `/api/v1/zalo/${zaloUid}/friend-request?headless=${headless}`,
        {
          phone_numbers: phoneNumbers,
          delay_between_requests: delayBetweenRequests,
        },
      );

      this.logger.log(`Friend request batch completed for UID: ${zaloUid}`, {
        success: response.status === 200,
        results: response.data?.result,
      });

      return response.data;
    } catch (error) {
      this.logger.error(
        `Failed to send friend request batch for UID: ${zaloUid}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Gửi tin nhắn hàng loạt
   */
  async sendMessageBatch(
    zaloUid: string,
    phoneNumbers: string[],
    messageContent: string,
    delayBetweenRequests: number = 3,
    delayBetweenMessages: number = 2,
    headless: boolean = false,
    batchName?: string,
  ): Promise<any> {
    try {
      this.logger.log(`Sending message batch for UID: ${zaloUid}`, {
        phoneCount: phoneNumbers.length,
        messageLength: messageContent.length,
        requestDelay: delayBetweenRequests,
        messageDelay: delayBetweenMessages,
      });

      const response = await this.httpClient.post(
        `/api/v1/zalo/${zaloUid}/message/batch?headless=${headless}`,
        {
          batch_name: batchName || `Message_Batch_${Date.now()}`,
          phone_numbers: phoneNumbers,
          message_content: messageContent,
          delay_between_requests: delayBetweenRequests,
          delay_between_messages: delayBetweenMessages,
        },
      );

      this.logger.log(`Message batch completed for UID: ${zaloUid}`, {
        success: response.status === 200,
        results: response.data?.result,
      });

      return response.data;
    } catch (error) {
      this.logger.error(
        `Failed to send message batch for UID: ${zaloUid}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Gửi kết bạn + tin nhắn đồng thời
   */
  async sendAllFriendRequestAndMessage(
    zaloUid: string,
    phoneNumbers: string[],
    messageContent: string,
    delayBetweenRequests: number = 3,
    delayBetweenMessages: number = 2,
    headless: boolean = false,
  ): Promise<any> {
    try {
      this.logger.log(
        `Sending all (friend request + message) for UID: ${zaloUid}`,
        {
          phoneCount: phoneNumbers.length,
          messageLength: messageContent.length,
          requestDelay: delayBetweenRequests,
          messageDelay: delayBetweenMessages,
        },
      );

      const response = await this.httpClient.post(
        `/api/v1/zalo/${zaloUid}/send-all?headless=${headless}`,
        {
          phone_numbers: phoneNumbers,
          message_content: messageContent,
          delay_between_requests: delayBetweenRequests,
          delay_between_messages: delayBetweenMessages,
        },
      );

      this.logger.log(`Send all completed for UID: ${zaloUid}`, {
        success: response.status === 200,
        results: response.data?.result,
      });

      return response.data;
    } catch (error) {
      this.logger.error(`Failed to send all for UID: ${zaloUid}`, error);
      throw error;
    }
  }

  /**
   * Get service info
   */
  getServiceInfo() {
    return {
      baseUrl: this.baseUrl,
      timeout: this.timeout,
      hasApiKey: !!this.apiKey,
    };
  }
}
