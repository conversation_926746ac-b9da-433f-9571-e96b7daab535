/**
 * Enum định nghĩa các job name cho Zalo webhook processing
 * Đ<PERSON>ợc sử dụng trong ZaloWebhookProcessor để route jobs đến appropriate handlers
 *
 * Note: Enum này được đồng bộ với ZaloQueueJobName trong redai-v201-be-app
 * Dựa trên 82 event types từ redai-zalo-sdk/src/types/webhook.ts
 */
export enum ZaloWebhookJobName {
  // =============================================================================
  // USER MESSAGE JOBS (High Priority) - 9 events
  // =============================================================================

  /**
   * Job xử lý user text messages
   * Events: user_send_text
   */
  PROCESS_USER_TEXT_MESSAGE = 'process-user-text-message',

  /**
   * Job xử lý user media messages (image, video, audio, gif, file)
   * Events: user_send_image, user_send_video, user_send_audio, user_send_gif, user_send_file
   */
  PROCESS_USER_MEDIA_MESSAGE = 'process-user-media-message',

  /**
   * Job xử lý user location/link/sticker messages
   * Events: user_send_location, user_send_link, user_send_sticker
   */
  PROCESS_USER_SPECIAL_MESSAGE = 'process-user-special-message',

  /**
   * Job xử lý user business card
   * Events: user_send_business_card
   */
  PROCESS_USER_BUSINESS_CARD = 'process-user-business-card',

  // =============================================================================
  // USER GROUP MESSAGE JOBS (High Priority) - 10 events
  // =============================================================================

  /**
   * Job xử lý user group text messages
   * Events: user_send_group_text
   */
  PROCESS_USER_GROUP_TEXT = 'process-user-group-text',

  /**
   * Job xử lý user group media messages
   * Events: user_send_group_image, user_send_group_video, user_send_group_audio, user_send_group_gif, user_send_group_file
   */
  PROCESS_USER_GROUP_MEDIA = 'process-user-group-media',

  /**
   * Job xử lý user group special messages
   * Events: user_send_group_location, user_send_group_link, user_send_group_sticker, user_send_group_business_card
   */
  PROCESS_USER_GROUP_SPECIAL = 'process-user-group-special',

  // =============================================================================
  // OA MESSAGE JOBS (Medium Priority) - 16 events
  // =============================================================================

  /**
   * Job xử lý OA direct messages
   * Events: oa_send_text, oa_send_image, oa_send_list, oa_send_gif, oa_send_file, oa_send_sticker
   */
  PROCESS_OA_DIRECT_MESSAGE = 'process-oa-direct-message',

  /**
   * Job xử lý OA group messages
   * Events: oa_send_group_text, oa_send_group_image, oa_send_group_link, oa_send_group_audio,
   *         oa_send_group_location, oa_send_group_video, oa_send_group_business_card,
   *         oa_send_group_sticker, oa_send_group_gif, oa_send_group_file
   */
  PROCESS_OA_GROUP_MESSAGE = 'process-oa-group-message',

  // =============================================================================
  // ANONYMOUS MESSAGE JOBS (Medium Priority) - 8 events
  // =============================================================================

  /**
   * Job xử lý anonymous messages từ user
   * Events: anonymous_send_text, anonymous_send_image, anonymous_send_file, anonymous_send_sticker
   */
  PROCESS_ANONYMOUS_USER_MESSAGE = 'process-anonymous-user-message',

  /**
   * Job xử lý anonymous messages từ OA
   * Events: oa_send_anonymous_text, oa_send_anonymous_image, oa_send_anonymous_file, oa_send_anonymous_sticker
   */
  PROCESS_ANONYMOUS_OA_MESSAGE = 'process-anonymous-oa-message',

  // =============================================================================
  // USER INTERACTION JOBS (High Priority) - 5 events
  // =============================================================================

  /**
   * Job xử lý user interactions
   * Events: user_click_chatnow, user_reacted_message, oa_reacted_message
   */
  PROCESS_USER_INTERACTION = 'process-user-interaction',

  /**
   * Job xử lý message status tracking
   * Events: user_received_message, user_seen_message
   */
  PROCESS_MESSAGE_STATUS = 'process-message-status',

  // =============================================================================
  // FOLLOW/UNFOLLOW JOBS (High Priority) - 2 events
  // =============================================================================

  /**
   * Job xử lý follow/unfollow events
   * Events: follow, unfollow
   */
  PROCESS_FOLLOW_EVENT = 'process-follow-event',

  // =============================================================================
  // BUSINESS LOGIC JOBS (Medium Priority) - 8 events
  // =============================================================================

  /**
   * Job xử lý shop orders
   * Events: shop_has_order
   */
  PROCESS_ORDER = 'process-order',

  /**
   * Job xử lý user info submissions/updates
   * Events: user_submit_info, update_user_info
   */
  PROCESS_USER_INFO = 'process-user-info',

  /**
   * Job xử lý user feedback
   * Events: user_feedback
   */
  PROCESS_FEEDBACK = 'process-feedback',

  /**
   * Job xử lý call events
   * Events: oa_call_user, user_call_oa
   */
  PROCESS_CALL_EVENT = 'process-call-event',

  /**
   * Job xử lý consent events
   * Events: oa_send_consent, user_reply_consent
   */
  PROCESS_CONSENT = 'process-consent',

  /**
   * Job xử lý template messages
   * Events: oa_send_template
   */
  PROCESS_TEMPLATE_MESSAGE = 'process-template-message',

  // =============================================================================
  // GROUP MANAGEMENT JOBS (Medium Priority) - 11 events
  // =============================================================================

  /**
   * Job xử lý group creation và basic management
   * Events: create_group, update_group_info, disperse_group
   */
  PROCESS_GROUP_BASIC_MANAGEMENT = 'process-group-basic-management',

  /**
   * Job xử lý group member management
   * Events: user_join_group, user_request_join_group, react_request_join_group,
   *         reject_request_join_group, user_out_group
   */
  PROCESS_GROUP_MEMBER_MANAGEMENT = 'process-group-member-management',

  /**
   * Job xử lý group admin management
   * Events: add_group_admin, remove_group_admin
   */
  PROCESS_GROUP_ADMIN_MANAGEMENT = 'process-group-admin-management',

  // =============================================================================
  // TEMPLATE & ZNS JOBS (Low Priority) - 8 events
  // =============================================================================

  /**
   * Job xử lý template quota và quality changes
   * Events: change_oa_daily_quota, change_template_quality, change_template_quota, change_template_status
   */
  PROCESS_TEMPLATE_CHANGES = 'process-template-changes',

  /**
   * Job xử lý template tags changes
   * Events: change_oa_template_tags
   */
  PROCESS_TEMPLATE_TAGS = 'process-template-tags',

  /**
   * Job xử lý ZNS message delivery
   * Events: user_received_zns_message
   */
  PROCESS_ZNS_DELIVERY = 'process-zns-delivery',

  // =============================================================================
  // JOURNEY & BUSINESS FLOW JOBS (Low Priority) - 2 events
  // =============================================================================

  /**
   * Job xử lý journey events
   * Events: event_journey_time_out, event_journey_acknowledged
   */
  PROCESS_JOURNEY_EVENT = 'process-journey-event',

  // =============================================================================
  // SYSTEM & WIDGET JOBS (Lowest Priority) - 6 events
  // =============================================================================

  /**
   * Job xử lý widget interactions
   * Events: widget_interaction_accepted, widget_failed_to_sync_user_external_id
   */
  PROCESS_WIDGET_EVENT = 'process-widget-event',

  /**
   * Job xử lý permission và extension events
   * Events: permission_revoked, extension_purchased
   */
  PROCESS_PERMISSION_EVENT = 'process-permission-event',

  // =============================================================================
  // ANALYTICS & TRACKING JOBS (Lowest Priority) - 2 events
  // =============================================================================

  /**
   * Job tracking user tags
   * Events: add_user_to_tag
   */
  PROCESS_USER_TAG = 'process-user-tag',

  // =============================================================================
  // LEGACY JOBS (Backward Compatibility)
  // =============================================================================

  /**
   * Legacy job names for backward compatibility
   */
  PROCESS_USER_MESSAGE = 'process-user-message', // Maps to multiple new jobs
  TRACK_MESSAGE_STATUS = 'track-message-status', // Maps to PROCESS_MESSAGE_STATUS
  TRACK_OA_MESSAGE = 'track-oa-message', // Maps to PROCESS_OA_DIRECT_MESSAGE
  TRACK_INTERACTION = 'track-interaction', // Maps to PROCESS_USER_INTERACTION
  PROCESS_TEMPLATE_EVENT = 'process-template-event', // Maps to PROCESS_TEMPLATE_CHANGES
  PROCESS_SYSTEM_EVENT = 'process-system-event', // Maps to PROCESS_WIDGET_EVENT + PROCESS_PERMISSION_EVENT
  PROCESS_GROUP_MANAGEMENT = 'process-group-management', // Maps to group management jobs
}

/**
 * Helper function để lấy priority level của job
 */
export function getJobPriority(jobName: ZaloWebhookJobName): number {
  switch (jobName) {
    // High Priority (150-199) - Real-time user interactions
    case ZaloWebhookJobName.PROCESS_USER_TEXT_MESSAGE:
    case ZaloWebhookJobName.PROCESS_USER_MEDIA_MESSAGE:
    case ZaloWebhookJobName.PROCESS_USER_SPECIAL_MESSAGE:
    case ZaloWebhookJobName.PROCESS_USER_BUSINESS_CARD:
    case ZaloWebhookJobName.PROCESS_USER_GROUP_TEXT:
    case ZaloWebhookJobName.PROCESS_USER_GROUP_MEDIA:
    case ZaloWebhookJobName.PROCESS_USER_GROUP_SPECIAL:
    case ZaloWebhookJobName.PROCESS_USER_INTERACTION:
    case ZaloWebhookJobName.PROCESS_MESSAGE_STATUS:
    case ZaloWebhookJobName.PROCESS_FOLLOW_EVENT:
      return 180;

    // Medium-High Priority (100-149) - OA messages and business logic
    case ZaloWebhookJobName.PROCESS_OA_DIRECT_MESSAGE:
    case ZaloWebhookJobName.PROCESS_OA_GROUP_MESSAGE:
    case ZaloWebhookJobName.PROCESS_ANONYMOUS_USER_MESSAGE:
    case ZaloWebhookJobName.PROCESS_ANONYMOUS_OA_MESSAGE:
    case ZaloWebhookJobName.PROCESS_ORDER:
    case ZaloWebhookJobName.PROCESS_USER_INFO:
    case ZaloWebhookJobName.PROCESS_FEEDBACK:
    case ZaloWebhookJobName.PROCESS_CALL_EVENT:
    case ZaloWebhookJobName.PROCESS_CONSENT:
    case ZaloWebhookJobName.PROCESS_TEMPLATE_MESSAGE:
      return 120;

    // Medium Priority (50-99) - Group management
    case ZaloWebhookJobName.PROCESS_GROUP_BASIC_MANAGEMENT:
    case ZaloWebhookJobName.PROCESS_GROUP_MEMBER_MANAGEMENT:
    case ZaloWebhookJobName.PROCESS_GROUP_ADMIN_MANAGEMENT:
      return 75;

    // Low Priority (25-49) - Template and ZNS
    case ZaloWebhookJobName.PROCESS_TEMPLATE_CHANGES:
    case ZaloWebhookJobName.PROCESS_TEMPLATE_TAGS:
    case ZaloWebhookJobName.PROCESS_ZNS_DELIVERY:
    case ZaloWebhookJobName.PROCESS_JOURNEY_EVENT:
      return 35;

    // Lowest Priority (1-24) - System events and analytics
    case ZaloWebhookJobName.PROCESS_WIDGET_EVENT:
    case ZaloWebhookJobName.PROCESS_PERMISSION_EVENT:
    case ZaloWebhookJobName.PROCESS_USER_TAG:
      return 10;

    // Legacy jobs (backward compatibility)
    case ZaloWebhookJobName.PROCESS_USER_MESSAGE:
      return 180; // Same as user message jobs
    case ZaloWebhookJobName.TRACK_MESSAGE_STATUS:
      return 180; // Same as message status
    case ZaloWebhookJobName.TRACK_OA_MESSAGE:
      return 120; // Same as OA messages
    case ZaloWebhookJobName.TRACK_INTERACTION:
      return 180; // Same as user interaction
    case ZaloWebhookJobName.PROCESS_TEMPLATE_EVENT:
      return 35; // Same as template changes
    case ZaloWebhookJobName.PROCESS_SYSTEM_EVENT:
      return 10; // Same as system events
    case ZaloWebhookJobName.PROCESS_GROUP_MANAGEMENT:
      return 75; // Same as group management

    default:
      return 50; // Default medium priority
  }
}

/**
 * Helper function để lấy category của job
 */
export function getJobCategory(jobName: ZaloWebhookJobName): string {
  switch (jobName) {
    // User message categories
    case ZaloWebhookJobName.PROCESS_USER_TEXT_MESSAGE:
    case ZaloWebhookJobName.PROCESS_USER_MEDIA_MESSAGE:
    case ZaloWebhookJobName.PROCESS_USER_SPECIAL_MESSAGE:
    case ZaloWebhookJobName.PROCESS_USER_BUSINESS_CARD:
      return 'user-messages';

    case ZaloWebhookJobName.PROCESS_USER_GROUP_TEXT:
    case ZaloWebhookJobName.PROCESS_USER_GROUP_MEDIA:
    case ZaloWebhookJobName.PROCESS_USER_GROUP_SPECIAL:
      return 'user-group-messages';

    // OA message categories
    case ZaloWebhookJobName.PROCESS_OA_DIRECT_MESSAGE:
    case ZaloWebhookJobName.PROCESS_OA_GROUP_MESSAGE:
      return 'oa-messages';

    // Anonymous message categories
    case ZaloWebhookJobName.PROCESS_ANONYMOUS_USER_MESSAGE:
    case ZaloWebhookJobName.PROCESS_ANONYMOUS_OA_MESSAGE:
      return 'anonymous-messages';

    // Interaction categories
    case ZaloWebhookJobName.PROCESS_USER_INTERACTION:
    case ZaloWebhookJobName.PROCESS_MESSAGE_STATUS:
    case ZaloWebhookJobName.PROCESS_FOLLOW_EVENT:
      return 'user-interactions';

    // Business logic categories
    case ZaloWebhookJobName.PROCESS_ORDER:
    case ZaloWebhookJobName.PROCESS_USER_INFO:
    case ZaloWebhookJobName.PROCESS_FEEDBACK:
    case ZaloWebhookJobName.PROCESS_CALL_EVENT:
    case ZaloWebhookJobName.PROCESS_CONSENT:
    case ZaloWebhookJobName.PROCESS_TEMPLATE_MESSAGE:
      return 'business-logic';

    // Group management categories
    case ZaloWebhookJobName.PROCESS_GROUP_BASIC_MANAGEMENT:
    case ZaloWebhookJobName.PROCESS_GROUP_MEMBER_MANAGEMENT:
    case ZaloWebhookJobName.PROCESS_GROUP_ADMIN_MANAGEMENT:
      return 'group-management';

    // Template and ZNS categories
    case ZaloWebhookJobName.PROCESS_TEMPLATE_CHANGES:
    case ZaloWebhookJobName.PROCESS_TEMPLATE_TAGS:
    case ZaloWebhookJobName.PROCESS_ZNS_DELIVERY:
      return 'template-zns';

    // Journey categories
    case ZaloWebhookJobName.PROCESS_JOURNEY_EVENT:
      return 'journey';

    // System categories
    case ZaloWebhookJobName.PROCESS_WIDGET_EVENT:
    case ZaloWebhookJobName.PROCESS_PERMISSION_EVENT:
      return 'system';

    // Analytics categories
    case ZaloWebhookJobName.PROCESS_USER_TAG:
      return 'analytics';

    // Legacy categories (backward compatibility)
    case ZaloWebhookJobName.PROCESS_USER_MESSAGE:
      return 'user-messages';
    case ZaloWebhookJobName.TRACK_MESSAGE_STATUS:
      return 'user-interactions';
    case ZaloWebhookJobName.TRACK_OA_MESSAGE:
      return 'oa-messages';
    case ZaloWebhookJobName.TRACK_INTERACTION:
      return 'user-interactions';
    case ZaloWebhookJobName.PROCESS_TEMPLATE_EVENT:
      return 'template-zns';
    case ZaloWebhookJobName.PROCESS_SYSTEM_EVENT:
      return 'system';
    case ZaloWebhookJobName.PROCESS_GROUP_MANAGEMENT:
      return 'group-management';

    default:
      return 'unknown';
  }
}

/**
 * Tất cả job names dưới dạng array để dễ iterate
 */
export const ALL_ZALO_WEBHOOK_JOB_NAMES = Object.values(ZaloWebhookJobName);

/**
 * Job names theo category
 */
export const ZALO_WEBHOOK_JOBS_BY_CATEGORY = {
  'user-messages': [
    ZaloWebhookJobName.PROCESS_USER_TEXT_MESSAGE,
    ZaloWebhookJobName.PROCESS_USER_MEDIA_MESSAGE,
    ZaloWebhookJobName.PROCESS_USER_SPECIAL_MESSAGE,
    ZaloWebhookJobName.PROCESS_USER_BUSINESS_CARD,
  ],
  'user-group-messages': [
    ZaloWebhookJobName.PROCESS_USER_GROUP_TEXT,
    ZaloWebhookJobName.PROCESS_USER_GROUP_MEDIA,
    ZaloWebhookJobName.PROCESS_USER_GROUP_SPECIAL,
  ],
  'oa-messages': [
    ZaloWebhookJobName.PROCESS_OA_DIRECT_MESSAGE,
    ZaloWebhookJobName.PROCESS_OA_GROUP_MESSAGE,
  ],
  'anonymous-messages': [
    ZaloWebhookJobName.PROCESS_ANONYMOUS_USER_MESSAGE,
    ZaloWebhookJobName.PROCESS_ANONYMOUS_OA_MESSAGE,
  ],
  'user-interactions': [
    ZaloWebhookJobName.PROCESS_USER_INTERACTION,
    ZaloWebhookJobName.PROCESS_MESSAGE_STATUS,
    ZaloWebhookJobName.PROCESS_FOLLOW_EVENT,
  ],
  'business-logic': [
    ZaloWebhookJobName.PROCESS_ORDER,
    ZaloWebhookJobName.PROCESS_USER_INFO,
    ZaloWebhookJobName.PROCESS_FEEDBACK,
    ZaloWebhookJobName.PROCESS_CALL_EVENT,
    ZaloWebhookJobName.PROCESS_CONSENT,
    ZaloWebhookJobName.PROCESS_TEMPLATE_MESSAGE,
  ],
  'group-management': [
    ZaloWebhookJobName.PROCESS_GROUP_BASIC_MANAGEMENT,
    ZaloWebhookJobName.PROCESS_GROUP_MEMBER_MANAGEMENT,
    ZaloWebhookJobName.PROCESS_GROUP_ADMIN_MANAGEMENT,
  ],
  'template-zns': [
    ZaloWebhookJobName.PROCESS_TEMPLATE_CHANGES,
    ZaloWebhookJobName.PROCESS_TEMPLATE_TAGS,
    ZaloWebhookJobName.PROCESS_ZNS_DELIVERY,
  ],
  'journey': [
    ZaloWebhookJobName.PROCESS_JOURNEY_EVENT,
  ],
  'system': [
    ZaloWebhookJobName.PROCESS_WIDGET_EVENT,
    ZaloWebhookJobName.PROCESS_PERMISSION_EVENT,
  ],
  'analytics': [
    ZaloWebhookJobName.PROCESS_USER_TAG,
  ],
};

/**
 * Mapping từ Zalo event types sang job names
 * Dựa trên ZaloWebhookEventType từ SDK
 */
export const ZALO_EVENT_TO_JOB_MAPPING: Record<string, ZaloWebhookJobName> = {
  // User message events -> User message jobs
  'user_send_text': ZaloWebhookJobName.PROCESS_USER_TEXT_MESSAGE,
  'user_send_image': ZaloWebhookJobName.PROCESS_USER_MEDIA_MESSAGE,
  'user_send_video': ZaloWebhookJobName.PROCESS_USER_MEDIA_MESSAGE,
  'user_send_audio': ZaloWebhookJobName.PROCESS_USER_MEDIA_MESSAGE,
  'user_send_gif': ZaloWebhookJobName.PROCESS_USER_MEDIA_MESSAGE,
  'user_send_file': ZaloWebhookJobName.PROCESS_USER_MEDIA_MESSAGE,
  'user_send_location': ZaloWebhookJobName.PROCESS_USER_SPECIAL_MESSAGE,
  'user_send_link': ZaloWebhookJobName.PROCESS_USER_SPECIAL_MESSAGE,
  'user_send_sticker': ZaloWebhookJobName.PROCESS_USER_SPECIAL_MESSAGE,
  'user_send_business_card': ZaloWebhookJobName.PROCESS_USER_BUSINESS_CARD,

  // User group message events -> User group message jobs
  'user_send_group_text': ZaloWebhookJobName.PROCESS_USER_GROUP_TEXT,
  'user_send_group_image': ZaloWebhookJobName.PROCESS_USER_GROUP_MEDIA,
  'user_send_group_video': ZaloWebhookJobName.PROCESS_USER_GROUP_MEDIA,
  'user_send_group_audio': ZaloWebhookJobName.PROCESS_USER_GROUP_MEDIA,
  'user_send_group_gif': ZaloWebhookJobName.PROCESS_USER_GROUP_MEDIA,
  'user_send_group_file': ZaloWebhookJobName.PROCESS_USER_GROUP_MEDIA,
  'user_send_group_location': ZaloWebhookJobName.PROCESS_USER_GROUP_SPECIAL,
  'user_send_group_link': ZaloWebhookJobName.PROCESS_USER_GROUP_SPECIAL,
  'user_send_group_sticker': ZaloWebhookJobName.PROCESS_USER_GROUP_SPECIAL,
  'user_send_group_business_card': ZaloWebhookJobName.PROCESS_USER_GROUP_SPECIAL,

  // OA message events -> OA message jobs
  'oa_send_text': ZaloWebhookJobName.PROCESS_OA_DIRECT_MESSAGE,
  'oa_send_image': ZaloWebhookJobName.PROCESS_OA_DIRECT_MESSAGE,
  'oa_send_list': ZaloWebhookJobName.PROCESS_OA_DIRECT_MESSAGE,
  'oa_send_gif': ZaloWebhookJobName.PROCESS_OA_DIRECT_MESSAGE,
  'oa_send_file': ZaloWebhookJobName.PROCESS_OA_DIRECT_MESSAGE,
  'oa_send_sticker': ZaloWebhookJobName.PROCESS_OA_DIRECT_MESSAGE,
  'oa_send_template': ZaloWebhookJobName.PROCESS_TEMPLATE_MESSAGE,

  // OA group message events -> OA group message jobs
  'oa_send_group_text': ZaloWebhookJobName.PROCESS_OA_GROUP_MESSAGE,
  'oa_send_group_image': ZaloWebhookJobName.PROCESS_OA_GROUP_MESSAGE,
  'oa_send_group_link': ZaloWebhookJobName.PROCESS_OA_GROUP_MESSAGE,
  'oa_send_group_audio': ZaloWebhookJobName.PROCESS_OA_GROUP_MESSAGE,
  'oa_send_group_location': ZaloWebhookJobName.PROCESS_OA_GROUP_MESSAGE,
  'oa_send_group_video': ZaloWebhookJobName.PROCESS_OA_GROUP_MESSAGE,
  'oa_send_group_business_card': ZaloWebhookJobName.PROCESS_OA_GROUP_MESSAGE,
  'oa_send_group_sticker': ZaloWebhookJobName.PROCESS_OA_GROUP_MESSAGE,
  'oa_send_group_gif': ZaloWebhookJobName.PROCESS_OA_GROUP_MESSAGE,
  'oa_send_group_file': ZaloWebhookJobName.PROCESS_OA_GROUP_MESSAGE,

  // Anonymous message events -> Anonymous message jobs
  'anonymous_send_text': ZaloWebhookJobName.PROCESS_ANONYMOUS_USER_MESSAGE,
  'anonymous_send_image': ZaloWebhookJobName.PROCESS_ANONYMOUS_USER_MESSAGE,
  'anonymous_send_file': ZaloWebhookJobName.PROCESS_ANONYMOUS_USER_MESSAGE,
  'anonymous_send_sticker': ZaloWebhookJobName.PROCESS_ANONYMOUS_USER_MESSAGE,
  'oa_send_anonymous_text': ZaloWebhookJobName.PROCESS_ANONYMOUS_OA_MESSAGE,
  'oa_send_anonymous_image': ZaloWebhookJobName.PROCESS_ANONYMOUS_OA_MESSAGE,
  'oa_send_anonymous_file': ZaloWebhookJobName.PROCESS_ANONYMOUS_OA_MESSAGE,
  'oa_send_anonymous_sticker': ZaloWebhookJobName.PROCESS_ANONYMOUS_OA_MESSAGE,

  // User interaction events -> User interaction jobs
  'user_click_chatnow': ZaloWebhookJobName.PROCESS_USER_INTERACTION,
  'user_reacted_message': ZaloWebhookJobName.PROCESS_USER_INTERACTION,
  'oa_reacted_message': ZaloWebhookJobName.PROCESS_USER_INTERACTION,
  'user_received_message': ZaloWebhookJobName.PROCESS_MESSAGE_STATUS,
  'user_seen_message': ZaloWebhookJobName.PROCESS_MESSAGE_STATUS,

  // Follow/Unfollow events -> Follow event jobs
  'follow': ZaloWebhookJobName.PROCESS_FOLLOW_EVENT,
  'unfollow': ZaloWebhookJobName.PROCESS_FOLLOW_EVENT,

  // Business logic events -> Business logic jobs
  'shop_has_order': ZaloWebhookJobName.PROCESS_ORDER,
  'user_submit_info': ZaloWebhookJobName.PROCESS_USER_INFO,
  'update_user_info': ZaloWebhookJobName.PROCESS_USER_INFO,
  'user_feedback': ZaloWebhookJobName.PROCESS_FEEDBACK,
  'oa_call_user': ZaloWebhookJobName.PROCESS_CALL_EVENT,
  'user_call_oa': ZaloWebhookJobName.PROCESS_CALL_EVENT,
  'oa_send_consent': ZaloWebhookJobName.PROCESS_CONSENT,
  'user_reply_consent': ZaloWebhookJobName.PROCESS_CONSENT,

  // Group management events -> Group management jobs
  'create_group': ZaloWebhookJobName.PROCESS_GROUP_BASIC_MANAGEMENT,
  'update_group_info': ZaloWebhookJobName.PROCESS_GROUP_BASIC_MANAGEMENT,
  'disperse_group': ZaloWebhookJobName.PROCESS_GROUP_BASIC_MANAGEMENT,
  'user_join_group': ZaloWebhookJobName.PROCESS_GROUP_MEMBER_MANAGEMENT,
  'user_request_join_group': ZaloWebhookJobName.PROCESS_GROUP_MEMBER_MANAGEMENT,
  'react_request_join_group': ZaloWebhookJobName.PROCESS_GROUP_MEMBER_MANAGEMENT,
  'reject_request_join_group': ZaloWebhookJobName.PROCESS_GROUP_MEMBER_MANAGEMENT,
  'user_out_group': ZaloWebhookJobName.PROCESS_GROUP_MEMBER_MANAGEMENT,
  'add_group_admin': ZaloWebhookJobName.PROCESS_GROUP_ADMIN_MANAGEMENT,
  'remove_group_admin': ZaloWebhookJobName.PROCESS_GROUP_ADMIN_MANAGEMENT,

  // Template and ZNS events -> Template/ZNS jobs
  'change_oa_daily_quota': ZaloWebhookJobName.PROCESS_TEMPLATE_CHANGES,
  'change_template_quality': ZaloWebhookJobName.PROCESS_TEMPLATE_CHANGES,
  'change_template_quota': ZaloWebhookJobName.PROCESS_TEMPLATE_CHANGES,
  'change_template_status': ZaloWebhookJobName.PROCESS_TEMPLATE_CHANGES,
  'change_oa_template_tags': ZaloWebhookJobName.PROCESS_TEMPLATE_TAGS,
  'user_received_zns_message': ZaloWebhookJobName.PROCESS_ZNS_DELIVERY,

  // Journey events -> Journey jobs
  'event_journey_time_out': ZaloWebhookJobName.PROCESS_JOURNEY_EVENT,
  'event_journey_acknowledged': ZaloWebhookJobName.PROCESS_JOURNEY_EVENT,

  // System events -> System jobs
  'widget_interaction_accepted': ZaloWebhookJobName.PROCESS_WIDGET_EVENT,
  'widget_failed_to_sync_user_external_id': ZaloWebhookJobName.PROCESS_WIDGET_EVENT,
  'permission_revoked': ZaloWebhookJobName.PROCESS_PERMISSION_EVENT,
  'extension_purchased': ZaloWebhookJobName.PROCESS_PERMISSION_EVENT,

  // Analytics events -> Analytics jobs
  'add_user_to_tag': ZaloWebhookJobName.PROCESS_USER_TAG,
};

/**
 * Helper function để get job name từ event type
 */
export function getJobNameFromEventType(eventType: string): ZaloWebhookJobName | null {
  return ZALO_EVENT_TO_JOB_MAPPING[eventType] || null;
}

/**
 * Validate job name
 */
export function isValidZaloJobName(jobName: string): jobName is ZaloWebhookJobName {
  return Object.values(ZaloWebhookJobName).includes(jobName as ZaloWebhookJobName);
}
