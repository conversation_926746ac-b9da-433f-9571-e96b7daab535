import { Platform } from 'src/modules/agents/enums';
import { EmployeeInfo, UserInfo } from 'src/modules/agents/interfaces';

export interface InAppJobData {
  runId: string;
  threadId: string;
  mainAgentId: string;
  keys: {
    platformThreadId: string; // "in_app:thread-123"
    runStatusKey: string; // "run_status:in_app:thread-123"
    streamKey: string; // "in_app:agent_stream:thread-123:run-456"
  };
  humanInfo: {
    user?: UserInfo | undefined;
    employee?: EmployeeInfo | undefined;
  };
  jwt: string;
  chatWithSystem: boolean;
  alwaysApproveToolCall: boolean;
  toolCallDecision?: 'yes' | 'no' | 'always';
  workerAgents?: Array<{id: string, prompt?: string}>;
  platform: Platform.IN_APP;
  webSearchEnabled?: boolean;
}
