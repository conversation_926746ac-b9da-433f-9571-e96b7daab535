import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { ZaloMessage } from '../entities/zalo-message.entity';

/**
 * Repository cho ZaloMessage entity trong worker
 */
@Injectable()
export class ZaloMessageRepository extends Repository<ZaloMessage> {
  private readonly logger = new Logger(ZaloMessageRepository.name);

  constructor(private dataSource: DataSource) {
    super(ZaloMessage, dataSource.createEntityManager());
  }

  /**
   * Tìm tin nhắn theo messageId và oaId
   */
  async findByMessageIdAndOaId(
    messageId: string,
    oaId: string,
  ): Promise<ZaloMessage | null> {
    try {
      return await this.findOne({
        where: {
          messageId,
          oaId,
        },
      });
    } catch (error) {
      this.logger.error(`Error finding message by messageId and oaId: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Tìm tin nhắn theo userId và oaId
   */
  async findByUserIdAndOaId(
    userId: string,
    oaId: string,
    limit: number = 20,
    offset: number = 0,
  ): Promise<ZaloMessage[]> {
    try {
      return await this.find({
        where: {
          userId,
          oaId,
        },
        order: {
          timestamp: 'DESC',
        },
        take: limit,
        skip: offset,
      });
    } catch (error) {
      this.logger.error(`Error finding messages by userId and oaId: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * Đếm số lượng tin nhắn theo userId và oaId
   */
  async countByUserIdAndOaId(
    userId: string,
    oaId: string,
  ): Promise<number> {
    try {
      return await this.count({
        where: {
          userId,
          oaId,
        },
      });
    } catch (error) {
      this.logger.error(`Error counting messages by userId and oaId: ${error.message}`, error.stack);
      return 0;
    }
  }

  /**
   * Tạo tin nhắn mới
   */
  async createMessage(messageData: Partial<ZaloMessage>): Promise<ZaloMessage> {
    try {
      const message = this.create({
        ...messageData,
        createdAt: messageData.createdAt || Date.now(),
        updatedAt: Date.now(),
      });
      return await this.save(message);
    } catch (error) {
      this.logger.error(`Error creating message: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cập nhật tin nhắn
   */
  async updateMessage(id: number, messageData: Partial<ZaloMessage>): Promise<void> {
    try {
      await this.update(id, {
        ...messageData,
        updatedAt: Date.now(),
      });
    } catch (error) {
      this.logger.error(`Error updating message: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy tin nhắn gần nhất theo integrationId
   */
  async findRecentByIntegrationId(
    integrationId: string,
    limit: number = 50,
  ): Promise<ZaloMessage[]> {
    try {
      return await this.find({
        where: {
          integrationId,
        },
        order: {
          timestamp: 'DESC',
        },
        take: limit,
      });
    } catch (error) {
      this.logger.error(`Error finding recent messages by integrationId: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * Xóa tin nhắn cũ (để cleanup)
   */
  async deleteOldMessages(olderThanTimestamp: number): Promise<number> {
    try {
      const result = await this.createQueryBuilder()
        .delete()
        .from(ZaloMessage)
        .where('timestamp < :timestamp', { timestamp: olderThanTimestamp })
        .execute();
      
      this.logger.log(`Deleted ${result.affected} old messages`);
      return result.affected || 0;
    } catch (error) {
      this.logger.error(`Error deleting old messages: ${error.message}`, error.stack);
      return 0;
    }
  }
}
