import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng zalo_zns_templates trong cơ sở dữ liệu
 * Lưu trữ thông tin về các template ZNS (Zalo Notification Service) mà người dùng đã tạo hoặc được cấp quyền sử dụng
 */
@Entity('zalo_zns_templates')
export class ZaloZnsTemplate {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * ID người dùng sở hữu template
   */
  @Column({ name: 'user_id' })
  userId: number;

  /**
   * ID của Official Account
   */
  @Column({ name: 'oa_id', length: 50 })
  oaId: string;

  /**
   * ID của template trên Zalo
   */
  @Column({ name: 'template_id', length: 50 })
  templateId: string;

  /**
   * Tên của template
   */
  @Column({ name: 'template_name', length: 255 })
  templateName: string;

  /**
   * Nội dung của template
   */
  @Column({ name: 'template_content', type: 'text' })
  templateContent: string;

  /**
   * Loại template (text, media, list, button)
   */
  @Column({ name: 'template_type', length: 20, nullable: true })
  templateType: string;

  /**
   * Trạng thái của template (approved, pending, rejected)
   */
  @Column({ name: 'status', length: 20 })
  status: string;

  /**
   * Thời gian timeout (milliseconds)
   */
  @Column({ name: 'timeout', type: 'bigint', nullable: true })
  timeout?: number;

  /**
   * URL preview template
   */
  @Column({ name: 'preview_url', type: 'text', nullable: true })
  previewUrl?: string;

  /**
   * Chất lượng template
   */
  @Column({ name: 'template_quality', length: 50, nullable: true })
  templateQuality?: string;

  /**
   * Tag template (TRANSACTION, CUSTOMER_CARE, PROMOTION)
   */
  @Column({ name: 'template_tag', length: 50, nullable: true })
  templateTag?: string;

  /**
   * Giá template
   */
  @Column({
    name: 'price',
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true,
  })
  price?: number;

  /**
   * Có áp dụng template quota không
   */
  @Column({ name: 'apply_template_quota', type: 'boolean', default: false })
  applyTemplateQuota: boolean;

  /**
   * Lý do (khi pending/rejected)
   */
  @Column({ name: 'reason', type: 'text', nullable: true })
  reason?: string;

  /**
   * Chi tiết params với validation rules (JSON)
   */
  @Column({ name: 'list_params', type: 'jsonb', nullable: true })
  listParams?: any[];

  /**
   * Thời điểm tạo (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  /**
   * Thời điểm cập nhật (Unix timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;
}
