import { Injectable, Logger } from '@nestjs/common';
import { Connection, MultiServerMCPClient } from '@langchain/mcp-adapters';
import { EncryptionService } from 'src/shared/services/encryption/encryption.service';
import { McpConfiguration } from '../interfaces';
import { Platform } from '../enums/platform.enum';
import { env } from 'src/config';

@Injectable()
export class McpClientService {
  private readonly logger = new Logger(McpClientService.name);

  constructor(private readonly encryptionService: EncryptionService) {}
  async buildMcpClients(
    mcpConfig: Record<string, McpConfiguration> | null,
    additionalHeaders?: Record<string, string>,
    platform?: Platform,
    runtimeCredentials?: Record<string, string>,
  ): Promise<MultiServerMCPClient | undefined> {
    this.logger.debug('🔧 Building MCP clients', {
      mcpConfigExists: !!mcpConfig,
      mcpServerCount: mcpConfig ? Object.keys(mcpConfig).length : 0,
      platform,
      hasAdditionalHeaders: !!additionalHeaders,
      additionalHeadersCount: additionalHeaders
        ? Object.keys(additionalHeaders).length
        : 0,
      hasRuntimeCredentials: !!runtimeCredentials,
      runtimeCredentialsKeys: runtimeCredentials
        ? Object.keys(runtimeCredentials)
        : [],
    });

    if (!mcpConfig) {
      this.logger.debug('❌ No MCP config provided, returning undefined');
      return undefined;
    }

    if (Object.keys(mcpConfig).length === 0) {
      this.logger.debug('❌ Empty MCP config, returning undefined');
      return undefined;
    }

    const mcpServers: Record<string, Connection> = {};

    for (const mcpServerName of Object.keys(mcpConfig)) {
      const mcpConfiguration: McpConfiguration = mcpConfig[mcpServerName];
      if (!mcpConfiguration) {
        this.logger.warn(
          `MCP configuration missing for server ${mcpServerName}`,
        );
        continue;
      }
      if (!mcpConfiguration.url) {
        this.logger.warn(
          `MCP configuration missing url for server ${mcpServerName}`,
        );
        continue;
      }
      if (!mcpConfiguration.transport) {
        this.logger.warn(
          `MCP configuration missing transport for server ${mcpServerName}`,
        );
        continue;
      }
      switch (mcpConfiguration.transport) {
        case 'http':
          // step 1: resolve header
          // step 1.1: decrypt base header
          const { encryptedHeaderString, secretKey: secretKeyPublic } =
            mcpConfiguration;
          const baseHeaders =
            encryptedHeaderString && secretKeyPublic
              ? this.encryptionService.decrypt<Record<string, string>>(
                  secretKeyPublic,
                  env.keyPairEncryption.KEY_PAIR_PRIVATE_KEY,
                  encryptedHeaderString,
                )
              : {};
          const runtimeHeaders = this.buildRuntimeHeaders(
            platform,
            runtimeCredentials,
          );
          const fullHeaders = {
            ...baseHeaders,
            ...additionalHeaders, // Non-auth headers
            ...runtimeHeaders, // Auth headers (highest priority)
          };

          this.logger.debug(
            `🔗 Building connection for MCP server: ${mcpServerName}`,
            {
              url: mcpConfiguration.url,
              transport: mcpConfiguration.transport,
              baseHeadersCount: Object.keys(baseHeaders).length,
              additionalHeadersCount: additionalHeaders
                ? Object.keys(additionalHeaders).length
                : 0,
              runtimeHeadersCount: Object.keys(runtimeHeaders).length,
              finalHeadersCount: Object.keys(fullHeaders).length,
              finalHeaderKeys: Object.keys(fullHeaders),
              // Don't log actual header values for security, just keys and counts
            },
          );
          const httpMcpConnection: Connection = {
            type: 'http',
            url: mcpConfiguration.url,
            transport: 'http',
            headers:
              Object.keys(fullHeaders).length > 0 ? fullHeaders : undefined,
          };
          mcpServers[mcpServerName] = httpMcpConnection;
          break;
        case 'sse':
          break;
        default:
          this.logger.warn(
            `MCP configuration invalid transport for server ${mcpServerName}`,
          );
          continue;
      }
    }

    if (Object.keys(mcpServers).length > 0) {
      this.logger.log(
        `✅ Successfully built MultiServerMCPClient with ${Object.keys(mcpServers).length} servers`,
        {
          serverNames: Object.keys(mcpServers),
        },
      );
      const mcpClient = new MultiServerMCPClient({
        throwOnLoadError: true,
        additionalToolNamePrefix: '',
        prefixToolNameWithServerName: false,
        useStandardContentBlocks: true,
        mcpServers,
      });
      return mcpClient;
    }

    this.logger.debug('❌ No valid MCP servers found, returning undefined');
    return undefined;
  }

  private buildRuntimeHeaders(
    platform?: Platform,
    runtimeCredentials?: Record<string, string>,
  ): Record<string, string> {
    this.logger.debug('🔑 Building runtime headers', {
      platform,
      hasRuntimeCredentials: !!runtimeCredentials,
      runtimeCredentialsKeys: runtimeCredentials
        ? Object.keys(runtimeCredentials)
        : [],
    });

    if (!platform || !runtimeCredentials) {
      this.logger.debug(
        '❌ No platform or runtime credentials, returning empty headers',
      );
      return {};
    }

    const headers: Record<string, string> = {};

    switch (platform) {
      case Platform.IN_APP:
        if (runtimeCredentials.jwt) {
          headers['Authorization'] = `Bearer ${runtimeCredentials.jwt}`;
          this.logger.debug(
            '✅ Added JWT Authorization header for IN_APP platform',
            {
              jwtLength: runtimeCredentials.jwt.length,
              jwtPrefix: runtimeCredentials.jwt.substring(0, 20) + '...',
            },
          );
        } else {
          this.logger.warn(
            '⚠️ IN_APP platform but no JWT in runtime credentials',
          );
        }
        break;

      // Add more platforms as needed
      default:
        this.logger.debug(
          `❓ Unknown platform: ${platform}, no runtime headers added`,
        );
        break;
    }

    this.logger.debug('🔑 Runtime headers built', {
      headerCount: Object.keys(headers).length,
      headerKeys: Object.keys(headers),
    });

    return headers;
  }
}
