/**
 * Redis Channel Patterns Enum
 * Tập trung quản lý tất cả <PERSON>is channel patterns cho workflow events
 */

/**
 * Redis Channel Patterns cho workflow events
 */
export enum RedisChannelPattern {

  /**
   * Pattern cho workflow node started events
   * Format: node.started.{nodeId}
   */
  NODE_STARTED = 'node.started.*',

  /**
   * Pattern cho workflow node processing events
   * Format: node.processing.{nodeId}
   */
  NODE_PROCESSING = 'node.processing.*',

  /**
   * Pattern cho workflow node completed events
   * Format: node.completed.{nodeId}
   */
  NODE_COMPLETED = 'node.completed.*',

  /**
   * Pattern cho workflow node failed events
   * Format: node.failed.{nodeId}
   */
  NODE_FAILED = 'node.failed.*',

  /**
   * Pattern cho workflow started events
   * Format: workflow.started.{workflowId}
   */
  WORKFLOW_STARTED = 'workflow.started.*',

  /**
   * Pattern cho workflow completed events
   * Format: workflow.completed.{workflowId}
   */
  WORKFLOW_COMPLETED = 'workflow.completed.*',

  /**
   * Pattern cho workflow failed events
   * Format: workflow.failed.{workflowId}
   */
  WORKFLOW_FAILED = 'workflow.failed.*',
}

/**
 * Redis Channel Builders - Tạo channel names cụ thể
 */
export class RedisChannelBuilder {
  /**
   * Tạo channel cho node started event
   */
  static buildNodeStartedChannel(nodeId: string): string {
    return `node.started.${nodeId}`;
  }

  /**
   * Tạo channel cho node processing event
   */
  static buildNodeProcessingChannel(nodeId: string): string {
    return `node.processing.${nodeId}`;
  }

  /**
   * Tạo channel cho node completed event
   */
  static buildNodeCompletedChannel(nodeId: string): string {
    return `node.completed.${nodeId}`;
  }

  /**
   * Tạo channel cho node failed event
   */
  static buildNodeFailedChannel(nodeId: string): string {
    return `node.failed.${nodeId}`;
  }

  /**
   * Tạo channel cho workflow started event
   */
  static buildWorkflowStartedChannel(workflowId: string): string {
    return `workflow.started.${workflowId}`;
  }

  /**
   * Tạo channel cho workflow completed event
   */
  static buildWorkflowCompletedChannel(workflowId: string): string {
    return `workflow.completed.${workflowId}`;
  }

  /**
   * Tạo channel cho workflow failed event
   */
  static buildWorkflowFailedChannel(workflowId: string): string {
    return `workflow.failed.${workflowId}`;
  }
}
