/**
 * @file Enhanced Workflow Actions with Conditional Support
 * 
 * Enhanced actions cho XState workflow machine với support cho
 * conditional routing và IF/SWITCH node logic.
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

import { assign } from 'xstate';
import { WorkflowContext } from '../types/workflow-context.interface';
import { DetailedNodeExecutionResult } from '../types';
import { 
  IConditionalDependencyGraph,
  IEnhancedConnection 
} from '../../../interfaces/conditional-connection.interface';

/**
 * Enhanced workflow actions với conditional routing support
 */
export const conditionalWorkflowActions = {
  /**
   * Handle node completion với conditional routing logic
   */
  handleConditionalNodeCompleted: assign({
    nodes: ({ context, event }: { context: WorkflowContext; event: any }) => {
      const { nodeId, result } = event;
      const updatedNodes = new Map(context.nodes);
      
      // Update completed node
      const nodeState = updatedNodes.get(nodeId);
      if (nodeState) {
        nodeState.status = result.success ? 'completed' : 'failed';
        nodeState.outputData = result.outputData;
        nodeState.error = result.error;
        nodeState.executionTime = result.metadata?.executionTime;
        
        // Store detailed result for conditional routing
        nodeState.detailedResult = result;
        
        updatedNodes.set(nodeId, nodeState);
      }
      
      return updatedNodes;
    },
    
    completedNodes: ({ context, event }: { context: WorkflowContext; event: any }) => {
      const { nodeId, result } = event;
      const updatedCompletedNodes = new Map(context.completedNodes || new Map());
      
      // Store detailed result for conditional dependency checking
      updatedCompletedNodes.set(nodeId, result);
      
      return updatedCompletedNodes;
    },
    
    // Update active connections based on conditional logic
    activeConnections: ({ context, event }: { context: WorkflowContext; event: any }) => {
      const { nodeId, result } = event;
      
      if (!context.conditionalGraph || !context.enhancedConnections) {
        return context.activeConnections || [];
      }
      
      // Get active connections from the completed node
      const conditionalConnectionService = (context as any).conditionalConnectionService;
      if (!conditionalConnectionService) {
        return context.activeConnections || [];
      }
      
      const newActiveConnections = conditionalConnectionService.getActiveConnections(
        nodeId,
        result,
        context.conditionalGraph
      );
      
      // Merge with existing active connections
      const existingActive = context.activeConnections || [];
      const allActive = [...existingActive, ...newActiveConnections];
      
      // Remove duplicates
      const uniqueActive = allActive.filter((conn, index, arr) => 
        arr.findIndex(c => c.id === conn.id) === index
      );
      
      return uniqueActive;
    }
  }),

  /**
   * Update dependencies với conditional routing support
   */
  updateConditionalDependencies: assign({
    readyNodes: ({ context }: { context: WorkflowContext }) => {
      if (!context.conditionalGraph || !context.completedNodes) {
        return context.readyNodes || [];
      }
      
      const dependencyResolver = (context as any).dependencyResolver;
      if (!dependencyResolver) {
        return context.readyNodes || [];
      }
      
      // Use enhanced dependency resolution
      const readyNodes = dependencyResolver.getReadyNodesWithConditional(
        context.conditionalGraph,
        context.completedNodes
      );
      
      return readyNodes;
    },
    
    waitingNodes: ({ context }: { context: WorkflowContext }) => {
      const readyNodes = context.readyNodes || [];
      const allNodeIds = Array.from(context.nodes?.keys() || []);
      const completedNodeIds = Array.from(context.completedNodes?.keys() || []);
      
      // Waiting nodes = all nodes - completed nodes - ready nodes
      const waitingNodes = allNodeIds.filter(nodeId => 
        !completedNodeIds.includes(nodeId) && 
        !readyNodes.includes(nodeId)
      );
      
      return waitingNodes;
    }
  }),

  /**
   * Initialize conditional routing context
   */
  initializeConditionalRouting: assign({
    conditionalGraph: ({ context }: { context: WorkflowContext }) => {
      // This should be set during workflow loading
      return context.conditionalGraph || undefined;
    },
    
    enhancedConnections: ({ context }: { context: WorkflowContext }) => {
      // This should be set during workflow loading
      return context.enhancedConnections || [];
    },
    
    activeConnections: ({ context }: { context: WorkflowContext }) => {
      // Initialize with non-conditional connections
      const enhancedConnections = context.enhancedConnections || [];
      const initialActive = enhancedConnections.filter(conn => !conn.isConditional);
      return initialActive;
    },
    
    completedNodes: ({ context }: { context: WorkflowContext }) => {
      // Initialize completed nodes map
      return context.completedNodes || new Map<string, DetailedNodeExecutionResult>();
    }
  }),

  /**
   * Log conditional routing decisions
   */
  logConditionalRouting: ({ context, event }: { context: WorkflowContext; event: any }) => {
    const { nodeId, result } = event;
    
    if (!context.conditionalGraph) {
      return;
    }
    
    const logger = (context as any).logger;
    if (!logger) {
      console.log(`Conditional routing for node ${nodeId}:`, {
        nodeType: result.nodeType,
        success: result.success,
        conditionalResult: result.outputData,
        activeConnections: context.activeConnections?.length || 0,
        readyNodes: context.readyNodes?.length || 0
      });
      return;
    }
    
    logger.debug(`Conditional routing for node ${nodeId}`, {
      nodeType: result.nodeType,
      success: result.success,
      conditionalResult: result.outputData,
      activeConnections: context.activeConnections?.length || 0,
      readyNodes: context.readyNodes?.length || 0,
      waitingNodes: context.waitingNodes?.length || 0
    });
  },

  /**
   * Validate conditional routing state
   */
  validateConditionalState: ({ context }: { context: WorkflowContext }) => {
    const errors: string[] = [];
    
    if (!context.conditionalGraph) {
      errors.push('Conditional graph not initialized');
    }
    
    if (!context.enhancedConnections) {
      errors.push('Enhanced connections not initialized');
    }
    
    if (!context.completedNodes) {
      errors.push('Completed nodes map not initialized');
    }
    
    if (errors.length > 0) {
      const logger = (context as any).logger;
      if (logger) {
        logger.warn('Conditional routing validation errors:', errors);
      } else {
        console.warn('Conditional routing validation errors:', errors);
      }
    }
    
    return errors.length === 0;
  },

  /**
   * Reset conditional routing state
   */
  resetConditionalState: assign({
    activeConnections: () => [],
    completedNodes: () => new Map<string, DetailedNodeExecutionResult>(),
    readyNodes: () => [],
    waitingNodes: () => []
  }),

  /**
   * Handle IF node specific completion
   */
  handleIfNodeCompleted: assign({
    ifNodeResults: ({ context, event }: { context: WorkflowContext; event: any }) => {
      const { nodeId, result } = event;
      
      if (result.nodeType !== 'IF_CONDITION') {
        return context.ifNodeResults || new Map();
      }
      
      const ifResults = new Map(context.ifNodeResults || new Map());
      ifResults.set(nodeId, {
        result: result.outputData.result,
        branchTaken: result.metadata?.branchTaken,
        evaluationDetails: result.outputData.evaluation_details
      });
      
      return ifResults;
    }
  }),

  /**
   * Handle SWITCH node specific completion
   */
  handleSwitchNodeCompleted: assign({
    switchNodeResults: ({ context, event }: { context: WorkflowContext; event: any }) => {
      const { nodeId, result } = event;

      if (result.nodeType !== 'SWITCH') {
        return context.switchNodeResults || new Map();
      }

      const switchResults = new Map(context.switchNodeResults || new Map());
      switchResults.set(nodeId, {
        selectedValue: result.outputData.selectedValue,
        selectedCase: result.outputData.selectedCase,
        isDefaultCase: result.outputData.isDefaultCase
      });

      return switchResults;
    }
  }),

  /**
   * Handle LOOP node specific completion
   */
  handleLoopNodeCompleted: assign({
    loopNodeResults: ({ context, event }: { context: WorkflowContext; event: any }) => {
      const { nodeId, result } = event;

      if (result.nodeType !== 'LOOP') {
        return context.loopNodeResults || new Map();
      }

      const loopResults = new Map(context.loopNodeResults || new Map());
      loopResults.set(nodeId, {
        control: result.outputData.breakConditionMet ? 'break' :
                result.outputData.maxIterationsReached ? 'break' : 'complete',
        iterations: result.outputData.iterations,
        breakConditionMet: result.outputData.breakConditionMet,
        maxIterationsReached: result.outputData.maxIterationsReached,
        breakReason: result.outputData.breakReason
      });

      return loopResults;
    }
  }),

  /**
   * Handle WAIT node specific completion
   */
  handleWaitNodeCompleted: assign({
    waitNodeResults: ({ context, event }: { context: WorkflowContext; event: any }) => {
      const { nodeId, result } = event;

      if (result.nodeType !== 'WAIT') {
        return context.waitNodeResults || new Map();
      }

      const waitResults = new Map(context.waitNodeResults || new Map());
      const outputData = result.outputData as any;
      const metadata = result.metadata as any;

      waitResults.set(nodeId, {
        status: outputData.wait_metadata?.status || 'completed',
        actualWaitTime: outputData.wait_metadata?.actual_wait_time || metadata?.actualWaitTime || 0,
        plannedWaitTime: outputData.wait_metadata?.planned_wait_time || metadata?.plannedWaitTime || 0,
        timeoutReached: outputData.wait_metadata?.timed_out || metadata?.timeoutReached || false,
        waitType: outputData.wait_metadata?.wait_type || metadata?.waitType
      });

      return waitResults;
    }
  }),

  /**
   * Handle HTTP_REQUEST node specific completion
   */
  handleHttpRequestNodeCompleted: assign({
    httpRequestNodeResults: ({ context, event }: { context: WorkflowContext; event: any }) => {
      const { nodeId, result } = event;

      if (result.nodeType !== 'HTTP_REQUEST') {
        return context.httpRequestNodeResults || new Map();
      }

      const httpResults = new Map(context.httpRequestNodeResults || new Map());
      const outputData = result.outputData as any;
      const metadata = result.metadata as any;

      httpResults.set(nodeId, {
        success: outputData.success || false,
        statusCode: outputData.status_code || metadata?.statusCode || 0,
        statusText: outputData.status_text || 'Unknown',
        responseTime: outputData.response_time || metadata?.responseTime || 0,
        method: metadata?.httpMethod || 'UNKNOWN',
        url: outputData.final_url || metadata?.finalUrl || 'unknown',
        hasError: !outputData.success || (outputData.status_code >= 400)
      });

      return httpResults;
    }
  })
};

/**
 * Enhanced guards với conditional support
 */
export const conditionalWorkflowGuards = {
  /**
   * Check if conditional routing is enabled
   */
  hasConditionalRouting: ({ context }: { context: WorkflowContext }) => {
    return !!(context.conditionalGraph && context.enhancedConnections);
  },

  /**
   * Check if node is conditional
   */
  isConditionalNode: ({ context, event }: { context: WorkflowContext; event: any }) => {
    const { nodeId } = event;
    const enhancedConnections = context.enhancedConnections || [];
    
    return enhancedConnections.some(conn => 
      conn.sourceNodeId === nodeId && conn.isConditional
    );
  },

  /**
   * Check if all conditional dependencies are resolved
   */
  allConditionalDependenciesResolved: ({ context }: { context: WorkflowContext }) => {
    if (!context.conditionalGraph || !context.completedNodes) {
      return false;
    }
    
    const waitingNodes = context.waitingNodes || [];
    return waitingNodes.length === 0;
  }
};
