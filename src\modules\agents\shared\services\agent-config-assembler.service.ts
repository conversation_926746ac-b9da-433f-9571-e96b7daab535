import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AgentConfig } from '../interfaces/agent-config.interface';
import {
  PlatformStrategy,
  AuthContext,
} from '../interfaces/platform-strategy.interface';
import { TypeAgentEnum } from '../enums';
import { McpConfiguration } from '../interfaces/mcp-config.interface';
import { ModelConfigOutput } from './model-registry-mapper.service';
import { EncryptedApiKeyPair } from './integration-config-builder.service';
import { AgentEntityConfigInterface, ModelConfig } from '../interfaces';
import { AgentMemories, SystemPrompt } from '../entities';

/**
 * Raw agent data from database queries
 */
export interface AgentRawData {
  id: string;
  name: string;
  instruction: string | null;
  config: AgentEntityConfigInterface | null;
  modelId: string | null;
  userId: number | null;
  employeeId: number | null;
  active: boolean;
  useSystemKey: boolean | null;
  modelConfig: ModelConfig;
  typeAgentType: TypeAgentEnum;
  prompt?: string; // Added for user multi-agent prompts and system agent descriptions
}

/**
 * Platform-Agnostic Agent Config Assembler Service
 *
 * Final assembly service that combines all pre-processed components
 * (model configs, integration configs, MCP configs) into complete
 * AgentConfigInterface structures ready for LangGraph execution.
 */
@Injectable()
export class AgentConfigAssemblerService {
  private readonly logger = new Logger(AgentConfigAssemblerService.name);

  constructor(
    @InjectRepository(AgentMemories)
    private readonly agentMemoriesRepository: Repository<AgentMemories>,
    @InjectRepository(SystemPrompt)
    private readonly systemPromptRepository: Repository<SystemPrompt>,
  ) {}

  /**
   * Assemble complete agent configurations from all pre-processed components
   * ZERO ADDITIONAL QUERIES - pure data assembly
   *
   * @param agents Basic agent data from Query 1
   * @param modelConfigs Pre-processed model configs from Task 3.1
   * @param integrationConfigs Pre-processed integration configs from Task 3.2
   * @param mcpConfigs Pre-processed MCP configs from Task 3.3
   * @param authContext Platform-specific authentication context
   * @param promptBuilderMap Pre-built prompt builders
   * @param platformStrategy Platform-specific strategy for tools and auth
   * @returns Map of agent IDs to complete AgentConfigInterface objects
   */
  async assembleAgentConfigs(params: {
    agents: AgentRawData[];
    modelConfigs: Record<string, ModelConfigOutput>;
    integrationConfigs: Record<string, EncryptedApiKeyPair[]>;
    mcpConfigs: Record<string, Record<string, McpConfiguration>>;
    authContext: AuthContext;
    promptBuilderMap: Record<string, Array<() => string | Promise<string>>>;
    platformStrategy: PlatformStrategy;
  }): Promise<Record<string, AgentConfig>> {
    const {
      agents,
      modelConfigs,
      integrationConfigs,
      mcpConfigs,
      authContext,
      promptBuilderMap,
      platformStrategy,
    } = params;

    this.logger.debug(
      `Assembling agent configs for ${agents.length} agents on platform: ${platformStrategy.platform}`,
    );

    const result: Record<string, AgentConfig> = {};

    for (const agent of agents) {
      const agentConfig = await this.assembleAgentConfig({
        agent,
        modelConfig: modelConfigs[agent.id],
        integrationConfig: integrationConfigs[agent.id] || [],
        mcpConfig: mcpConfigs[agent.id] || null,
        authContext,
        promptBuilders: promptBuilderMap[agent.id] || [],
        platformStrategy,
      });

      // Platform-specific tool assignment
      agentConfig.tools = platformStrategy.getToolsForAgent(agentConfig, authContext);

      result[agent.id] = agentConfig;

      this.logger.debug(
        `Assembled config for agent ${agent.id}: ${agent.name}`,
        {
          agentId: agent.id,
          agentName: agent.name,
          platform: platformStrategy.platform,
          hasModelConfig: !!modelConfigs[agent.id],
          integrationConfigCount: (integrationConfigs[agent.id] || []).length,
          hasMcpConfig: !!mcpConfigs[agent.id],
          toolCount: agentConfig.tools?.length || 0,
        },
      );
    }
    return result;
  }

  /**
   * Assemble complete agent configuration for a single agent
   */
  private async assembleAgentConfig(params: {
    agent: AgentRawData;
    modelConfig: ModelConfigOutput | undefined;
    integrationConfig: EncryptedApiKeyPair[];
    mcpConfig: Record<string, McpConfiguration> | null;
    authContext: AuthContext;
    promptBuilders: Array<() => string | Promise<string>>;
    platformStrategy: PlatformStrategy;
  }): Promise<AgentConfig> {
    const {
      agent,
      modelConfig,
      integrationConfig,
      mcpConfig,
      authContext,
      promptBuilders,
      platformStrategy,
    } = params;

    // Step 1: Validate required data
    if (!modelConfig) {
      throw new Error(`No model config found for agent ${agent.id}`);
    }

    // Step 2: Merge model config with integration API keys
    const completeModelConfig = {
      ...modelConfig,
      encryptedApiKeyPairs: integrationConfig,
    };

    // Step 3: Build MCP client using platform strategy
    this.logger.debug(`🔧 Building MCP client for agent: ${agent.id}`, {
      agentId: agent.id,
      agentName: agent.name,
      platform: platformStrategy.platform,
      hasMcpConfig: !!mcpConfig,
      mcpServerCount: mcpConfig ? Object.keys(mcpConfig).length : 0,
    });

    const mcpClient = await platformStrategy.buildMcpClient(
      mcpConfig,
      authContext,
    );

    this.logger.debug(`✅ MCP client built for agent: ${agent.id}`, {
      agentId: agent.id,
      mcpClientExists: !!mcpClient,
    });

    // Step 4: Build specialized config with description from prompt
    const specializedConfig = {
      ...agent.config,
      description: agent.prompt || agent.config?.description,
    };

    // Step 5: Build complete agent configuration
    return new AgentConfig({
      id: agent.id,
      name: agent.name,
      instruction: agent.instruction || '',
      specializedConfig: specializedConfig,
      mcpClient: mcpClient || null,
      mcpDescriptions: mcpConfig
        ? Object.entries(mcpConfig).map(([nameServer, mcp]) => ({
            serverName: nameServer,
            description: mcp.description,
          }))
        : null,
      type: agent.typeAgentType,
      owner: {
        userId: agent.userId || undefined,
        employeeId: agent.employeeId || undefined,
      },
      model: completeModelConfig,
      promptBuilders,
      agentMemoriesRepository: this.agentMemoriesRepository,
      systemPromptRepository: this.systemPromptRepository,
    });
  }
}
