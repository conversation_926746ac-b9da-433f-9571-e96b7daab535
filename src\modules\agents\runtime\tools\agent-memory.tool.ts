import { CallbackManagerForToolRun } from '@langchain/core/callbacks/manager';
import { StructuredTool, ToolRunnableConfig } from '@langchain/core/tools';
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AgentMemories } from '../../entities';
import { In, Repository } from 'typeorm';
import { z } from 'zod';

@Injectable()
export class AgentMemoryTool extends StructuredTool {
  private readonly logger = new Logger(AgentMemoryTool.name);
  name: string;
  description: string;
  schema: any;
  constructor(
    @InjectRepository(AgentMemories)
    private readonly agentMemoriesRepository: Repository<AgentMemories>,
  ) {
    super();
    this.name = 'manage_agent_memory';
    this.description = `A tool to create, update, or delete long-term memories for the agent. Each memory object in the 'memories' array must have an 'action' field.`;

    // ✅ Define the individual schemas for the union
    const createSchema = z.object({
      action: z.literal('create'),
      content: z.string().describe('The main content of the memory.'),
      metadata: z
        .record(z.any())
        .optional()
        .describe('Optional metadata for the memory.'),
    });

    const updateSchema = z.object({
      action: z.literal('update'),
      id: z.string().uuid('The ID of the memory to update.'),
      // Make both optional here, we'll validate them below
      content: z
        .string()
        .optional()
        .describe('The main content of the memory.'),
      metadata: z.record(z.any()).optional(),
    });

    const deleteSchema = z.object({
      action: z.literal('delete'),
      id: z.string().uuid('The ID of the memory to delete.'),
    });

    // ✅ Create the union first...
    const memoryActionSchema = z
      .discriminatedUnion('action', [createSchema, updateSchema, deleteSchema])
      // ✅ ...then apply a single .refine() to the whole union.
      .refine(
        (data) => {
          // If the action is 'update', check that at least one field is provided.
          if (data.action === 'update') {
            return !!data.content || !!data.metadata;
          }
          // For 'create' and 'delete', no extra validation is needed here.
          return true;
        },
        {
          message:
            "An 'update' action must provide either 'content' or 'metadata' to modify.",
          // This path helps point errors to the correct object in the array
          path: ['content'],
        },
      );

    // Define the final schema for the tool's input
    this.schema = z.object({
      memories: z
        .array(memoryActionSchema)
        .describe('An array of memory operations to perform.'),
    });
  }

  // No changes needed to your _call method.
  // It already groups by the 'action' field present in each memory object.
  protected async _call(
    arg: z.infer<typeof this.schema>,
    runManager?: CallbackManagerForToolRun,
    parentConfig?: ToolRunnableConfig,
  ): Promise<string> {
    const { memories } = arg;
    const agentId = parentConfig?.configurable?.agentConfig.id;
    if (!agentId) {
      return 'Error: Missing agent ID in execution code. Do no retry upon this message';
    }

    // 1. Group memories by operation
    const toCreate = memories.filter((m) => m.action === 'create');
    const toUpdate = memories.filter((m) => m.action === 'update');
    const toDelete = memories.filter((m) => m.action === 'delete');

    // 2. Use a QueryRunner to manage the transaction
    const queryRunner =
      this.agentMemoriesRepository.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 3. Execute each bulk operation within the transaction
      if (toCreate.length > 0) {
        const newMemories = toCreate.map((mem) =>
          this.agentMemoriesRepository.create({
            content: mem.content,
            metadata: mem.metadata,
            agentId: agentId,
          }),
        );
        await queryRunner.manager.save(AgentMemories, newMemories);
      }

      if (toUpdate.length > 0) {
        const idsToUpdate = toUpdate.map((m) => m.id);
        const count = await queryRunner.manager.count(AgentMemories, {
          where: { id: In(idsToUpdate), agentId: agentId },
        });
        if (count !== idsToUpdate.length) {
          throw new Error(
            'Permission denied or one or more memories not found for update.',
          );
        }
        // The `save` method handles partial updates correctly
        await queryRunner.manager.save(AgentMemories, toUpdate);
      }

      if (toDelete.length > 0) {
        const idsToDelete = toDelete.map((m) => m.id);
        await queryRunner.manager.delete(AgentMemories, {
          id: In(idsToDelete),
          agentId: agentId,
        });
      }

      // 4. If all operations succeed, commit the transaction
      await queryRunner.commitTransaction();
      return `Successfully processed memory operations: ${toCreate.length} created, ${toUpdate.length} updated, ${toDelete.length} deleted.`;
    } catch (error) {
      // 5. If any operation fails, roll back all previous changes
      await queryRunner.rollbackTransaction();
      this.logger.error(
        `Error in memory transaction for agent ${agentId}`,
        error,
      );
      return `Error: The memory operation failed and all changes were reverted. Reason: ${error.message}`;
    } finally {
      // 6. Always release the query runner to free up the connection
      await queryRunner.release();
    }
  }
}
