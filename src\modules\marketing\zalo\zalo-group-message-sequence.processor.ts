import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger, Injectable } from '@nestjs/common';
import { Job } from 'bullmq';
import { QueueName, ZaloGroupMessageSequenceJobName } from '../../../queue';
import {
  ZaloGroupMessageSequenceJobData,
  ZaloGroupMessageSequenceJobResult,
  GroupMessageType,
  GroupMessageItemDto,
} from './interfaces';
import { ZaloGroupMessageService } from '../../../shared/services/zalo/zalo-group-message.service';
import { ZaloTokenUtilsService } from '../../../shared/services/zalo/zalo-token-utils.service';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Integration } from '../../../shared/entities/integration.entity';

/**
 * Processor xử lý queue Zalo Group Message Sequence
 * Xử lý job gửi chuỗi tin nhắn group cho nhiều nhóm
 */
@Injectable()
@Processor(QueueName.ZALO_GROUP_MESSAGE_SEQUENCE, { concurrency: 2 })
export class ZaloGroupMessageSequenceProcessor extends WorkerHost {
  private readonly logger = new Logger(ZaloGroupMessageSequenceProcessor.name);

  constructor(
    private readonly zaloGroupMessageService: ZaloGroupMessageService,
    private readonly zaloTokenUtilsService: ZaloTokenUtilsService,
    private readonly configService: ConfigService,
    @InjectRepository(Integration)
    private readonly integrationRepository: Repository<Integration>,
  ) {
    super();
  }

  /**
   * Xử lý job từ queue
   */
  async process(
    job: Job<ZaloGroupMessageSequenceJobData>,
  ): Promise<ZaloGroupMessageSequenceJobResult> {
    this.logger.log(
      `Processing group message sequence job ${job.id} for user ${job.data.userId}`,
    );

    const startTime = Date.now();

    try {
      switch (job.name) {
        case ZaloGroupMessageSequenceJobName.SEND_GROUP_MESSAGE_SEQUENCE:
          const result = await this.processGroupMessageSequence(job);
          this.logger.log(`Job ${job.id} completed successfully`);
          return result;
        default:
          throw new Error(`Unknown job name: ${job.name}`);
      }
    } catch (error) {
      this.logger.error(`Job ${job.id} failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xử lý job gửi chuỗi tin nhắn group
   */
  private async processGroupMessageSequence(
    job: Job<ZaloGroupMessageSequenceJobData>,
  ): Promise<ZaloGroupMessageSequenceJobResult> {
    const { userId, integrationId, groupIds, messages, trackingId } = job.data;
    const startTime = Date.now();

    // Log chi tiết job data
    this.logger.log(
      `Processing group message sequence for user ${userId}, ${groupIds.length} groups, ${messages.length} messages`,
    );
    this.logger.debug(
      `Job data: ${JSON.stringify(
        {
          userId,
          integrationId,
          groupIds,
          messages: messages.map((msg) => ({
            messageType: msg.messageType,
            delaySeconds: msg.delaySeconds,
            content:
              msg.content?.substring(0, 100) +
              (msg.content && msg.content.length > 100 ? '...' : ''),
            imageUrl: msg.imageUrl,
            attachmentId: msg.attachmentId,
            caption: msg.caption,
            fileUrl: msg.fileUrl,
            filename: msg.filename,
            stickerId: msg.stickerId,
            mentionUids: msg.mentionUids,
          })),
          trackingId,
        },
        null,
        2,
      )}`,
    );

    try {
      // Lấy access token
      const accessToken = await this.getAccessToken(integrationId);
      this.logger.debug(
        `Got access token for integration ${integrationId}: ${accessToken?.substring(0, 20)}...`,
      );

      const results: Array<{
        groupId: string;
        messageIndex: number;
        messageId?: string;
        status: 'sent' | 'failed';
        error?: string;
      }> = [];

      let successCount = 0;
      let failureCount = 0;

      // Gửi tin nhắn đến từng nhóm
      for (const groupId of groupIds) {
        this.logger.debug(`Sending message sequence to group ${groupId}`);

        // Gửi từng tin nhắn trong chuỗi
        for (let i = 0; i < messages.length; i++) {
          const message = messages[i];

          try {
            this.logger.debug(
              `Preparing to send message ${i + 1}/${messages.length} to group ${groupId}:`,
              {
                messageType: message.messageType,
                content:
                  message.content?.substring(0, 50) +
                  (message.content && message.content.length > 50 ? '...' : ''),
                delaySeconds: message.delaySeconds,
              },
            );

            // Delay trước khi gửi tin nhắn (nếu có)
            if (message.delaySeconds && message.delaySeconds > 0) {
              this.logger.debug(
                `Delaying ${message.delaySeconds} seconds before sending message ${i + 1}`,
              );
              await new Promise((resolve) =>
                setTimeout(resolve, message.delaySeconds! * 1000),
              );
            }

            const result = await this.sendSingleGroupMessage(
              accessToken,
              groupId,
              message,
            );

            results.push({
              groupId,
              messageIndex: i,
              messageId: result.message_id,
              status: 'sent',
            });
            successCount++;

            this.logger.debug(
              `Sent message ${i + 1}/${messages.length} to group ${groupId} successfully`,
            );
          } catch (error) {
            this.logger.error(
              `Failed to send message ${i + 1} to group ${groupId}: ${error.message}`,
            );

            results.push({
              groupId,
              messageIndex: i,
              status: 'failed',
              error: error.message,
            });
            failureCount++;
          }
        }
      }

      const endTime = Date.now();
      const jobResult: ZaloGroupMessageSequenceJobResult = {
        totalSent: groupIds.length * messages.length,
        successCount,
        failureCount,
        results,
        startTime,
        endTime,
        trackingId,
      };

      this.logger.log(
        `Completed group message sequence: ${successCount} success, ${failureCount} failed`,
      );

      return jobResult;
    } catch (error) {
      this.logger.error(
        `Error processing group message sequence: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Gửi một tin nhắn đến nhóm
   */
  private async sendSingleGroupMessage(
    accessToken: string,
    groupId: string,
    message: GroupMessageItemDto,
  ): Promise<{ message_id: string }> {
    this.logger.debug(
      `Sending ${message.messageType} message to group ${groupId}`,
    );

    switch (message.messageType) {
      case GroupMessageType.TEXT:
        if (!message.content) {
          throw new Error('Content is required for text message');
        }

        const textPayload = {
          type: 'group_text' as const,
          text: message.content,
        };
        this.logger.debug(`Text message payload:`, textPayload);

        return await this.zaloGroupMessageService.sendTextMessage(
          accessToken,
          groupId,
          textPayload,
        );

      case GroupMessageType.IMAGE:
        if (!message.imageUrl && !message.attachmentId) {
          throw new Error(
            'ImageUrl or attachmentId is required for image message',
          );
        }
        return await this.zaloGroupMessageService.sendImageMessage(
          accessToken,
          groupId,
          {
            type: 'group_image',
            url: message.imageUrl || '',
          },
        );

      case GroupMessageType.FILE:
        if (!message.fileUrl || !message.filename) {
          throw new Error('FileUrl and filename are required for file message');
        }
        return await this.zaloGroupMessageService.sendFileMessage(
          accessToken,
          groupId,
          {
            type: 'group_file',
            url: message.fileUrl,
            filename: message.filename,
          },
        );

      case GroupMessageType.STICKER:
        if (!message.stickerId) {
          throw new Error('StickerId is required for sticker message');
        }
        return await this.zaloGroupMessageService.sendStickerMessage(
          accessToken,
          groupId,
          {
            type: 'group_sticker',
            sticker_id: message.stickerId,
          },
        );

      case GroupMessageType.MENTION:
        if (
          !message.content ||
          !message.mentionUids ||
          message.mentionUids.length === 0
        ) {
          throw new Error(
            'Content and mentionUids are required for mention message',
          );
        }
        return await this.zaloGroupMessageService.sendMentionMessage(
          accessToken,
          groupId,
          {
            type: 'group_mention',
            text: message.content,
            mention_uids: message.mentionUids,
          },
        );

      default:
        throw new Error(`Unsupported message type: ${message.messageType}`);
    }
  }

  /**
   * Lấy access token cho integration
   */
  private async getAccessToken(integrationId: string): Promise<string> {
    try {
      this.logger.debug(`Looking up integration: ${integrationId}`);

      // Lấy integration detail để có OA ID
      const integration = await this.integrationRepository.findOne({
        where: { id: integrationId },
      });

      this.logger.debug(`Integration found:`, {
        id: integration?.id,
        integrationName: integration?.integrationName,
        typeId: integration?.typeId,
        userId: integration?.userId,
        metadata: integration?.metadata,
      });

      if (!integration) {
        throw new Error(`Integration not found: ${integrationId}`);
      }

      const oaId = integration.metadata?.oaId;
      this.logger.debug(`Extracted OA ID from metadata: ${oaId}`);

      if (!oaId) {
        throw new Error(
          `OA ID not found in integration metadata: ${integrationId}`,
        );
      }

      // Sử dụng token utils để lấy access token với retry mechanism
      return await this.zaloTokenUtilsService.executeWithTokenRetry(
        async (token) => token,
        oaId, // Dùng OA ID thay vì integration ID
        3, // Retry tối đa 3 lần
      );
    } catch (error) {
      this.logger.error(
        `Failed to get access token for integration ${integrationId}: ${error.message}`,
      );
      throw new Error('Unable to get valid access token');
    }
  }
}
