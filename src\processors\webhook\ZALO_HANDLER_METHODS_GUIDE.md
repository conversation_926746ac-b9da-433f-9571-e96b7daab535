# Zalo Webhook Handler Methods Guide

## 📋 **Tổng Quan**

Đã tạo **25 handler methods** mới để xử lý **82 event types** từ Zalo SDK một cách có tổ chức và chi tiết.

## 🎯 **Cấu <PERSON>ler Methods**

### **1. USER MESSAGE HANDLERS (4 methods)**

#### **processUserTextMessageJob**
- **Events**: `user_send_text`
- **Purpose**: Xử lý tin nhắn text từ user
- **Output**: messageId, text, senderId

#### **processUserMediaMessageJob**
- **Events**: `user_send_image`, `user_send_video`, `user_send_audio`, `user_send_gif`, `user_send_file`
- **Purpose**: Xử lý media messages từ user
- **Output**: messageId, mediaType, mediaUrl, senderId

#### **processUserSpecialMessageJob**
- **Events**: `user_send_location`, `user_send_link`, `user_send_sticker`
- **Purpose**: Xử lý special messages từ user
- **Output**: messageId, specialType, payload, senderId

#### **processUserBusinessCardJob**
- **Events**: `user_send_business_card`
- **Purpose**: Xử lý business card từ user
- **Output**: messageId, businessCard, senderId

### **2. USER GROUP MESSAGE HANDLERS (3 methods)**

#### **processUserGroupTextJob**
- **Events**: `user_send_group_text`
- **Purpose**: Xử lý group text messages từ user
- **Output**: messageId, text, senderId, groupId

#### **processUserGroupMediaJob**
- **Events**: `user_send_group_image`, `user_send_group_video`, `user_send_group_audio`, `user_send_group_gif`, `user_send_group_file`
- **Purpose**: Xử lý group media messages từ user
- **Output**: messageId, mediaType, mediaUrl, senderId, groupId

#### **processUserGroupSpecialJob**
- **Events**: `user_send_group_location`, `user_send_group_link`, `user_send_group_sticker`, `user_send_group_business_card`
- **Purpose**: Xử lý group special messages từ user
- **Output**: messageId, specialType, payload, senderId, groupId

### **3. OA MESSAGE HANDLERS (2 methods)**

#### **processOADirectMessageJob**
- **Events**: `oa_send_text`, `oa_send_image`, `oa_send_list`, `oa_send_gif`, `oa_send_file`, `oa_send_sticker`
- **Purpose**: Xử lý direct messages từ OA
- **Output**: messageId, text, recipientId

#### **processOAGroupMessageJob**
- **Events**: `oa_send_group_text`, `oa_send_group_image`, `oa_send_group_link`, `oa_send_group_audio`, `oa_send_group_location`, `oa_send_group_video`, `oa_send_group_business_card`, `oa_send_group_sticker`, `oa_send_group_gif`, `oa_send_group_file`
- **Purpose**: Xử lý group messages từ OA
- **Output**: messageId, text, recipientId, groupId

### **4. ANONYMOUS MESSAGE HANDLERS (2 methods)**

#### **processAnonymousUserMessageJob**
- **Events**: `anonymous_send_text`, `anonymous_send_image`, `anonymous_send_file`, `anonymous_send_sticker`
- **Purpose**: Xử lý anonymous messages từ user
- **Output**: messageId, text, conversationId, senderId

#### **processAnonymousOAMessageJob**
- **Events**: `oa_send_anonymous_text`, `oa_send_anonymous_image`, `oa_send_anonymous_file`, `oa_send_anonymous_sticker`
- **Purpose**: Xử lý anonymous messages từ OA
- **Output**: messageId, text, conversationId, recipientId

### **5. USER INTERACTION HANDLERS (1 method)**

#### **processMessageStatusJob**
- **Events**: `user_received_message`, `user_seen_message`
- **Purpose**: Xử lý message status tracking
- **Output**: messageId, messageIds, senderId

### **6. BUSINESS LOGIC HANDLERS (1 method)**

#### **processTemplateMessageJob**
- **Events**: `oa_send_template`
- **Purpose**: Xử lý template messages từ OA
- **Output**: messageId, templatePayload, recipientId

### **7. GROUP MANAGEMENT HANDLERS (3 methods)**

#### **processGroupBasicManagementJob**
- **Events**: `create_group`, `update_group_info`, `disperse_group`
- **Purpose**: Xử lý basic group management
- **Output**: groupId

#### **processGroupMemberManagementJob**
- **Events**: `user_join_group`, `user_request_join_group`, `react_request_join_group`, `reject_request_join_group`, `user_out_group`
- **Purpose**: Xử lý group member management
- **Output**: groupId, users

#### **processGroupAdminManagementJob**
- **Events**: `add_group_admin`, `remove_group_admin`
- **Purpose**: Xử lý group admin management
- **Output**: groupId, users

### **8. TEMPLATE & ZNS HANDLERS (3 methods)**

#### **processTemplateChangesJob**
- **Events**: `change_oa_daily_quota`, `change_template_quality`, `change_template_quota`, `change_template_status`
- **Purpose**: Xử lý template changes
- **Output**: templateId, quota, quality, status, reason

#### **processTemplateTagsJob**
- **Events**: `change_oa_template_tags`
- **Purpose**: Xử lý template tags changes
- **Output**: tagLevel

#### **processZNSDeliveryJob**
- **Events**: `user_received_zns_message`
- **Purpose**: Xử lý ZNS delivery events
- **Output**: messageId, deliveryTime, trackingId

### **9. JOURNEY HANDLERS (1 method)**

#### **processJourneyEventJob**
- **Events**: `event_journey_time_out`, `event_journey_acknowledged`
- **Purpose**: Xử lý journey events
- **Output**: journeyId, messageId

### **10. SYSTEM HANDLERS (2 methods)**

#### **processWidgetEventJob**
- **Events**: `widget_interaction_accepted`, `widget_failed_to_sync_user_external_id`
- **Purpose**: Xử lý widget events
- **Output**: data

#### **processPermissionEventJob**
- **Events**: `permission_revoked`, `extension_purchased`
- **Purpose**: Xử lý permission events
- **Output**: extensionId, extensionSubInfo, data

### **11. ANALYTICS HANDLERS (1 method)**

#### **processUserTagJob**
- **Events**: `add_user_to_tag`
- **Purpose**: Xử lý user tag events
- **Output**: userIdByApp

## 🔄 **Event to Job Mapping**

Sử dụng `ZALO_EVENT_TO_JOB_MAPPING` để map từ event type sang job name:

```typescript
import { getJobNameFromEventType } from './zalo-job-names.enum';

const jobName = getJobNameFromEventType('user_send_text');
// Returns: ZaloWebhookJobName.PROCESS_USER_TEXT_MESSAGE
```

## 📊 **Priority Levels**

- **High Priority (180)**: User messages, interactions, follow events
- **Medium-High Priority (120)**: OA messages, business logic
- **Medium Priority (75)**: Group management
- **Low Priority (35)**: Template, ZNS, journey
- **Lowest Priority (10)**: System, analytics

## 🎯 **Cách Sử Dụng**

### **1. Automatic Routing**
```typescript
// Event được route tự động dựa trên job name
const result = await this.routeToProcessor(jobName, event, context);
```

### **2. Manual Handler Call**
```typescript
// Gọi handler trực tiếp
const result = await this.processUserTextMessageJob(event, context);
```

### **3. Event Type Detection**
```typescript
// Detect job name từ event type
const jobName = getJobNameFromEventType(event.event_name);
if (jobName) {
  const result = await this.routeToProcessor(jobName, event, context);
}
```

## 🔧 **Customization**

Mỗi handler method có thể được customize để:
- Thêm business logic cụ thể
- Integrate với database
- Trigger workflows
- Send notifications
- Update analytics

## 📈 **Benefits**

1. **Organized**: 82 events được group thành 25 handlers có tổ chức
2. **Scalable**: Dễ thêm logic mới cho từng loại event
3. **Maintainable**: Code rõ ràng, dễ debug và maintain
4. **Type-safe**: Sử dụng enum thay vì string literals
5. **Flexible**: Có thể customize từng handler độc lập

## 🚀 **Next Steps**

1. **Implement Business Logic**: Thêm logic cụ thể vào từng handler
2. **Database Integration**: Connect với database để lưu trữ data
3. **Workflow Triggers**: Trigger workflows dựa trên event types
4. **Analytics**: Collect metrics cho từng loại event
5. **Testing**: Tạo unit tests cho từng handler method
