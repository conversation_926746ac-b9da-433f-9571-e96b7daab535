import { Injectable, Logger } from '@nestjs/common';
import { ReactAgentConfig, ReactAgentStateType } from '../react-agent.graph';
import {
  AIMessage,
  BaseMessage,
  HumanMessage,
  isAIMessage,
  isHumanMessage,
  isToolMessage,
  RemoveMessage,
  ToolMessage,
} from '@langchain/core/messages';

@Injectable()
export class TrimService {
  private readonly logger = new Logger(TrimService.name);
  private readonly maxMessages = 20; // Maximum messages to keep

  async trim(
    state: ReactAgentStateType,
    config: ReactAgentConfig,
  ): Promise<Partial<ReactAgentStateType>> {
    const { messages } = state;

    // If we have 20 or fewer messages, no trimming needed
    if (messages.length <= this.maxMessages) {
      return state;
    }

    this.logger.debug(`Trimming messages: ${messages.length} > ${this.maxMessages}`);

    // Identify atomic groups
    const atomicGroups = this.identifyAtomicGroups(messages);

    // Keep atomic groups from the end until we reach ~20 messages
    let keptMessages = 0;
    const groupsToKeep: BaseMessage[][] = [];

    for (let i = atomicGroups.length - 1; i >= 0; i--) {
      const group = atomicGroups[i];
      if (keptMessages + group.length <= 20) {
        groupsToKeep.unshift(group); // Add to beginning to maintain order
        keptMessages += group.length;
      } else {
        break; // Stop when we'd exceed 20 messages
      }
    }

    // Flatten the kept groups to get the message IDs we want to keep
    const messageIdsToKeep = new Set(
      groupsToKeep.flat().map((msg) => msg.id as string),
    );

    // Create RemoveMessage objects for all messages we want to remove
    const removeMessages = messages
      .filter((msg) => !messageIdsToKeep.has(msg.id as string))
      .map((msg) => new RemoveMessage({ id: msg.id as string }));

    this.logger.debug(
      `Removing ${removeMessages.length} messages, keeping ${keptMessages} messages`,
    );

    return {
      messages: removeMessages,
    };
  }

  private identifyAtomicGroups(messages: BaseMessage[]): BaseMessage[][] {
    const groups: BaseMessage[][] = [];
    let currentGroup: BaseMessage[] = [];

    for (let i = 0; i < messages.length; i++) {
      const message = messages[i];

      if (isHumanMessage(message)) {
        const humanMessage = message as HumanMessage;
        // Human message ALWAYS starts a new group
        if (currentGroup.length > 0) {
          groups.push([...currentGroup]);
        }
        currentGroup = [humanMessage];
      } else if (isAIMessage(message)) {
        const aiMessage = message as AIMessage; // AI message type from LangChain
        currentGroup.push(aiMessage);

        // If AI has no tool calls, this group is complete
        if (!aiMessage.tool_calls || aiMessage.tool_calls.length === 0) {
          groups.push([...currentGroup]);
          currentGroup = [];
        }
        // If AI has tool calls, keep group open for tool responses
      } else if (isToolMessage(message)) {
        const toolMessage = message as ToolMessage;
        // Tool messages must belong to current group
        currentGroup.push(toolMessage);

        // Check if this completes all tool calls
        if (this.areAllToolCallsComplete(currentGroup)) {
          groups.push([...currentGroup]);
          currentGroup = [];
        }
      }
    }

    // Handle any remaining incomplete group
    if (currentGroup.length > 0) {
      groups.push(currentGroup);
    }

    return groups;
  }

  private areAllToolCallsComplete(groupMessages: BaseMessage[]): boolean {
    // Find the AI message with tool calls in this group
    const aiWithTools = groupMessages.find(
      (m) => isAIMessage(m) && m.tool_calls && m.tool_calls.length > 0,
    );

    if (!aiWithTools || !isAIMessage(aiWithTools)) return true;

    const expectedToolCallIds = new Set(
      (aiWithTools.tool_calls || [])
        .map((tc) => tc.id)
        .filter((id) => id != null) as string[],
    );
    const actualToolResponseIds = new Set(
      groupMessages
        .filter((m) => isToolMessage(m))
        .map((m) => (m as ToolMessage).tool_call_id)
        .filter((id) => id != null) as string[],
    );

    // Check if every tool call has a response
    return (
      expectedToolCallIds.size === actualToolResponseIds.size &&
      [...expectedToolCallIds].every((id) => actualToolResponseIds.has(id))
    );
  }
}
