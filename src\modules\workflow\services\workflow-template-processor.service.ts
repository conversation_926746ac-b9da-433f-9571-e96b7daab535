import { Injectable, Logger } from '@nestjs/common';
import { WorkflowContext } from '../interfaces/workflow-context.interface';
import { WorkflowContextService } from './workflow-context.service';

/**
 * ✅ WORKFLOW TEMPLATE PROCESSOR SERVICE
 * Service để xử lý templates với {{json.node.field}} syntax
 */
@Injectable()
export class WorkflowTemplateProcessorService {
  private readonly logger = new Logger(WorkflowTemplateProcessorService.name);

  constructor(
    private readonly contextService: WorkflowContextService
  ) { }

  /**
   * Process template với advanced syntax support
   */
  processTemplate(
    template: string,
    context: WorkflowContext
  ): string {

    if (!template || typeof template !== 'string') {
      return template;
    }

    let processedTemplate = template;
    const templateContext = this.buildTemplateContext(context);

    this.logger.debug(`Processing template`, {
      templateLength: template.length,
      hasVariables: template.includes('{{'),
      availableNodes: Object.keys(context.json)
    });

    // 1. Process {{json.node_name.field}} syntax
    processedTemplate = this.processJsonNamespace(processedTemplate, templateContext);

    // 2. Process {{metadata.field}} syntax
    processedTemplate = this.processMetadata(processedTemplate, templateContext);

    // 3. Process simple {{field}} syntax (backward compatibility)
    processedTemplate = this.processSimpleVariables(processedTemplate, templateContext);

    this.logger.debug('Template processed successfully', {
      originalLength: template.length,
      processedLength: processedTemplate.length,
      hasRemainingVariables: processedTemplate.includes('{{')
    });

    return processedTemplate;
  }

  /**
   * Process {{json.node_name.field}} syntax
   */
  private processJsonNamespace(
    template: string,
    context: Record<string, any>
  ): string {

    // Pattern: {{json.node_name.field}} hoặc {{json.node_name.nested.field}}
    const jsonPattern = /\{\{json\.([^.}]+)\.([^}]+)\}\}/g;

    return template.replace(jsonPattern, (match, nodeName, fieldPath) => {
      try {
        const nodeData = context.json?.[nodeName];
        if (!nodeData) {
          this.logger.warn(`Node "${nodeName}" not found in context`);
          return match; // Keep original if not found
        }

        // Support nested field access: field.subfield.subsubfield
        const value = this.getNestedValue(nodeData, fieldPath);

        if (value !== undefined && value !== null) {
          return String(value);
        } else {
          this.logger.warn(`Field "${fieldPath}" not found in node "${nodeName}"`);
          return ''; // Return empty string if field not found
        }

      } catch (error) {
        this.logger.error(`Error processing {{json.${nodeName}.${fieldPath}}}:`, error);
        return match; // Keep original on error
      }
    });
  }

  /**
   * Process {{metadata.field}} syntax
   */
  private processMetadata(
    template: string,
    context: Record<string, any>
  ): string {

    const metadataPattern = /\{\{metadata\.([^}]+)\}\}/g;

    return template.replace(metadataPattern, (match, fieldName) => {
      try {
        const value = this.getNestedValue(context.metadata, fieldName);
        return value !== undefined ? String(value) : '';
      } catch (error) {
        this.logger.error(`Error processing {{metadata.${fieldName}}}:`, error);
        return match;
      }
    });
  }

  /**
   * Process simple {{field}} syntax (backward compatibility)
   */
  private processSimpleVariables(
    template: string,
    context: Record<string, any>
  ): string {

    // Pattern: {{field}} (không có namespace)
    const simplePattern = /\{\{([^.}]+)\}\}/g;

    return template.replace(simplePattern, (match, fieldName) => {
      try {
        // Try to find in multiple namespaces
        let value = context[fieldName];

        if (value === undefined) {
          value = context.metadata?.[fieldName];
        }

        return value !== undefined ? String(value) : '';
      } catch (error) {
        this.logger.error(`Error processing {{${fieldName}}}:`, error);
        return match;
      }
    });
  }

  /**
   * Get nested value từ object với dot notation
   */
  private getNestedValue(obj: any, path: string): any {
    if (!obj || !path) return undefined;

    const keys = path.split('.');
    let current = obj;

    for (const key of keys) {
      if (current === null || current === undefined) {
        return undefined;
      }

      // Handle array access [index]
      if (key.includes('[') && key.includes(']')) {
        const arrayMatch = key.match(/^([^[]+)\[(\d+)\]$/);
        if (arrayMatch) {
          const [, arrayKey, indexStr] = arrayMatch;
          const index = parseInt(indexStr, 10);

          if (!(arrayKey in current)) {
            return undefined;
          }

          const arrayValue = current[arrayKey];
          if (!Array.isArray(arrayValue) || index < 0 || index >= arrayValue.length) {
            return undefined;
          }

          current = arrayValue[index];
          continue;
        }
      }

      current = current[key];
    }

    return current;
  }

  /**
   * Build template context từ workflow context
   */
  private buildTemplateContext(context: WorkflowContext): Record<string, any> {
    return {
      json: context.json,
      metadata: context.metadata,
      execution: {
        id: context.metadata.executionId,
        workflowId: context.metadata.workflowId,
        userId: context.metadata.userId,
        startTime: context.metadata.startTime,
        progress: {
          completed: context.metadata.completedNodes,
          failed: context.metadata.failedNodes,
          total: context.metadata.totalNodes
        }
      }
    };
  }

  /**
   * Validate template syntax
   */
  validateTemplate(
    template: string,
    availableNodes: string[] = []
  ): { isValid: boolean; errors: string[]; warnings: string[] } {

    const errors: string[] = [];
    const warnings: string[] = [];

    if (!template || typeof template !== 'string') {
      errors.push('Template must be a non-empty string');
      return { isValid: false, errors, warnings };
    }

    // Check for malformed templates
    const malformedPattern = /\{\{[^}]*$/g;
    if (malformedPattern.test(template)) {
      errors.push('Malformed template syntax found (unclosed {{)');
    }

    // Check {{json.node_name.field}} syntax
    const jsonPattern = /\{\{json\.([^.}]+)\.([^}]+)\}\}/g;
    let match;

    while ((match = jsonPattern.exec(template)) !== null) {
      const nodeName = match[1];

      if (availableNodes.length > 0 && !availableNodes.includes(nodeName)) {
        warnings.push(`Node "${nodeName}" not found in available nodes: ${availableNodes.join(', ')}`);
      }
    }

    const isValid = errors.length === 0;

    this.logger.debug(`Validated template`, {
      isValid,
      errorCount: errors.length,
      warningCount: warnings.length
    });

    return { isValid, errors, warnings };
  }

  /**
   * Extract variables từ template
   */
  extractVariables(template: string): {
    jsonVariables: Array<{ nodeName: string; fieldPath: string; fullPath: string }>;
    metadataVariables: string[];
    simpleVariables: string[];
  } {

    const jsonVariables: Array<{ nodeName: string; fieldPath: string; fullPath: string }> = [];
    const metadataVariables: string[] = [];
    const simpleVariables: string[] = [];

    // Extract {{json.node.field}} variables
    const jsonPattern = /\{\{json\.([^.}]+)\.([^}]+)\}\}/g;
    let match;

    while ((match = jsonPattern.exec(template)) !== null) {
      jsonVariables.push({
        nodeName: match[1],
        fieldPath: match[2],
        fullPath: match[0]
      });
    }

    // Extract {{metadata.field}} variables
    const metadataPattern = /\{\{metadata\.([^}]+)\}\}/g;
    while ((match = metadataPattern.exec(template)) !== null) {
      metadataVariables.push(match[1]);
    }

    // Extract simple {{field}} variables
    const simplePattern = /\{\{([^.}]+)\}\}/g;
    while ((match = simplePattern.exec(template)) !== null) {
      simpleVariables.push(match[1]);
    }

    this.logger.debug(`Extracted variables from template`, {
      jsonVariableCount: jsonVariables.length,
      metadataVariableCount: metadataVariables.length,
      simpleVariableCount: simpleVariables.length
    });

    return { jsonVariables, metadataVariables, simpleVariables };
  }

  /**
   * Process object templates (deep processing)
   */
  processObjectTemplate(
    obj: any,
    context: WorkflowContext
  ): any {

    if (typeof obj === 'string') {
      return this.processTemplate(obj, context);
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.processObjectTemplate(item, context));
    }

    if (obj && typeof obj === 'object') {
      const processed: Record<string, any> = {};

      for (const [key, value] of Object.entries(obj)) {
        processed[key] = this.processObjectTemplate(value, context);
      }

      return processed;
    }

    return obj;
  }
}
