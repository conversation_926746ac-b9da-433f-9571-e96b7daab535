import { Platform } from '../enums';
import { McpConfiguration } from './mcp-config.interface';
import { AgentConfig } from './agent-config.interface';
import { SelectQueryBuilder } from 'typeorm';
import { Integration } from '../entities';

export interface AuthContext {
  [key: string]: any; // Can hold JWT, API keys, tokens, etc.
}

export interface PlatformStrategy {
  readonly platform: Platform;
  
  /**
   * Build MCP client using platform-specific authentication
   */
  buildMcpClient(
    mcpConfig: Record<string, McpConfiguration> | null,
    authContext: AuthContext
  ): Promise<any>;
  
  /**
   * Get platform-specific tools for an agent
   */
  getToolsForAgent(agentConfig: AgentConfig): any[];
  
  /**
   * Customize integration query with platform-specific filtering logic
   */
  customizeIntegrationQuery(
    baseQuery: SelectQueryBuilder<Integration>,
    authContext: AuthContext,
  ): SelectQueryBuilder<Integration>;
}