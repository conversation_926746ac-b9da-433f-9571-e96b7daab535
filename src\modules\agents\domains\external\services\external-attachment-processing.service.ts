import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { HumanMessage } from '@langchain/core/messages';
import { v7 } from 'uuid';
import { CdnService } from 'src/infra';
import { TimeIntervalEnum } from '@common/dto/time-interval.enum';
import { streamImageToBase64 } from '../../../shared/utils/image-util';
import { ExternalConversationMessage } from '../entities/external-conversation-message.entity';
import { ConversationThreadsAttachmentType } from '../../../shared/enums';
import { KnowledgeFile, MediaData } from '../../../shared/entities/data';
import {
  AttachmentType,
  KnowledgeFileWithMessageId,
  MediaDataWithMessageId,
  MessageSpecificAttachments,
  ThreadAttachmentsWithMessages,
  ThreadKnowledgeFileAttachments,
  ThreadMediaAttachments,
} from '../../../shared/interfaces';

/**
 * External Attachment Processing Service
 *
 * Handles all attachment-related operations for Website AI Processor.
 * Provides same functionality as in-app but for external conversation messages.
 *
 * Key Features:
 * - Thread attachment fetching with message associations
 * - Message-specific attachment filtering
 * - Image to LangChain conversion for AI processing
 * - Knowledge file processing
 * - Type-safe attachment collections building
 * - Comprehensive error handling and logging
 *
 * Status: ✅ Refactored to match in-app logic exactly
 * - Uses MediaData and KnowledgeFile entities
 * - Supports both images and knowledge files
 * - CDN integration for image processing
 * - LangChain message format conversion
 */
@Injectable()
export class ExternalAttachmentProcessingService {
  private readonly logger = new Logger(
    ExternalAttachmentProcessingService.name,
  );

  constructor(
    @InjectRepository(MediaData)
    private readonly mediaRepo: Repository<MediaData>,
    @InjectRepository(KnowledgeFile)
    private readonly knowledgeFileRepo: Repository<KnowledgeFile>,
    private readonly cdnService: CdnService,
  ) {}

  /**
   * Fetch all thread attachments with their associated message IDs
   * Adapted from in-app logic for external conversations
   *
   * @param threadId - External customer platform data ID (thread ID)
   * @returns Object containing arrays of images and knowledge files with message associations
   */
  async fetchThreadAttachmentsWithMessages(
    threadId: string,
  ): Promise<ThreadAttachmentsWithMessages> {
    try {
      const [imagesRaw, knowledgeFilesRaw] = await Promise.all([
        this.mediaRepo
          .createQueryBuilder('md')
          .innerJoin(
            'external_conversation_message_attachment',
            'eca',
            'md.id = eca.attachment_id',
          )
          .select([
            'md.id as id',
            'md.name as name',
            'md.description as description',
            'md.tags as tags',
            'md.storage_key as "storageKey"',
            'eca.external_conversation_message_id as "messageId"',
          ])
          .where('eca.external_customer_platform_data_id = :threadId', {
            threadId,
          })
          .andWhere('eca.media_type = :mediaType', {
            mediaType: ConversationThreadsAttachmentType.IMAGE,
          })
          .getRawMany<MediaDataWithMessageId>(),

        this.knowledgeFileRepo
          .createQueryBuilder('kf')
          .innerJoin(
            'external_conversation_message_attachment',
            'eca',
            'kf.id = eca.attachment_id',
          )
          .select([
            'kf.id as id',
            'kf.name as name',
            'kf.file_id as "fileId"',
            'eca.external_conversation_message_id as "messageId"',
          ])
          .where('eca.external_customer_platform_data_id = :threadId', {
            threadId,
          })
          .andWhere('eca.media_type = :mediaType', {
            mediaType: ConversationThreadsAttachmentType.KNOWLEDGE_FILE,
          })
          .getRawMany<KnowledgeFileWithMessageId>(),
      ]);

      this.logger.debug(
        `Fetched thread attachments for external thread ${threadId}`,
        {
          threadId,
          imageCount: imagesRaw.length,
          knowledgeFileCount: knowledgeFilesRaw.length,
        },
      );

      return {
        images: imagesRaw,
        knowledgeFiles: knowledgeFilesRaw,
      };
    } catch (error) {
      this.logger.error(
        `Failed to fetch thread attachments for external thread ${threadId}:`,
        {
          threadId,
          error: error.message,
          stack: error.stack,
        },
      );
      return {
        images: [],
        knowledgeFiles: [],
      };
    }
  }

  /**
   * Process thread attachments and build collections
   * Same logic as in-app but for external conversations
   *
   * @param threadId - External customer platform data ID (thread ID)
   * @returns Processed attachment data including collections and raw data
   */
  async processThreadAttachments(threadId: string): Promise<{
    threadAttachmentsWithMessages: ThreadAttachmentsWithMessages;
    threadMediaAttachments: ThreadMediaAttachments;
    threadKnowledgeFileAttachments: ThreadKnowledgeFileAttachments;
  }> {
    try {
      // Get all thread attachments with message IDs
      const threadAttachmentsWithMessages =
        await this.fetchThreadAttachmentsWithMessages(threadId);

      // Build collections
      const threadMediaAttachments: ThreadMediaAttachments = [];
      const threadKnowledgeFileAttachments: ThreadKnowledgeFileAttachments = [];

      for (const image of threadAttachmentsWithMessages.images) {
        threadMediaAttachments.push({
          id: image.id,
          name: image.name,
          description: image.description,
          tags: image.tags,
          storageKey: image.storageKey,
          type: AttachmentType.IMAGE,
        });
      }

      for (const kf of threadAttachmentsWithMessages.knowledgeFiles) {
        threadKnowledgeFileAttachments.push({
          id: kf.id,
          name: kf.name,
          fileId: kf.fileId,
          type: AttachmentType.KNOWLEDGE_FILE,
        });
      }

      this.logger.debug(
        `Processed thread attachments for external thread ${threadId}`,
        {
          threadId,
          mediaAttachmentCount: Object.keys(threadMediaAttachments).length,
          knowledgeFileAttachmentCount: Object.keys(
            threadKnowledgeFileAttachments,
          ).length,
        },
      );

      return {
        threadAttachmentsWithMessages,
        threadMediaAttachments,
        threadKnowledgeFileAttachments,
      };
    } catch (error) {
      this.logger.error(
        `Failed to process thread attachments for external thread ${threadId}:`,
        {
          threadId,
          error: error.message,
          stack: error.stack,
        },
      );
      return {
        threadAttachmentsWithMessages: { images: [], knowledgeFiles: [] },
        threadMediaAttachments: [],
        threadKnowledgeFileAttachments: [],
      };
    }
  }

  /**
   * Process message-specific attachments by filtering thread attachments
   * Same logic as in-app but for external conversations
   *
   * @param userMessages - Array of external user messages to process attachments for
   * @param replyToMessageIds - Array of reply-to message IDs
   * @param threadAttachmentsWithMessages - Thread attachments with message associations
   * @returns Filtered attachments specific to messages and reply-to messages
   */
  async processMessageAttachments(
    userMessages: ExternalConversationMessage[],
    replyToMessageIds: string[],
    threadAttachmentsWithMessages: ThreadAttachmentsWithMessages,
  ): Promise<MessageSpecificAttachments> {
    try {
      // Filter for current user messages (for LangChain processing)
      const userMessageIds = userMessages
        .filter((msg) => msg.hasAttachments)
        .map((msg) => msg.id);

      this.logger.debug('Processing external message attachments', {
        userMessageIds,
        replyToMessageIds,
        totalThreadImages: threadAttachmentsWithMessages.images.length,
        totalThreadKnowledgeFiles:
          threadAttachmentsWithMessages.knowledgeFiles.length,
      });

      const messageSpecificImages = threadAttachmentsWithMessages.images.filter(
        (attachment) => userMessageIds.includes(attachment.messageId),
      );

      const messageSpecificKnowledgeFiles =
        threadAttachmentsWithMessages.knowledgeFiles.filter((attachment) =>
          userMessageIds.includes(attachment.messageId),
        );

      const replyToSpecificImages = threadAttachmentsWithMessages.images.filter(
        (attachment) => replyToMessageIds.includes(attachment.messageId),
      );

      const replyToSpecificKnowledgeFiles =
        threadAttachmentsWithMessages.knowledgeFiles.filter((attachment) =>
          replyToMessageIds.includes(attachment.messageId),
        );

      this.logger.debug('Processed external message-specific attachments', {
        messageSpecificImageCount: messageSpecificImages.length,
        messageSpecificKnowledgeFileCount: messageSpecificKnowledgeFiles.length,
        replyToSpecificImageCount: replyToSpecificImages.length,
        replyToSpecificKnowledgeFileCount: replyToSpecificKnowledgeFiles.length,
      });

      return {
        messageSpecificImages: messageSpecificImages,
        messageSpecificKnowledgeFiles,
        replyToSpecificImages: replyToSpecificImages,
        replyToSpecificKnowledgeFiles,
      };
    } catch (error) {
      this.logger.error('Failed to process external message attachments:', {
        userMessageCount: userMessages.length,
        replyToMessageCount: replyToMessageIds.length,
        error: error.message,
        stack: error.stack,
      });
      return {
        messageSpecificImages: [],
        messageSpecificKnowledgeFiles: [],
        replyToSpecificImages: [],
        replyToSpecificKnowledgeFiles: [],
      };
    }
  }

  /**
   * Convert images to LangChain HumanMessage format
   * Exact same logic as in-app
   *
   * @param messageSpecificImages - Array of images to convert
   * @returns Array of LangChain HumanMessage objects with image content, or null if no images
   */
  async convertImagesToLangChain(
    messageSpecificImages: MediaDataWithMessageId[],
  ): Promise<HumanMessage[] | null> {
    try {
      if (messageSpecificImages.length === 0) {
        this.logger.debug('No external images to convert to LangChain format');
        return null;
      }

      this.logger.debug(
        `Converting ${messageSpecificImages.length} external images to LangChain format`,
      );

      const humanMessages = await Promise.all(
        messageSpecificImages.map(async (image) => {
          try {
            const viewUrl = this.cdnService.generateUrlView(
              image.storageKey,
              TimeIntervalEnum.FIVE_MINUTES,
            );

            this.logger.debug(
              `Generated view URL for external image ${image.id}`,
              {
                imageId: image.id,
                imageName: image.name,
                viewUrl,
              },
            );

            const { base64String, mimeType } = await streamImageToBase64(
              viewUrl as string,
            );

            this.logger.debug(
              `Converted external image ${image.id} to base64`,
              {
                imageId: image.id,
                mimeType,
                base64Length: base64String.length,
              },
            );

            return new HumanMessage({
              id: v7(),
              content: [
                {
                  type: 'image_url',
                  image_url: {
                    url: `data:${mimeType};base64,${base64String}`,
                  },
                },
              ],
            });
          } catch (imageError) {
            this.logger.error(
              `Failed to convert external image ${image.id} to LangChain format:`,
              {
                imageId: image.id,
                imageName: image.name,
                storageKey: image.storageKey,
                error: imageError.message,
                stack: imageError.stack,
              },
            );
            throw imageError; // Re-throw to handle at the outer level
          }
        }),
      );

      this.logger.debug(
        `Successfully converted ${humanMessages.length} external images to LangChain format`,
      );
      return humanMessages;
    } catch (error) {
      this.logger.error(
        'Failed to convert external images to LangChain format:',
        {
          imageCount: messageSpecificImages.length,
          error: error.message,
          stack: error.stack,
        },
      );
      return null; // Return null on error to allow processing to continue
    }
  }
}
