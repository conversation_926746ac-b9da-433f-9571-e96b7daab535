import { Repository } from 'typeorm';
import { UserConvertCustomerMemory } from '../domains/external/entities/user-convert-customer-memory.entity';
import { WebsiteInfo } from '../platforms/website/interfaces/website-info.interface';
import {
  UserConvertCustomerInfo,
  UserInfo,
} from '../shared/interfaces';

/**
 * Visitor Context Only Prompt Builder (without memory)
 * Generates XML representation of visitor context for personalized agent responses (without memory)
 * Uses attribute-heavy approach for minimal token usage
 */
export function buildVisitorContextOnlyPrompt(
  userConvertCustomer?: UserConvertCustomerInfo,
  websiteOwner?: UserInfo,
  websiteInfo?: WebsiteInfo,
): string {
  if (!userConvertCustomer) {
    return ''; // No visitor info available
  }

  const parts: string[] = ['<visitor-context>'];

  // Visitor identity section
  const identityAttrs: string[] = [`id="${userConvertCustomer.id}"`];
  if (userConvertCustomer.name)
    identityAttrs.push(`name="${userConvertCustomer.name}"`);
  if (userConvertCustomer.email)
    identityAttrs.push(`email="${userConvertCustomer.email.join(', ')}"`);
  if (userConvertCustomer.phone)
    identityAttrs.push(`phone="${userConvertCustomer.phone}"`);
  if (userConvertCustomer.countryCode)
    identityAttrs.push(`country-code="${userConvertCustomer.countryCode}"`);
  if (userConvertCustomer.address)
    identityAttrs.push(`address="${userConvertCustomer.address}"`);

  if (identityAttrs.length > 0) {
    parts.push(`  <visitor ${identityAttrs.join(' ')} />`);
  }

  // Visitor tags (interests/categories)
  if (userConvertCustomer.tags && userConvertCustomer.tags.length > 0) {
    const tagString = userConvertCustomer.tags.join(',');
    parts.push(`  <interests tags="${tagString}" />`);
  }

  // Visitor metadata (custom fields)
  if (
    userConvertCustomer.metadata &&
    Object.keys(userConvertCustomer.metadata).length > 0
  ) {
    const metadataEntries = Object.entries(userConvertCustomer.metadata)
      .filter(([key, value]) => value !== null && value !== undefined)
      .map(([key, value]) => {
        const escapedValue = String(value).replace(/"/g, '&quot;');
        return `${key}="${escapedValue}"`;
      })
      .join(' ');

    if (metadataEntries) {
      parts.push(`  <visitor-metadata ${metadataEntries} />`);
    }
  }

  // Website owner context (business information)
  if (websiteOwner) {
    const ownerAttrs: string[] = [`owner-id="${websiteOwner.userId}"`];
    if (websiteOwner.fullName)
      ownerAttrs.push(`owner-name="${websiteOwner.fullName}"`);
    if (websiteOwner.email)
      ownerAttrs.push(`contact-email="${websiteOwner.email}"`);
    if (websiteOwner.timezone)
      ownerAttrs.push(`timezone="${websiteOwner.timezone}"`);
    if (websiteOwner.currency)
      ownerAttrs.push(`currency="${websiteOwner.currency}"`);

    parts.push(`  <business ${ownerAttrs.join(' ')} />`);
  }

  // Website environment context
  if (websiteInfo) {
    // Browser information
    if (websiteInfo.clientSide?.browserInfo) {
      const browser = websiteInfo.clientSide.browserInfo;
      const browserAttrs: string[] = [];
      if (browser.platform) browserAttrs.push(`platform="${browser.platform}"`);
      if (browser.language) browserAttrs.push(`language="${browser.language}"`);
      if (browser.vendor) browserAttrs.push(`vendor="${browser.vendor}"`);

      if (browserAttrs.length > 0) {
        parts.push(`  <browser ${browserAttrs.join(' ')} />`);
      }
    }

    // Device/screen information
    if (websiteInfo.clientSide?.screenInfo) {
      const screen = websiteInfo.clientSide.screenInfo;
      const screenAttrs: string[] = [];
      if (screen.width && screen.height) {
        screenAttrs.push(`resolution="${screen.width}x${screen.height}"`);
      }
      if (screen.orientation)
        screenAttrs.push(`orientation="${screen.orientation}"`);

      if (screenAttrs.length > 0) {
        parts.push(`  <device ${screenAttrs.join(' ')} />`);
      }
    }

    // Page context
    if (websiteInfo.clientSide?.pageInfo) {
      const page = websiteInfo.clientSide.pageInfo;
      const pageAttrs: string[] = [];
      if (page.url) pageAttrs.push(`url="${page.url}"`);
      if (page.title) pageAttrs.push(`title="${page.title}"`);
      if (page.referrer) pageAttrs.push(`referrer="${page.referrer}"`);

      if (pageAttrs.length > 0) {
        parts.push(`  <page ${pageAttrs.join(' ')} />`);
      }
    }

    // Geographic context
    if (websiteInfo.clientSide?.location) {
      const location = websiteInfo.clientSide.location;
      const locationAttrs: string[] = [];
      if (location.latitude && location.longitude) {
        locationAttrs.push(
          `coordinates="${location.latitude},${location.longitude}"`,
        );
      }
      if (location.accuracy)
        locationAttrs.push(`accuracy="${location.accuracy}"`);

      if (locationAttrs.length > 0) {
        parts.push(`  <location ${locationAttrs.join(' ')} />`);
      }
    }

    // Time context
    if (websiteInfo.clientSide?.timeInfo) {
      const timeInfo = websiteInfo.clientSide.timeInfo;
      const timeAttrs: string[] = [];
      if (timeInfo.timezone) timeAttrs.push(`timezone="${timeInfo.timezone}"`);
      if (timeInfo.locale) timeAttrs.push(`locale="${timeInfo.locale}"`);

      if (timeAttrs.length > 0) {
        parts.push(`  <timing ${timeAttrs.join(' ')} />`);
      }
    }
  }

  parts.push('</visitor-context>');
  return parts.join('\n');
}

/**
 * Visitor Memory Prompt Builder with Repository Access
 * Follows in-app pattern of directly fetching memory data
 * Generates comprehensive visitor context including conversation history
 */
export const buildVisitorContextPrompt = async (
  visitorId: string,
  userConvertCustomerMemoryRepository: Repository<UserConvertCustomerMemory>,
  userConvertCustomer?: UserConvertCustomerInfo,
  websiteOwner?: UserInfo,
  websiteInfo?: WebsiteInfo,
): Promise<string> => {
  if (!visitorId) {
    return ''; // No visitor ID, no context
  }

  const parts: string[] = [];

  // Add visitor context (without memory)
  const contextPrompt = buildVisitorContextOnlyPrompt(
    userConvertCustomer,
    websiteOwner,
    websiteInfo,
  );
  if (contextPrompt) {
    parts.push(contextPrompt);
  }

  // Fetch and add visitor memories
  try {
    const memories = await userConvertCustomerMemoryRepository
      .createQueryBuilder('memory')
      .where('memory.userConvertCustomerId = :visitorId', { visitorId })
      .orderBy('memory.createdAt', 'DESC')
      .limit(10) // Limit to recent memories
      .getMany();

    if (memories.length > 0) {
      const memoryParts: string[] = ['<visitor-memories>'];

      memories.forEach((memory) => {
        const attributes: string[] = [`id="${memory.id}"`];

        // Add created date if available (convert timestamp to date)
        if (memory.createdAt && typeof memory.createdAt === 'string') {
          try {
            const timestamp = parseInt(memory.createdAt, 10);
            if (!isNaN(timestamp) && timestamp > 0) {
              const date = new Date(timestamp);
              if (!isNaN(date.getTime())) {
                const dateString = date.toISOString().split('T')[0];
                attributes.push(`created="${dateString}"`);
              }
            }
          } catch (error) {
            // Skip invalid timestamp, continue without date
            console.warn(
              `Invalid timestamp for visitor memory ${memory.id}: ${memory.createdAt}`,
            );
          }
        }

        memoryParts.push(`  <memory ${attributes.join(' ')}>`);

        // Content field - main memory content
        if (memory.content) {
          // Escape XML characters in content
          const escapedContent = memory.content
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;');

          memoryParts.push(`    <content>${escapedContent}</content>`);
        }

        // Metadata (visitor preferences, tags, categories)
        if (memory.metadata && Object.keys(memory.metadata).length > 0) {
          const metadataEntries = Object.entries(memory.metadata)
            .filter(([key, value]) => value !== null && value !== undefined)
            .map(([key, value]) => {
              // Handle different data types appropriately
              let stringValue: string;
              if (typeof value === 'object') {
                stringValue = JSON.stringify(value);
              } else {
                stringValue = String(value);
              }

              // Escape XML characters
              const escapedValue = stringValue
                .replace(/&/g, '&amp;')
                .replace(/"/g, '&quot;');

              return `${key}="${escapedValue}"`;
            })
            .join(' ');

          if (metadataEntries) {
            memoryParts.push(`    <metadata ${metadataEntries} />`);
          }
        }

        memoryParts.push('  </memory>');
      });

      memoryParts.push('</visitor-memories>');
      parts.push(memoryParts.join('\n'));
    }
  } catch (error) {
    console.error('Error fetching visitor memories:', error);
    // Continue without memories rather than breaking the prompt
  }

  return parts.join('\n\n');
};
