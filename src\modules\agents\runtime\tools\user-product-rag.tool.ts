import { Injectable, Logger } from '@nestjs/common';
import { StructuredTool, ToolRunnableConfig } from '@langchain/core/tools';
import { CallbackManagerForToolRun } from '@langchain/core/callbacks/manager';
import { z } from 'zod';
import { RagEngineService } from 'src/infra';
import { InAppSupervisorWorkersConfig } from '../../platforms/in-app/graph-configs/in-app-supervisor-workers.config';
import { AssistantSpendingType } from '../../enums';
import { UserBillingService } from '../../services';

@Injectable()
export class UserProductRAGTool extends StructuredTool {
  name: string;
  description: string;
  schema: z.ZodSchema;
  private readonly rPointEmbeddingRate = 2;
  private readonly logger = new Logger(UserProductRAGTool.name);

  constructor(
    private readonly ragEngineService: RagEngineService,
    private readonly userBilling: UserBillingService,
  ) {
    super();
    this.logger.log('Initializing UserProductRAGTool');
    this.name = 'search_products';
    this.description =
      'Search through user products (physical, digital, events, services) to find relevant information based on queries. ' +
      'Use this tool when the user wants to find specific products or product information. ' +
      "Generate appropriate search queries based on the user's question and product requirements.";

    this.schema = z.object({
      query: z
        .string()
        .describe(
          'The search query to find relevant products',
        ),
      product_types: z
        .array(z.enum(['physical', 'digital', 'event', 'service']))
        .optional()
        .describe('Optional array of product types to filter by'),
      max_results: z
        .number()
        .min(1)
        .max(100)
        .optional()
        .describe('Optional maximum number of results to return'),
      include_sub_products: z
        .boolean()
        .optional()
        .describe('Optional flag to include sub-products in search results'),
      customer_product_ids: z
        .array(z.string())
        .min(1, 'Customer product IDs are required to search products')
        .describe(
          'Array of customer product IDs to search within. If not provided, all accessible products will be searched.',
        ),
    });
  }

  protected async _call(
    arg: {
      query: string;
      product_types?: ('physical' | 'digital' | 'event' | 'service')[];
      max_results?: number;
      include_sub_products?: boolean;
      customer_product_ids: number[];
    },
    runManager?: CallbackManagerForToolRun,
    parentConfig?: ToolRunnableConfig<InAppSupervisorWorkersConfig>,
  ): Promise<string> {
    try {
      const userId = parentConfig?.configurable?.currentUser?.user?.userId;
      if (!userId) {
        return 'internal error: User ID is required to search products. Stop using this tool.';
      }

      // Call the RAG engine service to search products with default settings
      const searchResult = await this.ragEngineService.searchProducts({
        query: arg.query,
        product_types: arg.product_types,
        limit: arg.max_results || 10, // Default to 10 if not provided
        include_sub_products: arg.include_sub_products ?? true,
        customer_product_ids: arg.customer_product_ids,
        threshold: 0.5, // Default threshold
        use_rerank: true, // Default to using rerank
      });
      
      const usageToken = searchResult.total_tokens_used || 0;
      this.logger.debug(
        `User ${userId} used ${usageToken} points for product search query "${arg.query}"`,
      );
      const usageRPoint = usageToken * this.rPointEmbeddingRate;
      this.logger.debug(
        `User ${userId} will be charged ${usageRPoint} R-Points for product search query "${arg.query}"`,
      );
      await this.userBilling.updateUserPointBalance(
        userId,
        usageRPoint,
        AssistantSpendingType.IN_APP,
      );
      await this.userBilling.createSpendingRecords(
        [
          {
            agentId: parentConfig?.configurable?.executorAgent?.id as string,
            model: 'jina-embedding-v4',
            inputTokens: usageToken,
            outputTokens: 0,
            totalTokens: usageToken,
            pointCost: usageRPoint,
          },
        ],
        userId,
        AssistantSpendingType.IN_APP,
        parentConfig?.configurable?.run_id || '',
      );

      // Format the results for the LLM
      if (!searchResult.success || searchResult.results.length === 0) {
        return `No relevant products found for query: "${arg.query}"`;
      }

      const formattedResults = searchResult.results
        .map((result, index) => {
          let resultText = `--- Product Result ${index + 1} ---\n`;
          resultText += `Product Type: ${result.product_type}\n`;
          resultText += `Table Type: ${result.table_type}\n`;
          resultText += `Product ID: ${result.id}\n`;
          resultText += `Similarity Score: ${result.similarity.toFixed(3)}\n`;

          if (result.rerank_score) {
            resultText += `Rerank Score: ${result.rerank_score.toFixed(3)}\n`;
          }

          // Add specific product IDs based on product type
          if (result.physical_product_id) {
            resultText += `Physical Product ID: ${result.physical_product_id}\n`;
          }
          if (result.digital_product_id) {
            resultText += `Digital Product ID: ${result.digital_product_id}\n`;
          }
          if (result.event_product_id) {
            resultText += `Event Product ID: ${result.event_product_id}\n`;
          }
          if (result.service_product_id) {
            resultText += `Service Product ID: ${result.service_product_id}\n`;
          }
          if (result.customer_product_id) {
            resultText += `Customer Product ID: ${result.customer_product_id}\n`;
          }
          if (result.variant_id) {
            resultText += `Variant ID: ${result.variant_id}\n`;
          }
          if (result.version_id) {
            resultText += `Version ID: ${result.version_id}\n`;
          }
          if (result.ticket_id) {
            resultText += `Ticket ID: ${result.ticket_id}\n`;
          }
          if (result.service_package_id) {
            resultText += `Service Package ID: ${result.service_package_id}\n`;
          }

          return resultText;
        })
        .join('\n');

      let summary = `Found ${searchResult.total_found} relevant products for "${arg.query}" (showing top ${searchResult.results.length}):\n`;
      summary += `Recognized Product Types: ${searchResult.recognized_product_types.join(', ')}\n`;
      summary += `Search Strategy: ${searchResult.search_strategy}\n\n`;
      summary += formattedResults;

      // Log search metadata for debugging
      if (runManager) {
        runManager.handleText(
          `Product search executed in ${searchResult.execution_time_ms}ms with threshold ${searchResult.threshold_used}`,
        );
      }

      return summary;
    } catch (error) {
      const errorMessage = `Error searching products: ${error.message}`;
      if (runManager) {
        runManager.handleText(errorMessage);
      }
      throw new Error(errorMessage);
    }
  }
}