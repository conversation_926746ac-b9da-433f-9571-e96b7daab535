import { Modu<PERSON> } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { ConfigModule } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';
import { QueueName } from '../../../queue';
import { ZaloConsultationSequenceProcessor } from './zalo-consultation-sequence.processor';
import { ZaloModule } from '../../../shared/services/zalo/zalo.module';

/**
 * Module cho Zalo Consultation Sequence Worker
 * Xử lý các job gửi chuỗi tin nhắn tư vấn Zalo
 */
@Module({
  imports: [
    ConfigModule,
    BullModule.registerQueue({
      name: QueueName.ZALO_CONSULTATION_SEQUENCE,
    }),
    HttpModule.register({
      timeout: 30000,
      maxRedirects: 5,
    }),
    ZaloModule,
  ],
  providers: [ZaloConsultationSequenceProcessor],
  exports: [ZaloConsultationSequenceProcessor],
})
export class ZaloConsultationSequenceModule {}
