import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { lastValueFrom } from 'rxjs';
import { AxiosError } from 'axios';
import { AppException, ErrorCode } from '@common/exceptions';
import {
  ZaloZnsMessage,
  ZaloZnsSendResult,
  ZaloZnsTemplate,
  ZaloZnsTemplateList,
  ZaloCreateZnsTemplateRequest,
  ZaloUpdateZnsTemplateRequest,
  ZaloZnsUploadImageResult,
  ZaloZnsHashPhoneMessage,
  ZaloZnsDevModeMessage,
  ZaloZnsRsaMessage,
  ZaloZnsJourneyMessage,
  ZaloZnsStatusInfo,
  ZaloZnsQuotaInfo,
  ZaloZnsAllowedContent,
  ZaloZnsTemplateSampleData,
  ZaloZnsCustomerRating,
  ZaloZnsCustomerRatingList,
  ZaloZnsQualityInfo,
  ZaloZnsQualityHistoryList,
  ZaloZnsTemplateStatus,
} from './zalo.interface';

// <PERSON>hai báo thêm để TypeScript hiểu về Express.Multer.File
declare global {
  namespace Express {
    namespace Multer {
      interface File {
        fieldname: string;
        originalname: string;
        encoding: string;
        mimetype: string;
        size: number;
        destination: string;
        filename: string;
        path: string;
        buffer: Buffer;
      }
    }
  }
}

/**
 * Service cho Zalo Notification Service (ZNS)
 *
 * Điều kiện sử dụng ZNS API:
 * - Official Account phải được duyệt và có quyền gửi ZNS
 * - Access token hợp lệ của Official Account
 * - Template ZNS phải được duyệt trước khi sử dụng
 * - Số điện thoại người nhận phải đúng định dạng và hợp lệ
 * - Dữ liệu template phải khớp với các tham số đã định nghĩa
 * - Tuân thủ giới hạn số lượng tin nhắn theo gói dịch vụ
 *
 * Tài liệu tham khảo: https://developers.zalo.me/docs/zalo-notification-service/
 */
@Injectable()
export class ZaloZnsService {
  private readonly logger = new Logger(ZaloZnsService.name);
  private readonly znsApiUrl =
    'https://business.openapi.zalo.me/message/template';
  private readonly templateApiUrl = 'https://business.openapi.zalo.me/template';
  private readonly appId: string;
  private readonly appSecret: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    const appId = this.configService.get<string>('ZALO_APP_ID');
    const appSecret = this.configService.get<string>('ZALO_APP_SECRET');

    if (!appId || !appSecret) {
      throw new Error(
        'ZALO_APP_ID or ZALO_APP_SECRET is not defined in configuration',
      );
    }

    this.appId = appId;
    this.appSecret = appSecret;
  }

  /**
   * Chuẩn hóa số điện thoại Việt Nam sang định dạng quốc tế cho Zalo API
   * @param phone Số điện thoại đầu vào
   * @returns Số điện thoại đã chuẩn hóa (84xxxxxxxxx)
   */
  private normalizeVietnamesePhoneNumber(phone: string): string {
    // Loại bỏ tất cả ký tự không phải số
    const cleanPhone = phone.replace(/\D/g, '');

    // Nếu đã có mã quốc gia 84, giữ nguyên
    if (cleanPhone.startsWith('84') && cleanPhone.length === 11) {
      return cleanPhone;
    }

    // Nếu bắt đầu bằng 0 (định dạng Việt Nam), chuyển sang 84
    if (cleanPhone.startsWith('0') && cleanPhone.length === 10) {
      return '84' + cleanPhone.substring(1);
    }

    // Nếu có 9 số (thiếu số 0 đầu), thêm 84
    if (cleanPhone.length === 9) {
      return '84' + cleanPhone;
    }

    // Trường hợp khác, trả về số gốc đã làm sạch
    return cleanPhone;
  }

  /**
   * Gửi tin nhắn ZNS
   * @param accessToken Access token của Official Account
   * @param message Thông tin tin nhắn ZNS
   * @returns Kết quả gửi tin nhắn
   * @throws AppException nếu có lỗi xảy ra
   */
  async sendZnsMessage(
    accessToken: string,
    message: ZaloZnsMessage,
  ): Promise<ZaloZnsSendResult> {
    try {
      // Chuẩn hóa số điện thoại trước khi gửi
      const normalizedPhone = this.normalizeVietnamesePhoneNumber(
        message.phone,
      );

      const data = {
        phone: normalizedPhone,
        template_id: message.template_id,
        template_data: message.template_data,
        tracking_id: message.tracking_id || this.generateTrackingId(),
      };

      const headers = {
        'Content-Type': 'application/json',
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.post<{
          error: number;
          message: string;
          data?: ZaloZnsSendResult;
        }>(this.znsApiUrl, data, { headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi gửi tin nhắn ZNS: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi gửi tin nhắn ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error sending ZNS message: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi gửi tin nhắn ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi gửi tin nhắn ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Kiểm tra trạng thái tin nhắn ZNS
   * @param accessToken Access token của Official Account
   * @param messageId ID của tin nhắn
   * @returns Trạng thái tin nhắn
   * @throws AppException nếu có lỗi xảy ra
   */
  async checkZnsMessageStatus(
    accessToken: string,
    messageId: string,
  ): Promise<{ message_id: string; status: string; delivered_time?: number }> {
    try {
      const url = 'https://business.openapi.zalo.me/message/status';
      const params = { message_id: messageId };
      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: {
            message_id: string;
            status: string;
            delivered_time?: number;
          };
        }>(url, { params, headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi kiểm tra trạng thái tin nhắn ZNS: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi kiểm tra trạng thái tin nhắn ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error checking ZNS message status: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi kiểm tra trạng thái tin nhắn ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi kiểm tra trạng thái tin nhắn ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách template ZNS
   * Endpoint: GET https://business.openapi.zalo.me/template/all
   * @param accessToken Access token của Official Account
   * @param offset Offset cho phân trang (tùy chọn)
   * @param limit Số lượng template tối đa trả về (tùy chọn)
   * @param status Trạng thái của template muốn lấy (tùy chọn)
   *   - status = 1: Lấy các template có trạng thái Enable
   *   - status = 2: Lấy các template có trạng thái Pending review
   *   - status = 3: Lấy các template có trạng thái Reject
   *   - status = 4: Lấy các template có trạng thái Disable
   *   - Không truyền: Lấy template thuộc mọi trạng thái
   * @returns Danh sách template
   * @throws AppException nếu có lỗi xảy ra
   */
  async getZnsTemplates(
    accessToken: string,
    offset?: number,
    limit?: number,
    status?: ZaloZnsTemplateStatus,
  ): Promise<ZaloZnsTemplateList> {
    try {
      const url = `${this.templateApiUrl}/all`;
      const params: any = {};
      if (offset !== undefined) params.offset = offset;
      if (limit !== undefined) params.limit = limit;
      if (status !== undefined) params.status = status;

      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZaloZnsTemplate[];
          metadata?: {
            total: number;
          };
        }>(url, { params, headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy danh sách template ZNS: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy danh sách template ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return {
        data: response.data.data,
        metadata: response.data.metadata || {
          total: response.data.data.length,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error getting ZNS templates: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy danh sách template ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy danh sách template ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Lấy chi tiết template ZNS
   * Endpoint: GET https://business.openapi.zalo.me/template/info
   * @param accessToken Access token của Official Account
   * @param templateId ID của template (string)
   * @returns Chi tiết template
   * @throws AppException nếu có lỗi xảy ra
   */
  async getZnsTemplateDetail(
    accessToken: string,
    templateId: string,
  ): Promise<ZaloZnsTemplate> {
    try {
      const url = `${this.templateApiUrl}/info/v2`;
      const params = { template_id: templateId };
      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZaloZnsTemplate;
        }>(url, { params, headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy chi tiết template ZNS: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy chi tiết template ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting ZNS template detail: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy chi tiết template ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy chi tiết template ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Tạo template ZNS mới
   * Endpoint: POST https://business.openapi.zalo.me/template/create
   * @param accessToken Access token của Official Account
   * @param templateData Thông tin template cần tạo
   * @returns Kết quả tạo template
   * @throws AppException nếu có lỗi xảy ra
   */
  async createZnsTemplate(
    accessToken: string,
    templateData: ZaloCreateZnsTemplateRequest,
  ): Promise<{ template_id: string; status: string }> {
    try {
      const url = `${this.templateApiUrl}/create`;
      const data = {
        templateName: templateData.templateName,
        templateType: templateData.templateType,
        lang: templateData.lang || 'vi',
        timeout: templateData.timeout || 86400,
        previewUrl: templateData.previewUrl,
        templateContent: templateData.templateContent,
        listButton: templateData.listButton,
        listElement: templateData.listElement,
      };

      const headers = {
        'Content-Type': 'application/json',
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.post<{
          error: number;
          message: string;
          data?: { template_id: string; status: string };
        }>(url, data, { headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi tạo template ZNS: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi tạo template ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error creating ZNS template: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi tạo template ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi tạo template ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật template ZNS
   * Endpoint: POST https://business.openapi.zalo.me/template/update
   * @param accessToken Access token của Official Account
   * @param templateData Thông tin template cần cập nhật
   * @returns Kết quả cập nhật template
   * @throws AppException nếu có lỗi xảy ra
   */
  async updateZnsTemplate(
    accessToken: string,
    templateData: ZaloUpdateZnsTemplateRequest,
  ): Promise<{ template_id: string; status: string }> {
    try {
      const url = `${this.templateApiUrl}/update`;
      const data = {
        templateId: templateData.templateId,
        templateName: templateData.templateName,
        timeout: templateData.timeout,
        previewUrl: templateData.previewUrl,
        templateContent: templateData.templateContent,
        listButton: templateData.listButton,
        listElement: templateData.listElement,
      };

      const headers = {
        'Content-Type': 'application/json',
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.post<{
          error: number;
          message: string;
          data?: { template_id: string; status: string };
        }>(url, data, { headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi cập nhật template ZNS: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi cập nhật template ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error updating ZNS template: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi cập nhật template ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi cập nhật template ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Upload ảnh cho template ZNS
   * Endpoint: POST https://business.openapi.zalo.me/template/upload_image
   * @param accessToken Access token của Official Account
   * @param file File ảnh cần upload
   * @returns Kết quả upload ảnh
   * @throws AppException nếu có lỗi xảy ra
   */
  async uploadZnsImage(
    accessToken: string,
    file: Express.Multer.File,
  ): Promise<ZaloZnsUploadImageResult> {
    try {
      const url = `${this.templateApiUrl}/upload_image`;

      // Tạo FormData cho upload file
      const formData = new (require('form-data'))();
      formData.append('file', file.buffer, {
        filename: file.originalname,
        contentType: file.mimetype,
      });

      const headers = {
        ...formData.getHeaders(),
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.post<{
          error: number;
          message: string;
          data?: ZaloZnsUploadImageResult;
        }>(url, formData, { headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi upload ảnh ZNS: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi upload ảnh ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error uploading ZNS image: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi upload ảnh ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi upload ảnh ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Gửi tin nhắn ZNS sử dụng hash phone
   * Endpoint: POST https://business.openapi.zalo.me/message/template/hash_phone
   * @param accessToken Access token của Official Account
   * @param message Thông tin tin nhắn ZNS với hash phone
   * @returns Kết quả gửi tin nhắn
   * @throws AppException nếu có lỗi xảy ra
   */
  async sendZnsHashPhoneMessage(
    accessToken: string,
    message: ZaloZnsHashPhoneMessage,
  ): Promise<ZaloZnsSendResult> {
    try {
      const url = `${this.znsApiUrl}/hash_phone`;
      const data = {
        phone_hash: message.phone_hash,
        template_id: message.template_id,
        template_data: message.template_data,
        tracking_id: message.tracking_id || this.generateTrackingId(),
      };

      const headers = {
        'Content-Type': 'application/json',
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.post<{
          error: number;
          message: string;
          data?: ZaloZnsSendResult;
        }>(url, data, { headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi gửi tin nhắn ZNS hash phone: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi gửi tin nhắn ZNS hash phone: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error sending ZNS hash phone message: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi gửi tin nhắn ZNS hash phone: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi gửi tin nhắn ZNS hash phone: ${error.message}`,
      );
    }
  }

  /**
   * Gửi tin nhắn ZNS development mode
   * Endpoint: POST https://business.openapi.zalo.me/message/template
   * @param accessToken Access token của Official Account
   * @param message Thông tin tin nhắn ZNS development mode
   * @returns Kết quả gửi tin nhắn
   * @throws AppException nếu có lỗi xảy ra
   */
  async sendZnsDevModeMessage(
    accessToken: string,
    message: ZaloZnsDevModeMessage,
  ): Promise<ZaloZnsSendResult> {
    try {
      const data = {
        phone: message.phone,
        template_id: message.template_id,
        template_data: message.template_data,
        tracking_id: message.tracking_id || this.generateTrackingId(),
        mode: message.mode,
      };

      const headers = {
        'Content-Type': 'application/json',
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.post<{
          error: number;
          message: string;
          data?: ZaloZnsSendResult;
        }>(this.znsApiUrl, data, { headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi gửi tin nhắn ZNS development mode: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi gửi tin nhắn ZNS development mode: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error sending ZNS development mode message: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi gửi tin nhắn ZNS development mode: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi gửi tin nhắn ZNS development mode: ${error.message}`,
      );
    }
  }

  /**
   * Gửi tin nhắn ZNS với mã hóa RSA
   * Endpoint: POST https://business.openapi.zalo.me/message/template
   * @param accessToken Access token của Official Account
   * @param message Thông tin tin nhắn ZNS với mã hóa RSA
   * @returns Kết quả gửi tin nhắn
   * @throws AppException nếu có lỗi xảy ra
   */
  async sendZnsRsaMessage(
    accessToken: string,
    message: ZaloZnsRsaMessage,
  ): Promise<ZaloZnsSendResult> {
    try {
      // Chuẩn hóa số điện thoại trước khi gửi (nếu chưa được mã hóa RSA)
      const normalizedPhone = this.normalizeVietnamesePhoneNumber(
        message.phone,
      );

      const data = {
        phone: normalizedPhone, // Số điện thoại đã được chuẩn hóa
        template_id: message.template_id,
        template_data: message.template_data,
        tracking_id: message.tracking_id || this.generateTrackingId(),
      };

      const headers = {
        'Content-Type': 'application/json',
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.post<{
          error: number;
          message: string;
          data?: ZaloZnsSendResult;
        }>(this.znsApiUrl, data, { headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi gửi tin nhắn ZNS RSA: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi gửi tin nhắn ZNS RSA: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error sending ZNS RSA message: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi gửi tin nhắn ZNS RSA: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi gửi tin nhắn ZNS RSA: ${error.message}`,
      );
    }
  }

  /**
   * Gửi tin nhắn ZNS Journey
   * Endpoint: POST https://business.openapi.zalo.me/message/template/journey
   * @param accessToken Access token của Official Account
   * @param message Thông tin tin nhắn ZNS Journey
   * @returns Kết quả gửi tin nhắn
   * @throws AppException nếu có lỗi xảy ra
   */
  async sendZnsJourneyMessage(
    accessToken: string,
    message: ZaloZnsJourneyMessage,
  ): Promise<ZaloZnsSendResult> {
    try {
      const url = `${this.znsApiUrl}/journey`;
      const data = {
        phone: message.phone,
        template_id: message.template_id,
        template_data: message.template_data,
        journey_id: message.journey_id,
        tracking_id: message.tracking_id || this.generateTrackingId(),
      };

      const headers = {
        'Content-Type': 'application/json',
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.post<{
          error: number;
          message: string;
          data?: ZaloZnsSendResult;
        }>(url, data, { headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi gửi tin nhắn ZNS Journey: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi gửi tin nhắn ZNS Journey: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error sending ZNS Journey message: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi gửi tin nhắn ZNS Journey: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi gửi tin nhắn ZNS Journey: ${error.message}`,
      );
    }
  }

  // ===== CÁC API TRUY XUẤT THÔNG TIN ZNS =====

  /**
   * Lấy thông tin trạng thái ZNS của Official Account
   * Endpoint: GET https://business.openapi.zalo.me/message/status
   * @param accessToken Access token của Official Account
   * @returns Thông tin trạng thái ZNS
   * @throws AppException nếu có lỗi xảy ra
   */
  async getZnsStatus(accessToken: string): Promise<ZaloZnsStatusInfo> {
    try {
      const url = `${this.znsApiUrl}/status`;
      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZaloZnsStatusInfo;
        }>(url, { headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy trạng thái ZNS: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy trạng thái ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting ZNS status: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy trạng thái ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy trạng thái ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin quota ZNS của Official Account
   * Endpoint: GET https://business.openapi.zalo.me/message/quota
   * @param accessToken Access token của Official Account
   * @returns Thông tin quota ZNS
   * @throws AppException nếu có lỗi xảy ra
   */
  async getZnsQuota(accessToken: string): Promise<ZaloZnsQuotaInfo> {
    try {
      const url = `${this.znsApiUrl}/quota`;
      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZaloZnsQuotaInfo;
        }>(url, { headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy quota ZNS: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy quota ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting ZNS quota: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy quota ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy quota ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin loại nội dung ZNS được phép gửi
   * Endpoint: GET https://business.openapi.zalo.me/message/content_types
   * @param accessToken Access token của Official Account
   * @returns Thông tin loại nội dung được phép gửi
   * @throws AppException nếu có lỗi xảy ra
   */
  async getZnsAllowedContentTypes(
    accessToken: string,
  ): Promise<ZaloZnsAllowedContent> {
    try {
      const url = `${this.znsApiUrl}/content_types`;
      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZaloZnsAllowedContent;
        }>(url, { headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy loại nội dung ZNS được phép: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy loại nội dung ZNS được phép: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting ZNS allowed content types: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy loại nội dung ZNS được phép: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy loại nội dung ZNS được phép: ${error.message}`,
      );
    }
  }

  /**
   * Lấy dữ liệu mẫu của template ZNS
   * Endpoint: GET https://business.openapi.zalo.me/template/sample_data
   * @param accessToken Access token của Official Account
   * @param templateId ID của template
   * @returns Dữ liệu mẫu của template
   * @throws AppException nếu có lỗi xảy ra
   */
  async getZnsTemplateSampleData(
    accessToken: string,
    templateId: string,
  ): Promise<ZaloZnsTemplateSampleData> {
    try {
      const url = `${this.templateApiUrl}/sample_data`;
      const params = { template_id: templateId };
      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZaloZnsTemplateSampleData;
        }>(url, { params, headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy dữ liệu mẫu template ZNS: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy dữ liệu mẫu template ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting ZNS template sample data: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy dữ liệu mẫu template ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy dữ liệu mẫu template ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin đánh giá của khách hàng
   * Endpoint: GET https://business.openapi.zalo.me/message/rating
   * @param accessToken Access token của Official Account
   * @param startTime Thời gian bắt đầu (Unix timestamp, tùy chọn)
   * @param endTime Thời gian kết thúc (Unix timestamp, tùy chọn)
   * @returns Thông tin đánh giá của khách hàng
   * @throws AppException nếu có lỗi xảy ra
   */
  async getZnsCustomerRating(
    accessToken: string,
    startTime?: number,
    endTime?: number,
  ): Promise<ZaloZnsCustomerRating> {
    try {
      const url = `${this.znsApiUrl}/rating`;
      const params: any = {};
      if (startTime !== undefined) params.start_time = startTime;
      if (endTime !== undefined) params.end_time = endTime;

      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZaloZnsCustomerRating;
        }>(url, { params, headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy đánh giá khách hàng ZNS: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy đánh giá khách hàng ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting ZNS customer rating: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy đánh giá khách hàng ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy đánh giá khách hàng ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách chi tiết đánh giá của khách hàng
   * Endpoint: GET https://business.openapi.zalo.me/message/rating/details
   * @param accessToken Access token của Official Account
   * @param options Tùy chọn lọc và phân trang
   * @returns Danh sách chi tiết đánh giá
   * @throws AppException nếu có lỗi xảy ra
   */
  async getZnsCustomerRatingDetails(
    accessToken: string,
    options?: {
      startTime?: number;
      endTime?: number;
      offset?: number;
      limit?: number;
      rating?: number;
      templateId?: string;
    },
  ): Promise<ZaloZnsCustomerRatingList> {
    try {
      const url = `${this.znsApiUrl}/rating/details`;
      const params: any = {};
      if (options?.startTime !== undefined)
        params.start_time = options.startTime;
      if (options?.endTime !== undefined) params.end_time = options.endTime;
      if (options?.offset !== undefined) params.offset = options.offset;
      if (options?.limit !== undefined) params.limit = options.limit;
      if (options?.rating !== undefined) params.rating = options.rating;
      if (options?.templateId !== undefined)
        params.template_id = options.templateId;

      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZaloZnsCustomerRatingList;
        }>(url, { params, headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy chi tiết đánh giá khách hàng ZNS: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy chi tiết đánh giá khách hàng ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting ZNS customer rating details: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy chi tiết đánh giá khách hàng ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy chi tiết đánh giá khách hàng ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin chất lượng gửi ZNS hiện tại của Official Account
   * Endpoint: GET https://business.openapi.zalo.me/message/quality
   * @param accessToken Access token của Official Account
   * @returns Thông tin chất lượng gửi ZNS
   * @throws AppException nếu có lỗi xảy ra
   */
  async getZnsQuality(accessToken: string): Promise<ZaloZnsQualityInfo> {
    try {
      const url = `${this.znsApiUrl}/quality`;
      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZaloZnsQualityInfo;
        }>(url, { headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy thông tin chất lượng ZNS: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy thông tin chất lượng ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting ZNS quality info: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy thông tin chất lượng ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy thông tin chất lượng ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Lấy lịch sử chất lượng gửi ZNS theo thời gian
   * Endpoint: GET https://business.openapi.zalo.me/message/quality/history
   * @param accessToken Access token của Official Account
   * @param options Tùy chọn thời gian và khoảng đánh giá
   * @returns Lịch sử chất lượng gửi ZNS
   * @throws AppException nếu có lỗi xảy ra
   */
  async getZnsQualityHistory(
    accessToken: string,
    options?: {
      startTime?: number;
      endTime?: number;
      interval?: 'daily' | 'weekly' | 'monthly';
    },
  ): Promise<ZaloZnsQualityHistoryList> {
    try {
      const url = `${this.znsApiUrl}/quality/history`;
      const params: any = {};
      if (options?.startTime !== undefined)
        params.start_time = options.startTime;
      if (options?.endTime !== undefined) params.end_time = options.endTime;
      if (options?.interval !== undefined) params.interval = options.interval;

      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZaloZnsQualityHistoryList;
        }>(url, { params, headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy lịch sử chất lượng ZNS: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy lịch sử chất lượng ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting ZNS quality history: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy lịch sử chất lượng ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy lịch sử chất lượng ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Tạo ID giao dịch ngẫu nhiên
   * @returns ID giao dịch
   */
  private generateTrackingId(): string {
    return `zns_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
  }
}
