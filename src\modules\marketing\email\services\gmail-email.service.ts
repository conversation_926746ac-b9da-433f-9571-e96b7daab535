import { Injectable, Logger } from '@nestjs/common';
import { google } from 'googleapis';

/**
 * Interface cho Gmail message
 */
export interface GmailMessage {
  to: string;
  subject: string;
  body: string;
  from?: string;
  cc?: string[];
  bcc?: string[];
  isHtml?: boolean;
}

/**
 * Interface cho Gmail credentials
 */
export interface GmailCredentials {
  accessToken: string;
  refreshToken?: string;
}

/**
 * Interface cho kết quả gửi Gmail
 */
export interface GmailSendResult {
  messageId: string;
  threadId?: string;
  success: boolean;
  metadata?: {
    sentAt: Date;
    size: number;
    labelIds?: string[];
  };
}

/**
 * Service xử lý gửi email qua Gmail API trong worker
 */
@Injectable()
export class GmailEmailService {
  private readonly logger = new Logger(GmailEmailService.name);

  /**
   * Gửi email qua Gmail API
   * @param credentials Gmail credentials (access token, refresh token)
   * @param message Thông tin email cần gửi
   * @returns <PERSON>ết quả gửi email
   */
  async sendEmail(
    credentials: GmailCredentials,
    message: GmailMessage,
  ): Promise<GmailSendResult> {
    try {
      this.logger.debug(`Sending email via Gmail API to ${message.to}`);

      // Tạo OAuth2 client
      const oauth2Client = new google.auth.OAuth2();
      oauth2Client.setCredentials({
        access_token: credentials.accessToken,
        refresh_token: credentials.refreshToken,
      });

      // Tạo Gmail instance
      const gmail = google.gmail({ version: 'v1', auth: oauth2Client });

      // Build email content
      const emailContent = this.buildEmailContent(message);

      // Encode email content thành base64url
      const encodedEmail = Buffer.from(emailContent, 'utf-8')
        .toString('base64')
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=+$/, '');

      // Gửi email
      const response = await gmail.users.messages.send({
        userId: 'me',
        requestBody: {
          raw: encodedEmail,
        },
      });

      const result: GmailSendResult = {
        messageId: response.data.id || '',
        threadId: response.data.threadId || '',
        success: true,
        metadata: {
          sentAt: new Date(),
          size: emailContent.length,
          labelIds: response.data.labelIds || [],
        },
      };

      this.logger.log(
        `Email sent successfully via Gmail API to ${message.to}, messageId: ${result.messageId}`,
      );
      return result;
    } catch (error) {
      this.logger.error(
        `Failed to send email via Gmail API to ${message.to}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Build email content theo RFC 2822 format
   * @param message Thông tin email
   * @returns Email content string
   */
  private buildEmailContent(message: GmailMessage): string {
    const lines: string[] = [];

    // Headers
    lines.push(`To: ${message.to}`);
    if (message.from) {
      lines.push(`From: ${message.from}`);
    }
    if (message.cc && message.cc.length > 0) {
      lines.push(`Cc: ${message.cc.join(', ')}`);
    }
    if (message.bcc && message.bcc.length > 0) {
      lines.push(`Bcc: ${message.bcc.join(', ')}`);
    }

    // Subject với UTF-8 encoding
    const encodedSubject = this.encodeUtf8Header(message.subject);
    lines.push(`Subject: ${encodedSubject}`);

    // Content type
    if (message.isHtml !== false) {
      lines.push('Content-Type: text/html; charset=UTF-8');
    } else {
      lines.push('Content-Type: text/plain; charset=UTF-8');
    }

    lines.push('Content-Transfer-Encoding: 8bit');
    lines.push('MIME-Version: 1.0');

    // Empty line để phân tách headers và body
    lines.push('');

    // Body
    lines.push(message.body);

    return lines.join('\r\n');
  }

  /**
   * Encode UTF-8 header theo RFC 2047
   * @param text Text cần encode
   * @returns Encoded text
   */
  private encodeUtf8Header(text: string): string {
    // Kiểm tra xem có ký tự non-ASCII không
    if (!/[^\x00-\x7F]/.test(text)) {
      return text; // Chỉ có ASCII, không cần encode
    }

    // Encode theo RFC 2047: =?UTF-8?B?base64_encoded_text?=
    const encoded = Buffer.from(text, 'utf-8').toString('base64');
    return `=?UTF-8?B?${encoded}?=`;
  }

  /**
   * Validate email address format
   * @param email Email address
   * @throws Error nếu email không hợp lệ
   */
  private validateEmailAddress(email: string): void {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new Error(`Invalid email address: ${email}`);
    }
  }

  /**
   * Sanitize HTML content để tránh XSS
   * @param content HTML content
   * @returns Sanitized content
   */
  private sanitizeContent(content: string): string {
    // Basic sanitization - có thể mở rộng với thư viện như DOMPurify
    return content
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '');
  }
}
