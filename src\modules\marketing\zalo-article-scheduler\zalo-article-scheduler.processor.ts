import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger, Injectable } from '@nestjs/common';
import { Job } from 'bullmq';
import { QueueName, ZaloArticleSchedulerJobName } from '../../../queue/queue-name.enum';
import { ZaloArticleSchedulerJobData } from '../../../queue/queue.types';
import { ZaloArticleSchedulerService } from './zalo-article-scheduler.service';

/**
 * Processor xử lý queue lên lịch xuất bản bài viết Zalo
 */
@Injectable()
@Processor(QueueName.ZALO_ARTICLE_SCHEDULER, {
  concurrency: 3, // Xử lý tối đa 3 job đồng thời
  stalledInterval: 30 * 1000, // 30 giây
  maxStalledCount: 1,
})
export class ZaloArticleSchedulerProcessor extends WorkerHost {
  private readonly logger = new Logger(ZaloArticleSchedulerProcessor.name);

  constructor(
    private readonly zaloArticleSchedulerService: ZaloArticleSchedulerService,
  ) {
    super();
  }

  /**
   * Xử lý job xuất bản bài viết đã lên lịch
   * @param job Job chứa dữ liệu bài viết cần xuất bản
   */
  async process(job: Job<ZaloArticleSchedulerJobData, any, string>): Promise<void> {
    this.logger.log(
      `Bắt đầu xử lý job xuất bản bài viết: ${job.id} - Article: ${job.data.articleId} - Job name: ${job.name}`,
    );

    try {
      switch (job.name) {
        case ZaloArticleSchedulerJobName.PUBLISH_SCHEDULED_ARTICLE:
          await this.zaloArticleSchedulerService.publishScheduledArticle(job.data);
          break;

        default:
          this.logger.warn(`Unknown job name: ${job.name}`);
          throw new Error(`Unknown job name: ${job.name}`);
      }

      this.logger.log(
        `Hoàn thành xử lý job xuất bản bài viết: ${job.id} - Article: ${job.data.articleId}`,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi xử lý job xuất bản bài viết: ${job.id} - Article: ${job.data.articleId} - Error: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
