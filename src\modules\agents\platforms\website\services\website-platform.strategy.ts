import { Injectable, Logger } from '@nestjs/common';
import { Platform, ModelFeature, InputModality } from '../../../enums';
import { AgentConfig } from '../../../interfaces/agent-config.interface';
import { McpConfiguration } from '../../../interfaces/mcp-config.interface';
import { McpClientService } from '../../../services';
import { WebSearchTool } from '../../../runtime/tools/web-search.tool';
import { AgentMemoryTool } from '../../../runtime/tools/agent-memory.tool';
import { ImageLoaderTool } from '../../../runtime/tools/image-loader.tool';
import { AuthContext, PlatformStrategy } from 'src/modules/agents/interfaces';
import { SelectQueryBuilder } from 'typeorm';
import { Integration } from '../../../entities';
import { WebsiteUserConvertCustomerMemoryTool } from 'src/modules/agents/runtime/tools/website-user-convert-customer-memory.tool';
import { AgentKnowledgeRAGTool } from 'src/modules/agents/runtime/tools/agent-knowledge-rag.tool';

@Injectable()
export class WebsitePlatformStrategy implements PlatformStrategy {
  private readonly logger = new Logger(WebsitePlatformStrategy.name);
  readonly platform = Platform.WEBSITE;

  constructor(
    private readonly mcpClientService: McpClientService,
    private readonly webSearchService: WebSearchTool,
    private readonly agentMemoryService: AgentMemoryTool,
    private readonly imageLoaderTool: ImageLoaderTool,
    private readonly websiteUserConvertCustomerMemoryTool: WebsiteUserConvertCustomerMemoryTool,
    private readonly agentKnowledgeRAGTool: AgentKnowledgeRAGTool,

  ) {}

  async buildMcpClient(
    mcpConfig: Record<string, McpConfiguration> | null,
    authContext: AuthContext,
  ): Promise<any> {
    // Website visitors don't have JWT, but we can still build MCP clients
    // using the website owner's configuration
    return mcpConfig
      ? await this.mcpClientService.buildMcpClients(
          mcpConfig,
          undefined,
          this.platform,
          authContext,
        )
      : null;
  }

  getToolsForAgent(agentConfig: AgentConfig): any[] {
    const result = agentConfig.model.features.includes(ModelFeature.TOOL_CALL)
      ? [
          this.webSearchService,
          this.agentMemoryService,
          // Note: Website platform doesn't include user-specific RAG tools
          // since visitors don't have personal knowledge/media/products
          agentConfig.model.inputModalities.includes(InputModality.IMAGE)
            ? this.imageLoaderTool
            : undefined,
          this.websiteUserConvertCustomerMemoryTool,
          this.agentKnowledgeRAGTool,
        ].filter(Boolean)
      : [];
    this.logger.debug(
      `getToolsForAgent: ${agentConfig.id} - ${result.length} tools available (website platform)`,
    );
    return result;
  }

  customizeIntegrationQuery(
    baseQuery: SelectQueryBuilder<Integration>,
    authContext: AuthContext,
  ): SelectQueryBuilder<Integration> {
    const { websiteOwnerId } = authContext;

    // Integration selection logic for website platform
    // Use the business owner's API keys (not the visitor's - they don't have any)
    if (websiteOwnerId) {
      // For website agents: check use_system_key field using business owner's ID
      return baseQuery.andWhere(
        `
        (
          (agent.use_system_key = true AND integration.employee_id IS NOT NULL) OR
          (agent.use_system_key = false AND integration.user_id = :websiteOwnerId)
        )
      `,
        { userId: websiteOwnerId }, // Note: parameter name stays :userId for SQL compatibility
      );
    } else {
      // Fallback to system integrations if no website owner identified
      return baseQuery.andWhere('integration.employee_id IS NOT NULL');
    }
  }
}
