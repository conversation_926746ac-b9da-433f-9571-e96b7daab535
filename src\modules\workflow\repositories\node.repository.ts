import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindOptionsWhere, Repository } from 'typeorm';
import { Node } from '../entities/node.entity';

/**
 * Repository cho Node entity
 */
@Injectable()
export class NodeRepository {
  constructor(
    @InjectRepository(Node)
    private readonly repository: Repository<Node>,
  ) { }

  async create(nodeData: Partial<Node>): Promise<Node> {
    const node = this.repository.create(nodeData);
    return await this.repository.save(node);
  }

  async findById(id: string, workflowId: string): Promise<Node | null> {
    return await this.repository.findOne({ where: { id, workflowId } });
  }

  async findByWorkflowId(workflowId: string): Promise<Node[]> {
    return await this.repository.find({
      where: { workflowId },
      order: { name: 'ASC' },
    });
  }

  async update(workflowId: string, id: string, updateData: Partial<Node>): Promise<Node | null> {
    const updateResult = await this.repository.update(id, updateData);
    if ((updateResult.affected || 0) === 0) {
      return null;
    }
    return await this.findById(id, workflowId);
  }

  async delete(id: string): Promise<boolean> {
    const deleteResult = await this.repository.delete(id);
    return (deleteResult.affected || 0) > 0;
  }

  async deleteByWorkflowId(workflowId: string): Promise<number> {
    const deleteResult = await this.repository.delete({ workflowId });
    return deleteResult.affected || 0;
  }

  async count(where?: FindOptionsWhere<Node>): Promise<number> {
    return await this.repository.count({ where });
  }
}