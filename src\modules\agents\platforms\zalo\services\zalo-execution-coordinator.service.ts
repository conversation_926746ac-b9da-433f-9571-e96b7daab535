import { Injectable, Logger } from '@nestjs/common';
import { AgentConfig, LangChainInput } from '../../../shared/interfaces';
import { TokenUsageCollector } from '../../../shared/utils';
import { ZaloJobData } from '../interfaces/zalo-info.interface';
import { ZaloPlannerExecutorConfig } from '../graph-configs/zalo-planner-executor-config.interface';

@Injectable()
export class ZaloExecutionCoordinatorService {
  private readonly logger = new Logger(ZaloExecutionCoordinatorService.name);

  /**
   * Build GraphConfigurable object for PlannerExecutorGraph execution
   * Combines planner config, executor config, and execution parameters
   */
  buildPlannerExecutorGraphConfigurable(param: {
    jobData: ZaloJobData;
    plannerAgent: AgentConfig | undefined;
    executorAgent: AgentConfig;
    platformThreadId: string;
  }): ZaloPlannerExecutorConfig {
    const { jobData, plannerAgent, executorAgent, platformThreadId } = param;
    
    this.logger.debug('Building PlannerExecutor GraphConfigurable object', {
      zaloUserId: jobData.humanInfo.zaloUser.id,
      zaloOwnerId: jobData.humanInfo.zaloOwner.userId,
      oaId: jobData.humanInfo.zaloInfo.oaId,
      hasPlannerAgent: !!plannerAgent,
      executorAgentId: executorAgent.id,
    });

    const graphConfigurable: ZaloPlannerExecutorConfig = {
      plannerAgent,
      executorAgent,
      thread_id: platformThreadId,
      platform: jobData.platform,
      currentUser: {
        visitor: jobData.humanInfo.zaloUser,
        owner: jobData.humanInfo.zaloOwner,
        info: jobData.humanInfo.zaloInfo,
      },
      alwaysApproveToolCall: true,
    };

    this.logger.debug('PlannerExecutor GraphConfigurable built successfully', {
      threadId: graphConfigurable.thread_id,
      platform: graphConfigurable.platform,
      hasPlannerAgent: !!graphConfigurable.plannerAgent,
      executorAgentId: graphConfigurable.executorAgent.id,
    });

    return graphConfigurable;
  }

  /**
   * Initialize token usage collector for billing
   * Creates collector with both planner and executor agent mappings
   */
  initializeTokenCollector(
    jobData: ZaloJobData,
    plannerAgent: AgentConfig | undefined,
    executorAgent: AgentConfig,
  ): TokenUsageCollector {
    this.logger.debug('Initializing token usage collector', {
      zaloUserId: jobData.humanInfo.zaloUser.id,
      zaloOwnerId: jobData.humanInfo.zaloOwner.userId,
      oaId: jobData.humanInfo.zaloInfo.oaId,
      hasPlannerAgent: !!plannerAgent,
      executorAgentId: executorAgent.id,
    });

    // Build agent map for token tracking
    const agentMap: Record<string, AgentConfig> = {
      [executorAgent.id]: executorAgent,
    };

    // Add planner agent if present
    if (plannerAgent) {
      agentMap[plannerAgent.id] = plannerAgent;
    }

    // Zalo platform: Zalo OA owner pays for tokens (zaloOwnerId)
    const tokenCollector = new TokenUsageCollector(agentMap, {
      userId: jobData.humanInfo.zaloOwner.userId,
    });

    this.logger.debug('Token usage collector initialized', {
      agentCount: Object.keys(agentMap).length,
      zaloOwnerId: jobData.humanInfo.zaloOwner.userId,
      agentIds: Object.keys(agentMap),
    });

    return tokenCollector;
  }

  /**
   * Build execution parameters for PlannerExecutorGraph
   * Creates tags and context information for execution tracking
   */
  buildExecutionParameters(
    jobData: ZaloJobData,
    plannerAgent: AgentConfig | undefined,
    executorAgent: AgentConfig,
  ): { tags: string[]; userContext: string; executorAgentTag: string; plannerAgentTag?: string } {
    this.logger.debug('Building execution parameters', {
      runId: jobData.runId,
      platform: jobData.platform,
      zaloUserId: jobData.humanInfo.zaloUser.id,
      zaloOwnerId: jobData.humanInfo.zaloOwner.userId,
      oaId: jobData.humanInfo.zaloInfo.oaId,
    });

    // Context shows Zalo OA owner (who pays) and Zalo user (who interacts)
    const userContext = `zalo_owner:${jobData.humanInfo.zaloOwner.userId}`;

    // Required agent tags for token usage collection
    const executorAgentTag = `agent:${executorAgent.id}:${executorAgent.type}`;
    const plannerAgentTag = plannerAgent 
      ? `agent:${plannerAgent.id}:${plannerAgent.type}` 
      : undefined;

    const tags = [
      userContext,
      `zalo_user:${jobData.humanInfo.zaloUser.id}`,
      `oa:${jobData.humanInfo.zaloInfo.oaId}`,
      `run:${jobData.runId}`,
      `platform:${jobData.platform}`,
      executorAgentTag,
    ];

    // Add planner tag if present
    if (plannerAgentTag) {
      tags.push(plannerAgentTag);
    }

    this.logger.debug('Execution parameters built', {
      userContext,
      executorAgentTag,
      plannerAgentTag,
      tagCount: tags.length,
    });

    return { tags, userContext, executorAgentTag, plannerAgentTag };
  }

  /**
   * Setup LangGraph execution with streaming and token collection
   * Coordinates all execution components and prepares for streaming
   */
  async setupLangGraphExecution(
    jobData: ZaloJobData,
    langGraphInput: LangChainInput,
    graphConfigurable: ZaloPlannerExecutorConfig,
  ): Promise<{
    tokenUsageCollector: TokenUsageCollector;
    input: any;
    tags: string[];
    abortController: AbortController;
  }> {
    this.logger.debug('Setting up LangGraph execution', {
      runId: jobData.runId,
      threadId: jobData.threadId,
      hasMessages: !!langGraphInput.messages,
      messageCount: langGraphInput.messages?.length || 0,
    });

    // Initialize token usage collector for billing
    const tokenUsageCollector = this.initializeTokenCollector(
      jobData,
      graphConfigurable.plannerAgent,
      graphConfigurable.executorAgent,
    );

    // Build LangGraph input from messages
    const input = { messages: langGraphInput.messages };

    // Build execution parameters for PlannerExecutorGraph
    const { tags } = this.buildExecutionParameters(
      jobData,
      graphConfigurable.plannerAgent,
      graphConfigurable.executorAgent,
    );

    const abortController = new AbortController();

    this.logger.debug('LangGraph execution setup complete', {
      runId: jobData.runId,
      threadId: jobData.threadId,
      messageCount: langGraphInput.messages?.length || 0,
      tagCount: tags.length,
      hasPlannerAgent: !!graphConfigurable.plannerAgent,
    });

    return {
      tokenUsageCollector,
      input,
      tags,
      abortController,
    };
  }
}