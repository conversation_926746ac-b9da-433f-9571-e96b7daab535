import { Injectable, Logger } from '@nestjs/common';
import {
  InputModality,
  ModelFeature,
  ModelProviderEnum,
  ModelTypeEnum,
  OutputModality,
  SamplingParameter,
} from '../enums';
import { ModelConfig } from '../interfaces';
import { ModelPricingInterface } from '../interfaces';

/**
 * Raw model registry data from database queries
 * Matches the actual Query 2 implementation fields
 */
export interface ModelRegistryRawData {
  agentId: string;
  modelName: string;
  isFineTune: boolean;
  provider: ModelProviderEnum;
  maxTokens: number;
  contexWindow: number;

  inputModalitiesBase: InputModality[];
  outputModalitiesBase: OutputModality[];
  samplingParametersBase: SamplingParameter[];
  featuresBase: ModelFeature[];
  basePricing: ModelPricingInterface;

  inputModalitiesFineTune: InputModality[];
  outputModalitiesFineTune: OutputModality[];
  samplingParametersFineTune: SamplingParameter[];
  featuresFineTune: ModelFeature[];
  fineTunePricing: ModelPricingInterface;
}

/**
 * Selected capabilities based on isFineTune flag
 */
interface SelectedCapabilities {
  inputModalities: InputModality[];
  outputModalities: OutputModality[];
  samplingParameters: SamplingParameter[];
  features: ModelFeature[];
  pricing: ModelPricingInterface;
}

/**
 * Model configuration output for AgentConfigInterface
 */
export interface ModelConfigOutput {
  name: string;
  provider: ModelProviderEnum;
  inputModalities: InputModality[];
  outputModalities: OutputModality[];
  samplingParameters: SamplingParameter[];
  features: ModelFeature[];
  maxTokens: number;
  parameters: {
    temperature?: number;
    topP?: number;
    topK?: number;
    maxTokens?: number;
    maxOutputTokens?: number;
  };
  pricing: {
    inputRate: number;
    outputRate: number;
  };
  encryptedApiKeyPairs: {
    publicKey: string;
    encryptedContent: string;
  }[];
}

/**
 * Model Registry Mapper Service
 *
 * Transforms raw database model registry data into structured model configurations
 * for the AgentConfigInterface. Handles base vs fine-tune selection and parameter merging.
 */
@Injectable()
export class ModelRegistryMapperService {
  private readonly logger = new Logger(ModelRegistryMapperService.name);

  /**
   * Map raw model registry data to structured model configurations
   *
   * @param rawData Array of raw model registry data from database
   * @param agentModelConfigs Map of agent IDs to their model configurations (Agent.modelConfig)
   * @returns Map of agent IDs to structured model configurations
   */
  mapModelRegistryData(
    rawData: ModelRegistryRawData[],
    agentModelConfigs: Map<
      string,
      { modelConfig: ModelConfig; useSystemKey: boolean }
    >,
  ): Record<string, ModelConfigOutput> {
    this.logger.debug(
      `Mapping model registry data for ${rawData.length} agents`,
    );

    const modelConfigMap: Record<string, ModelConfigOutput> | null = {};

    for (const data of rawData) {
      try {
        const agentModelConfig = agentModelConfigs.get(data.agentId);
        const modelConfig = this.buildModelConfig(data, agentModelConfig);
        modelConfigMap[data.agentId] = modelConfig;

        this.logger.debug(
          `Mapped model config for agent ${data.agentId}: ${data.modelName}`,
        );
      } catch (error) {
        this.logger.error(
          `Failed to map model config for agent ${data.agentId}: ${error.message}`,
          {
            agentId: data.agentId,
            modelName: data.modelName,
            error: error.message,
          },
        );
        // Continue processing other agents
      }
    }

    return modelConfigMap;
  }

  /**
   * Build complete model configuration for a single agent
   *
   * @param data Raw model registry data
   * @param agentModelConfig Agent's model configuration (Agent.modelConfig)
   * @returns Structured model configuration
   */
  private buildModelConfig(
    data: ModelRegistryRawData,
    agentModelConfig?: { modelConfig: ModelConfig; useSystemKey: boolean },
  ): ModelConfigOutput {
    // Step 1: Select base vs fine-tune capabilities
    const capabilities = this.selectCapabilities(
      data,
      agentModelConfig?.useSystemKey as boolean,
    );

    // Step 2: Merge user parameters with registry defaults
    const parameters = this.mergeParameters(
      data,
      agentModelConfig?.modelConfig,
    );

    // Step 3: Assemble final configuration
    return {
      name: data.modelName,
      provider: this.mapProvider(data.provider),
      inputModalities: this.mapInputModalities(capabilities.inputModalities),
      outputModalities: this.mapOutputModalities(capabilities.outputModalities),
      samplingParameters: this.mapSamplingParameters(
        capabilities.samplingParameters,
      ),
      features: this.mapFeatures(capabilities.features),
      maxTokens: data.maxTokens,
      parameters,
      pricing: capabilities.pricing,
      encryptedApiKeyPairs: [], // Will be populated by Integration Config Builder
    };
  }

  /**
   * Select appropriate capabilities based on isFineTune flag
   *
   * Core business logic:
   * - If Models.isFineTune = true → use ModelRegistry.*_fine_tune fields
   * - If Models.isFineTune = false → use ModelRegistry.*_base fields
   */
  private selectCapabilities(
    data: ModelRegistryRawData,
    useSystemKey: boolean,
  ): SelectedCapabilities {
    if (data.isFineTune) {
      this.logger.debug(
        `Using fine-tune capabilities for agent ${data.agentId}`,
      );
      return {
        inputModalities: data.inputModalitiesFineTune,
        outputModalities: data.outputModalitiesFineTune,
        samplingParameters: data.samplingParametersFineTune,
        features: data.featuresFineTune,
        pricing: useSystemKey
          ? data.fineTunePricing
          : { inputRate: 0, outputRate: 0 },
      };
    } else {
      this.logger.debug(`Using base capabilities for agent ${data.agentId}`);
      return {
        inputModalities: data.inputModalitiesBase,
        outputModalities: data.outputModalitiesBase,
        samplingParameters: data.samplingParametersBase,
        features: data.featuresBase,
        pricing: useSystemKey
          ? data.basePricing
          : { inputRate: 0, outputRate: 0 },
      };
    }
  }

  /**
   * Merge user model parameters with registry defaults and validate constraints
   *
   * @param data Raw model registry data
   * @param agentModelConfig Agent's model configuration with user preferences
   * @returns Merged and validated parameters
   */
  private mergeParameters(
    data: ModelRegistryRawData,
    agentModelConfig?: ModelConfig,
  ) {
    // Note: ModelConfig uses snake_case (top_p, max_tokens) while AgentConfigInterface uses camelCase (topP, maxTokens)
    const userParams = {
      temperature: agentModelConfig?.temperature,
      topP: agentModelConfig?.top_p,
      topK: agentModelConfig?.top_k,
      maxTokens: agentModelConfig?.max_tokens,
      maxOutputTokens: agentModelConfig?.max_tokens,
    };

    return userParams;
  }

  /**
   * Validate parameter within specified range
   */
  private validateParameter(
    value: number | undefined,
    min: number,
    max: number,
    defaultValue: number,
  ): number {
    if (value === undefined || value === null) {
      return defaultValue;
    }

    if (value < min || value > max) {
      this.logger.warn(
        `Parameter value ${value} out of range [${min}, ${max}], using default ${defaultValue}`,
      );
      return defaultValue;
    }

    return value;
  }

  /**
   * Determine model type based on data characteristics
   */
  private determineModelType(data: ModelRegistryRawData): ModelTypeEnum {
    if (data.isFineTune) {
      return ModelTypeEnum.FINE_TUNE;
    }

    // For now, default to SYSTEM type
    // This logic can be enhanced based on additional criteria
    return ModelTypeEnum.SYSTEM;
  }

  // ============================================================================
  // ENUM MAPPING METHODS
  // ============================================================================

  /**
   * Map database provider string to ModelProviderEnum
   */
  private mapProvider(provider: string): ModelProviderEnum {
    const providerMap: Record<string, ModelProviderEnum> = {
      OPENAI: ModelProviderEnum.OPENAI,
      ANTHROPIC: ModelProviderEnum.ANTHROPIC,
      GOOGLE: ModelProviderEnum.GOOGLE,
      XAI: ModelProviderEnum.XAI,
      DEEPSEEK: ModelProviderEnum.DEEPSEEK,
    };

    const mappedProvider = providerMap[provider.toUpperCase()];
    if (!mappedProvider) {
      this.logger.warn(`Unknown provider: ${provider}, defaulting to OPENAI`);
      return ModelProviderEnum.OPENAI;
    }

    return mappedProvider;
  }

  /**
   * Map database input modality strings to InputModality enum array
   */
  private mapInputModalities(modalities: string[]): InputModality[] {
    const modalityMap: Record<string, InputModality> = {
      text: InputModality.TEXT,
      image: InputModality.IMAGE,
      audio: InputModality.AUDIO,
      video: InputModality.VIDEO,
    };

    return modalities
      .map((modality) => modalityMap[modality.toLowerCase()])
      .filter(Boolean); // Remove undefined values
  }

  /**
   * Map database output modality strings to OutputModality enum array
   */
  private mapOutputModalities(modalities: string[]): OutputModality[] {
    const modalityMap: Record<string, OutputModality> = {
      text: OutputModality.TEXT,
      image: OutputModality.IMAGE,
      audio: OutputModality.AUDIO,
      video: OutputModality.VIDEO,
    };

    return modalities
      .map((modality) => modalityMap[modality.toLowerCase()])
      .filter(Boolean); // Remove undefined values
  }

  /**
   * Map database sampling parameter strings to SamplingParameter enum array
   */
  private mapSamplingParameters(parameters: string[]): SamplingParameter[] {
    const parameterMap: Record<string, SamplingParameter> = {
      temperature: SamplingParameter.TEMPERATURE,
      top_p: SamplingParameter.TOP_P,
      top_k: SamplingParameter.TOP_K,
      max_tokens: SamplingParameter.MAX_TOKENS,
      max_output_tokens: SamplingParameter.MAX_OUTPUT_TOKENS,
    };

    return parameters
      .map((param) => parameterMap[param.toLowerCase()])
      .filter(Boolean); // Remove undefined values
  }

  /**
   * Map database feature strings to ModelFeature enum array
   */
  private mapFeatures(features: string[]): ModelFeature[] {
    const featureMap: Record<string, ModelFeature> = {
      tool_call: ModelFeature.TOOL_CALL,
      parallel_tool_call: ModelFeature.PARALLEL_TOOL_CALL,
      forced_tool_call: ModelFeature.FORCED_TOOL_CALL,
    };

    return features
      .map((feature) => featureMap[feature.toLowerCase()])
      .filter(Boolean); // Remove undefined values
  }
}
