import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';

@Entity({ name: 'user_converts' })
export class UserConverts {
  @PrimaryGeneratedColumn('increment', { type: 'bigint', comment: 'ID bản ghi chuyển đổi' })
  id: string;

  @Column('uuid', {
    name: 'convert_customer_id',
    nullable: true,
    comment: '<PERSON>h<PERSON>ch hàng được chuyển đổi',
  })
  convertCustomerId: string | null;

  @Column('varchar', {
    name: 'user_id',
    length: 50,
    nullable: true,
    comment: 'ID của người dùng (user) thực hiện chuyển đổi',
  })
  userId: string;

  @Column('varchar', {
    name: 'conversion_type',
    length: 50,
    nullable: true,
    comment: '<PERSON><PERSON><PERSON> chuyển đổi (ví dụ: online, offline, referral)',
  })
  conversionType: string | null;

  @Column('varchar', {
    name: 'source',
    length: 100,
    nullable: true,
    comment: '<PERSON>uồ<PERSON> gốc chuyển đổi (ví dụ: website, social media, event)',
  })
  source: string | null;

  @Column('text', {
    name: 'notes',
    nullable: true,
    comment: 'Ghi chú thêm về chuyển đổi',
  })
  notes: string | null;

  @Column('jsonb', {
    name: 'content',
    nullable: true,
    comment: 'Thông tin bổ sung (JSON)',
  })
  content: Record<string, any> | null;

  @Column('bigint', {
    name: 'created_at',
    default: () => "(EXTRACT(EPOCH FROM now()) * 1000)::bigint",
    comment: 'Thời gian tạo',
  })
  createdAt: string;

  @Column('bigint', {
    name: 'updated_at',
    default: () => "(EXTRACT(EPOCH FROM now()) * 1000)::bigint",
    comment: 'Thời gian cập nhật',
  })
  updatedAt: string;

  @Column('varchar', {
    name: 'convert_customer_name',
    length: 255,
    nullable: true,
  })
  convertCustomerName: string | null;

  @Column('varchar', {
    name: 'convert_customer_phone',
    length: 20,
    nullable: true,
  })
  convertCustomerPhone: string | null;

  @Column('varchar', {
    name: 'convert_customer_email',
    length: 255,
    nullable: true,
  })
  convertCustomerEmail: string | null;
}
