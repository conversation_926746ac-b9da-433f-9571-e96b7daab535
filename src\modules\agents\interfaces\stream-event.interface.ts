import { StreamEventType } from "../enums";

/**
 * Base stream event interface for Redis streaming architecture
 * Simplified structure - <PERSON><PERSON> already has runId/threadId context
 */
export interface BaseStreamEvent {
  /**
   * Event type identifier
   */
  type: StreamEventType;

  /**
   * Event timestamp in milliseconds
   */
  timestamp: number;


  /**
   * Whether the event is from a subprocess
   */
  isFromSubprocess: boolean;
}

// ============================================================================
// TEXT MESSAGE EVENTS (Streaming Support)
// ============================================================================

/**
 * Text message start event - indicates new message creation
 */
export interface TextMessageStart extends BaseStreamEvent {
  type: StreamEventType.TEXT_MESSAGE_START;

  /**
   * Message ID - correlates with InternalConversationMessage.id
   */
  messageId: string;
}

/**
 * Text message content event - streaming text chunks
 */
export interface TextMessageContent extends BaseStreamEvent {
  type: StreamEventType.TEXT_MESSAGE_CONTENT;

  /**
   * Message ID - correlates with InternalConversationMessage.id
   */
  messageId: string;

  /**
   * Streaming text chunk from LangGraph
   */
  delta: string;
}

/**
 * Text message end event - final complete message
 */
export interface TextMessageEnd extends BaseStreamEvent {
  type: StreamEventType.TEXT_MESSAGE_END;

  /**
   * Message ID - correlates with InternalConversationMessage.id
   */
  messageId: string;

  /**
   * Complete message text from on_chat_model_end
   */
  finalContent: string;
}

// ============================================================================
// MEDIA MESSAGE EVENTS (Single Events)
// ============================================================================

/**
 * Image message event - single event with image URL and metadata
 */
export interface ImageMessage extends BaseStreamEvent {
  type: StreamEventType.IMAGE_MESSAGE;

  /**
   * Message ID - correlates with InternalConversationMessage.id
   */
  messageId: string;

  /**
   * URL to the generated/uploaded image
   */
  imageUrl: string;

  /**
   * Attachment ID - correlates with InternalConversationMessagesAttachment.attachmentId
   */
  attachmentId: string;

  /**
   * Optional file name
   */
  fileName?: string;
}

/**
 * File message event - single event with file URL and metadata
 */
export interface FileMessage extends BaseStreamEvent {
  type: StreamEventType.FILE_MESSAGE;

  /**
   * Message ID - correlates with InternalConversationMessage.id
   */
  messageId: string;

  /**
   * URL to download the file
   */
  fileUrl: string;

  /**
   * Attachment ID - correlates with InternalConversationMessagesAttachment.attachmentId
   */
  attachmentId: string;

  /**
   * File name
   */
  fileName: string;
}

// ============================================================================
// TOOL EXECUTION EVENTS
// ============================================================================

/**
 * Tool call start event - tool call initiation
 */
export interface ToolCallStart extends BaseStreamEvent {
  type: StreamEventType.TOOL_CALL_START;

  /**
   * Tool name
   */
  toolName: string;

  /**
   * Tool execution ID
   */
  toolId: string;

  /**
   * Tool arguments
   */
  toolArgs: any;
}

/**
 * Tool call arguments event - streaming tool call arguments
 */
export interface ToolCallArgs extends BaseStreamEvent {
  type: StreamEventType.TOOL_CALL_ARGS;
}

/**
 * Tool call end event - tool execution result
 */
export interface ToolCallEnd extends BaseStreamEvent {
  type: StreamEventType.TOOL_CALL_END;

  /**
   * Tool name
   */
  toolName: string;

  /**
   * Tool execution ID
   */
  toolId: string;

  /**
   * Tool execution result
   */
  toolResult: any;
}

/**
 * Tool call interrupt event - tool execution interrupted by human review
 */
export interface ToolCallInterrupt extends BaseStreamEvent {
  type: StreamEventType.TOOL_CALL_INTERRUPT;
}

// ============================================================================
// RUN LIFECYCLE EVENTS
// ============================================================================

/**
 * Run started event - run execution has begun
 */
export interface RunStarted extends BaseStreamEvent {
  type: StreamEventType.RUN_STARTED;

  /**
   * Run ID for correlation
   */
  runId: string;
}

/**
 * Run cancelled event - run was cancelled by user or system
 */
export interface RunCancelled extends BaseStreamEvent {
  type: StreamEventType.RUN_CANCELLED;

  /**
   * Cancellation reason
   */
  reason?: string;

  /**
   * Whether cancellation was user-initiated or system-initiated
   */
  initiatedBy: 'user' | 'system';
}

/**
 * Run complete event - successful completion with cost information
 */
export interface RunComplete extends BaseStreamEvent {
  type: StreamEventType.RUN_COMPLETE;

  /**
   * Total cost for the run (exposed to users)
   * Internal token details are hidden for security
   */
  totalCost?: number;
}

/**
 * Run error event - structured error information
 */
export interface RunError extends BaseStreamEvent {
  type: StreamEventType.RUN_ERROR;

  /**
   * Structured error information
   */
  error: {
    /**
     * Error type
     */
    type: string;

    /**
     * Error message
     */
    message: string;

    /**
     * Optional error code
     */
    code?: string;

    /**
     * Optional stack trace (for debugging)
     */
    stack?: string;
  };
}

// ============================================================================
// UNION TYPES
// ============================================================================

/**
 * Union type for all stream events
 */
export type StreamEvent =
  | TextMessageStart
  | TextMessageContent
  | TextMessageEnd
  | ImageMessage
  | FileMessage
  | ToolCallStart
  | ToolCallArgs
  | ToolCallEnd
  | ToolCallInterrupt
  | RunStarted
  | RunCancelled
  | RunComplete
  | RunError;
