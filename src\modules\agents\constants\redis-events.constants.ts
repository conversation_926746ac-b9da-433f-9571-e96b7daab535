/**
 * Redis Event Patterns for Cancellation (Agents Module)
 *
 * We use BullMQ for job triggering, Redis pub/sub only for cancellation.
 */

import { Platform } from "../enums";

/**
 * Event patterns for Redis pub/sub communication between backend and worker
 */
export const REDIS_EVENTS = {
  /**
   * Cancel event to stop processing a run
   * Published by: Backend API (Chat Module)
   * Consumed by: Worker Service (Agents Module)
   */
  RUN_CANCEL: 'run.cancel',
} as const;

/**
 * Base interface for all Redis events
 */
export interface BaseRedisEvent {
  eventType: typeof REDIS_EVENTS.RUN_CANCEL;
  publishedAt: number;
}

/**
 * Cancellation reason types for distinguishing between user actions
 */
export enum CancellationReason {
  /**
   * User sent a new message while AI was processing
   * Behavior: Stop current processing and re-enqueue with new messages
   */
  MESSAGE_INTERRUPT = 'message_interrupt',

  /**
   * User explicitly clicked stop/cancel button
   * Behavior: Stop processing completely, do not re-enqueue
   */
  USER_ABORT = 'user_abort'
}

/**
 * Interface for run cancel event payload
 */
export interface RunCancelEvent extends BaseRedisEvent {
  eventType: typeof REDIS_EVENTS.RUN_CANCEL;
  threadId: string; // LangGraph thread ID for cancellation
  runId?: string; // Optional, for logging purposes only
  reason: CancellationReason; // Explicit cancellation type
  timestamp: number;
  platform: Platform;
}
