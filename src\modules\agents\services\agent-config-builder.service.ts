import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AgentConfig } from '../interfaces/agent-config.interface';
import { Agent, Integration, Mcp, Models } from '../entities';
import { ModelConfig } from 'src/modules/agents/interfaces';
import {
  AgentConfigAssemblerService,
  AgentRawData,
  IntegrationConfigBuilderService,
  IntegrationRawData,
  McpConfigBuilderService,
  McpConfigRawData,
  ModelRegistryMapperService,
  ModelRegistryRawData,
} from 'src/modules/agents/services';
import { AuthContext, PlatformStrategy } from '../interfaces';

/**
 * Generic Agent Config Builder Service
 *
 * Orchestrates the complete agent configuration building process using
 * the 4-query maximum strategy. Coordinates all builder services to
 * transform raw database data into complete AgentConfigInterface objects.
 *
 * This service is platform-agnostic and uses strategy pattern for
 * platform-specific customizations.
 */
@Injectable()
export class AgentConfigBuilderService {
  private readonly logger = new Logger(AgentConfigBuilderService.name);

  constructor(
    @InjectRepository(Agent)
    private readonly agentRepository: Repository<Agent>,
    @InjectRepository(Models)
    private readonly modelRepository: Repository<Models>,
    @InjectRepository(Integration)
    private readonly integrationRepository: Repository<Integration>,
    @InjectRepository(Mcp) private readonly mcpRepository: Repository<Mcp>,
    private readonly modelRegistryMapper: ModelRegistryMapperService,
    private readonly integrationConfigBuilder: IntegrationConfigBuilderService,
    private readonly mcpConfigBuilder: McpConfigBuilderService,
    private readonly agentConfigAssembler: AgentConfigAssemblerService,
  ) {}

  /**
   * Build agent configurations with 4-query maximum strategy
   *
   * @param agentIds Array of agent IDs to build configurations for
   * @param promptBuilderMap Map of agent IDs to prompt builder functions
   * @param platformStrategy Strategy for platform-specific customizations
   * @param authContext Authentication context for the platform
   * @returns Map of agent IDs to complete AgentConfigInterface objects
   */
  async buildAgentConfigs(param: {
    agentIds: Array<{ id: string; prompt?: string }>;
    promptBuilderMap: Record<string, Array<() => string | Promise<string>>>;
    platformStrategy: PlatformStrategy;
    authContext: AuthContext;
  }): Promise<Record<string, AgentConfig>> {
    const { agentIds, promptBuilderMap, platformStrategy, authContext } = param;
    const agentIdStrings = agentIds.map((agent) => agent.id);
    this.logger.debug(`Building agent configs for ${agentIds.length} agents`, {
      agentIds: agentIdStrings,
      platform: platformStrategy.platform,
    });

    const startTime = Date.now();

    try {
      // Step 1: Execute 4 batch queries in parallel (ZERO additional queries after this)
      this.logger.debug('Executing 4 batch queries in parallel...');
      const [agents, modelData, integrations, mcpConfigs] = await Promise.all([
        this.getAgents(agentIdStrings),
        this.getModelData(agentIdStrings),
        this.getIntegrations(agentIdStrings, platformStrategy, authContext),
        this.getMcpConfigs(agentIdStrings),
      ]);
      this.logger.debug('------------------------------------------------');
      this.logger.debug(JSON.stringify(agents, null, 2));
      this.logger.debug(JSON.stringify(modelData, null, 2));
      this.logger.debug(JSON.stringify(integrations, null, 2));
      this.logger.debug(JSON.stringify(mcpConfigs, null, 2));
      this.logger.debug('------------------------------------------------');
      this.logger.debug('Batch queries completed', {
        agentsCount: agents.length,
        modelDataCount: modelData.length,
        integrationsCount: integrations.length,
        mcpConfigsCount: mcpConfigs.length,
      });

      if (!modelData?.length) {
        this.logger.error('No model data found for any agents');
        throw new Error('No model data found for any agents');
      }

      if (!agents?.length) {
        this.logger.error('No agent data found for any agents');
        throw new Error('No agent data found for any agents');
      }

      if (!integrations?.length) {
        this.logger.error('No integrations found for any agents');
        throw new Error('No integrations found for any agents');
      }

      // Step 2: Enrich agents with prompt data from backend
      const enrichedAgents = agents.map((agent) => {
        const agentWithPrompt = agentIds.find((a) => a.id === agent.id);
        return {
          ...agent,
          prompt: agentWithPrompt?.prompt,
        };
      });

      // Step 3: Extract agent model configs for the mapper
      const agentModelConfigs = this.extractAgentModelConfigs(agents);

      // Step 3: Transform data through builder services (ZERO additional queries)
      this.logger.debug(
        'Starting data transformation through builder services...',
      );
      const modelConfigs = this.modelRegistryMapper.mapModelRegistryData(
        modelData,
        agentModelConfigs,
      );
      const integrationConfigs =
        this.integrationConfigBuilder.buildIntegrationConfigs(
          integrations,
          modelConfigs,
        );
      const mcpConfigMap = this.mcpConfigBuilder.buildMcpConfigs(mcpConfigs);

      this.logger.debug('Data transformation completed', {
        modelConfigsCount: modelConfigs.size,
        integrationConfigsCount: integrationConfigs.size,
        mcpConfigMapCount: mcpConfigMap.size,
      });

      // Step 4: Assemble final configurations
      this.logger.debug('Assembling final agent configurations...');

      const result = await this.agentConfigAssembler.assembleAgentConfigs({
        agents: enrichedAgents,
        modelConfigs,
        integrationConfigs,
        mcpConfigs: mcpConfigMap,
        authContext,
        promptBuilderMap: promptBuilderMap,
        platformStrategy,
      });

      const duration = Date.now() - startTime;
      this.logger.log(
        `Successfully built ${Object.keys(result).length} agent configurations in ${duration}ms`,
      );

      this.logger.debug(JSON.stringify(result, null, 2));
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(
        `Failed to build agent configurations after ${duration}ms: ${error.message}`,
        {
          agentIds,
          platform: platformStrategy.platform,
          error: error.message,
          stack: error.stack,
        },
      );
      throw error;
    }
  }

  /**
   * Query 1: Get agents with TypeAgent data
   *
   * @param agentIds Array of agent IDs
   * @returns Agent data with TypeAgent.type included
   */
  private async getAgents(agentIds: string[]): Promise<AgentRawData[]> {
    this.logger.debug(
      `Executing Query 1: getAgents for ${agentIds.length} agents`,
    );

    const result = await this.agentRepository
      .createQueryBuilder('agent')
      .leftJoin('type_agents', 'typeAgent', 'agent.type_id = typeAgent.id')
      .select([
        'agent.id as id',
        'agent.name as name',
        'agent.instruction as instruction',
        'agent.config as config',
        'agent.model_id as "modelId"',
        'agent.user_id as "userId"',
        'agent.employee_id as "employeeId"',
        'agent.active as active',
        'agent.use_system_key as "useSystemKey"',
        'agent.model_config as "modelConfig"',
        'typeAgent.type as "typeAgentType"',
      ])
      .where('agent.id IN (:...agentIds)', { agentIds })
      .andWhere('agent.active = true')
      .andWhere('agent.deleted_at IS NULL')
      .getRawMany();

    this.logger.debug(`Query 1 completed: found ${result.length} agents`);
    return result;
  }

  /**
   * Query 2: Get model and model registry data
   *
   * @param agentIds Array of agent IDs
   * @returns Model and ModelRegistry data for agents
   */
  private async getModelData(
    agentIds: string[],
  ): Promise<ModelRegistryRawData[]> {
    this.logger.debug(
      `Executing Query 2: getModelData for ${agentIds.length} agents`,
    );

    const result = await this.modelRepository
      .createQueryBuilder('model')
      .leftJoin(
        'model_registry', // Raw table name for the join
        'registry', // Alias for the joined table
        'model.model_registry_id = registry.id', // The raw ON condition
      )
      .leftJoin(
        'agents', // Raw table name for the join
        'agent', // Alias for the joined table
        'model.id = agent.model_id', // The raw ON condition
      )
      .select([
        'agent.id as "agentId"',
        'model.model_id as "modelName"',
        'model.is_fine_tune as "isFineTune"',
        'registry.provider as "provider"',
        'registry.max_tokens as "maxTokens"',
        'registry.context_window as "contextWindow"',
        'registry.input_modalities_base as "inputModalitiesBase"',
        'registry.output_modalities_base as "outputModalitiesBase"',
        'registry.sampling_parameters_base  as "samplingParametersBase"',
        'registry.features_base as "featuresBase"',
        'registry.base_pricing as "basePricing"',
        'registry.input_modalities_fine_tune as "inputModalitiesFineTune"',
        'registry.output_modalities_fine_tune as "outputModalitiesFineTune"',
        'registry.sampling_parameters_fine_tune as "samplingParametersFineTune"',
        'registry.features_fine_tune as "featuresFineTune"',
        'registry.fine_tune_pricing as "fineTunePricing"',
      ])
      .where('agent.id IN (:...agentIds)', { agentIds })
      .getRawMany();

    this.logger.debug(
      `Query 2 completed: found ${result.length} model records`,
    );
    return result;
  }

  /**
   * Query 3: Get integration data with platform-specific filtering logic
   *
   * @param agentIds Array of agent IDs
   * @param platformStrategy Strategy for platform-specific customizations
   * @param authContext Authentication context for the platform
   * @returns Integration data for agents
   */
  private async getIntegrations(
    agentIds: string[],
    platformStrategy: PlatformStrategy,
    authContext: AuthContext,
  ): Promise<IntegrationRawData[]> {
    this.logger.debug(
      `Executing Query 3: getIntegrations for ${agentIds.length} agents`,
      { platform: platformStrategy.platform },
    );

    // Build the base query following the SQL pattern: Agent → Model → ModelIntegration → Integration
    const baseQuery = this.integrationRepository
      .createQueryBuilder('integration')
      .leftJoin(
        'model_integration',
        'modelIntegration',
        'integration.id = modelIntegration.integration_id',
      )
      .leftJoin('models', 'model', 'modelIntegration.model_id = model.id')
      .leftJoin('agents', 'agent', 'agent.model_id = model.id')
      .leftJoin(
        'integration_providers',
        'integrationProvider',
        'integration.type_id = integrationProvider.id',
      )
      .leftJoin(
        'model_registry',
        'modelRegistry',
        'model.model_registry_id = modelRegistry.id',
      )
      .select([
        'agent.id as "agentId"',
        'integration.encrypted_config as "encryptedConfig"',
        'integration.secret_key as "secretKey"',
      ])
      .where('agent.id IN (:...agentIds)', { agentIds })
      .andWhere(
        '"integrationProvider"."type"::text = "modelRegistry"."provider"::text',
      ); // Cast both enums to text for comparison

    // Apply platform-specific filtering logic
    const customizedQuery = platformStrategy.customizeIntegrationQuery(
      baseQuery,
      authContext,
    );
    const result = await customizedQuery.getRawMany();

    this.logger.debug(
      `Query 3 completed: found ${result.length} integration records`,
    );
    return result;
  }

  /**
   * Query 4: Get MCP configuration data
   *
   * @param agentIds Array of agent IDs
   * @returns MCP configuration data for agents
   */
  private async getMcpConfigs(agentIds: string[]): Promise<McpConfigRawData[]> {
    this.logger.debug(
      `Executing Query 4: getMcpConfigs for ${agentIds.length} agents`,
    );

    const query = this.mcpRepository
      .createQueryBuilder('mcp')
      .leftJoin('agents_mcp', 'agentsMcp', 'agentsMcp.mcp_id = mcp.id')
      .select([
        'agentsMcp.agent_id as "agentId"',
        'mcp.name_server as "nameServer"',
        'mcp.config as config',
        'mcp.secret_key as "secretKey"',
        'mcp.headers as "encryptedHeaders"',
        'mcp.description as "description"',
      ])
      .where('agentsMcp.agent_id IN (:...agentIds)', { agentIds });

    const result = await query.getRawMany();
    this.logger.debug(`Query 4 completed: found ${result.length} MCP records`);
    return result;
  }

  /**
   * Extract agent model configs from agent data for the mapper
   *
   * @param agents Agent data from Query 1
   * @returns Map of agent IDs to their model configurations
   */
  private extractAgentModelConfigs(
    agents: AgentRawData[],
  ): Map<string, { modelConfig: ModelConfig; useSystemKey: boolean }> {
    const agentModelConfigs = new Map<
      string,
      { modelConfig: ModelConfig; useSystemKey: boolean }
    >();

    for (const agent of agents) {
      if (agent.modelConfig) {
        agentModelConfigs.set(agent.id, {
          modelConfig: agent.modelConfig,
          useSystemKey: agent.useSystemKey as boolean,
        });
      }
    }

    this.logger.debug(
      `Extracted model configs for ${agentModelConfigs.size} agents`,
    );
    return agentModelConfigs;
  }
}
