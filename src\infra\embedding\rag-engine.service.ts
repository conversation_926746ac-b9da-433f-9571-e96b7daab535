import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { env } from 'src/config';

// Request DTOs
export interface FileSearchQuery {
  query: string;
  threshold?: number; // 0-1, default: 0.7
  limit?: number; // 1-100, default: 10
  use_rerank?: boolean; // default: true
  file_ids: string[];
}

export interface MediaTextSearchQuery {
  query: string;
  threshold?: number; // 0-1, default: 0.7
  limit?: number; // 1-100, default: 10
  use_rerank?: boolean; // default: true
  media_ids: string[];
}

export interface ProductSearchQuery {
  query: string;
  customer_product_ids: number[];
  product_types?: ('physical' | 'digital' | 'event' | 'service')[];
  threshold: number;
  limit: number;
  use_rerank: boolean;
  include_sub_products: boolean;
}


// Result DTOs
export interface FileSearchResult {
  content: string;
  similarity_score: number;
  source_type: string; // 'file' or 'media'
  source_id: string;
  embedding_source: string;
  rerank_score?: number | null;
  rerank_reason?: string | null;
  filename?: string | null;
  file_size?: number | null;
  file_type?: string | null;
  media_name?: string | null;
  media_description?: string | null;
  media_tags?: string[] | null;
  storage_key?: string | null;
  cdn_url?: string | null;
  chunk_metadata?: Record<string, any> | null;
}

export interface MediaTextSearchResult {
  id: string;
  media_id: string;
  content: string;
  similarity: number;
  embedding_source: string;
  name: string;
  storage_key: string;
  rerank_score?: number | null;
  rerank_reason?: string | null;
  description?: string | null;
  tags?: string[] | null;
  media_type?: string | null;
  file_size?: number | null;
  chunk_metadata?: Record<string, any> | null;
  cdn_url?: string | null;
}

export interface ProductSearchResult {
  product_type: string; // physical/digital/event/service
  table_type: string; // main or sub-product type
  id: number;
  similarity: number;
  rerank_score?: number | null;
  rerank_reason?: string | null;
  physical_product_id?: number | null;
  digital_product_id?: number | null;
  event_product_id?: number | null;
  service_product_id?: number | null;
  customer_product_id?: number | null;
  variant_id?: number | null;
  version_id?: number | null;
  ticket_id?: number | null;
  service_package_id?: number | null;
}

// Response DTOs
export interface FileSearchResponse {
  success: boolean;
  message: string;
  query: string;
  results: FileSearchResult[];
  total_found: number;
  file_ids_filter?: string[] | null;
  threshold_used: number;
  rerank_applied?: boolean;
  rerank_method?: string;
  execution_time_ms: number;
  embedding_time_ms?: number | null;
  search_time_ms?: number | null;
  rerank_time_ms?: number | null;
  token_usage?: {
    embedding_tokens: number;
    operation_breakdown: {
      embedding: number;
      rerank: number;
    };
    provider_breakdown: {
      [key: string]: number;
    };
    recognition_tokens: number;
    rerank_tokens: number;
    total_tokens: number;
  };
}

export interface MediaTextSearchResponse {
  success: boolean;
  message: string;
  query: string;
  results: MediaTextSearchResult[];
  total_found: number;
  media_type_filter?: string | null;
  media_ids_filter?: string[] | null;
  threshold_used: number;
  rerank_applied?: boolean;
  rerank_method?: string;
  execution_time_ms: number;
  embedding_time_ms?: number | null;
  search_time_ms?: number | null;
  rerank_time_ms?: number | null;
  media_types_found?: Record<string, number> | null;
  /**
   * "token_usage": {
    "embedding_tokens": 150,
    "operation_breakdown": {
      "embedding": 150,
      "rerank": 75
    },
    "provider_breakdown": {
      "jina": 225
    },
    "recognition_tokens": 0,
    "rerank_tokens": 75,
    "total_tokens": 225
  },
   * 
  */
  token_usage?: {
    embedding_tokens: number;
    operation_breakdown: {
      embedding: number;
      rerank: number;
    };
    provider_breakdown: {
      [key: string]: number;
    };
    recognition_tokens: number;
    rerank_tokens: number;
    total_tokens: number;
  };
}

export interface ProductSearchResponse {
  success: boolean;
  message: string;
  query: string;
  results: ProductSearchResult[];
  recognized_product_types: string[];
  search_strategy: string; // main_only/hierarchical
  total_found: number;
  main_results?: number;
  sub_results?: number;
  physical_results?: number;
  digital_results?: number;
  event_results?: number;
  service_results?: number;
  threshold_used: number;
  limit_used: number;
  rerank_applied?: boolean;
  rerank_method?: string;
  recognition_method: string;
  recognition_skipped?: boolean;
  recognition_tokens_used?: number;
  search_tokens_used?: number;
  rerank_tokens_used?: number;
  total_tokens_used?: number;
  execution_time_ms: number;
  embedding_time_ms?: number | null;
  search_time_ms?: number | null;
  rerank_time_ms?: number | null;
  recognition_time_ms?: number | null;
  search_tables?: string[];
}

@Injectable()
export class RagEngineService {
  private readonly logger = new Logger(RagEngineService.name);
  private readonly baseUrl: string;
  private readonly apiKey: string;
  private readonly defaultThreshold = 0.4; // Default threshold for search queries
  private readonly defaultLimit = 10; // Default limit for search results
  private readonly defaultUseRerank = true; // Default rerank setting

  constructor(private readonly httpService: HttpService) {
    this.baseUrl = env.embedding.EMBEDDING_BASE_URL;
    this.apiKey = env.embedding.EMBEDDING_SECRET_KEY;
  }

  private getHeaders() {
    return {
      'Content-Type': 'application/json',
      'X-RAG-Secret-Key': this.apiKey,
    };
  }

  /**
   * Search content in files using the RAG engine
   * POST /files/search
   */
  async searchFiles(query: FileSearchQuery): Promise<FileSearchResponse> {
    try {
      this.logger.debug(`Searching files with query: ${query.query}`);
      const fullUrl = `${this.baseUrl}/files/search`;
      this.logger.debug(`FULL EMBEDDING URL: ${fullUrl}`);
      const response = await firstValueFrom(
        this.httpService.post(
          fullUrl,
          {
            query: query.query,
            threshold: query.threshold ?? this.defaultThreshold,
            limit: query.limit ?? this.defaultLimit,
            use_rerank: query.use_rerank ?? this.defaultUseRerank,
            file_ids: query.file_ids ?? null,
          },
          {
            headers: this.getHeaders(),
          },
        ),
      );

      this.logger.debug(`File search completed successfully`);
      return response.data;
    } catch (error) {
      this.logger.error(`File search failed: ${error.message}`, error.stack);
      throw new Error(`Failed to search files: ${error.message}`);
    }
  }

  /**
   * Search media content using text query
   * POST /media/search
   */
  async searchMedia(
    query: MediaTextSearchQuery,
  ): Promise<MediaTextSearchResponse> {
    try {
      this.logger.debug(`Searching media with query: ${query.query}`);

      const response = await firstValueFrom(
        this.httpService.post(
          `${this.baseUrl}/media/search`,
          {
            query: query.query,
            threshold: query.threshold ?? 0.7,
            limit: query.limit ?? 10,
            use_rerank: query.use_rerank ?? true,
            media_ids: query.media_ids ?? null,
          },
          {
            headers: this.getHeaders(),
          },
        ),
      );

      this.logger.debug(`Media search completed successfully`);
      return response.data;
    } catch (error) {
      this.logger.error(`Media search failed: ${error.message}`, error.stack);
      throw new Error(`Failed to search media: ${error.message}`);
    }
  }

  /**
   * Search products with AI recognition and hierarchical search
   * POST /products/search/
   */
  async searchProducts(
    query: ProductSearchQuery,
  ): Promise<ProductSearchResponse> {
    try {
      this.logger.debug(`Searching products with query: ${query.query}`);

      const response = await firstValueFrom(
        this.httpService.post(
          `${this.baseUrl}/products/search/`,
          {
            query: query.query,
            product_types: query.product_types ?? null,
            threshold: query.threshold ?? 0.7,
            limit: query.limit ?? 10,
            use_rerank: query.use_rerank ?? true,
            include_sub_products: query.include_sub_products ?? true,
          },
          {
            headers: this.getHeaders(),
          },
        ),
      );

      this.logger.debug(`Product search completed successfully`);
      return response.data;
    } catch (error) {
      this.logger.error(`Product search failed: ${error.message}`, error.stack);
      throw new Error(`Failed to search products: ${error.message}`);
    }
  }
}
