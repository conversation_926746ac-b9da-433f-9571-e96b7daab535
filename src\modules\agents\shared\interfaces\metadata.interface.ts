import { PaymentGatewayStatus } from '../enums';

export interface FacebookPageMetadata {
  active: boolean;
  avatar: string;
  error: boolean;
  personalId: string;
  pageId: string;
}

export interface FacebookPersonalMetadata {
  avatar: string;
  personalId: string;
  expirationDateUnix: number;
  avatarKey?: string; // S3 key của avatar đã upload
}

export interface WebsiteMetadata {
  host: string;
  logo: string;
  active: boolean;
  click: number;
}

export interface EmailServerMetadata {
  serverName: string;
  providerName: string;
  host?: string;
  port?: number;
  useSsl?: boolean;
  useStartTls?: boolean;
  isActive: boolean;
  additionalSettings?: any;
  createdAt: number;
  updatedAt: number;
}

export interface PaymentGatewayMetadata {
  paymentGatewayType: string;
  accountId: string;
  userCompanyInSepayId: string;
  bankCode: string;
  accountNumber: string;
  accountHolderName: string;
  label: string;
  phoneNumber: string;
  status: PaymentGatewayStatus;
  requestId: string;
  isVa: boolean;
  canCreateVa: boolean;
  mainId?: string;
  vaId?: string;
  merchantAddress?: string;
  merchantName?: string;
}

export interface FptSmsMetadata {
  brandName: string;
  apiUrl?: string;

  [key: string]: any;
}

export interface TwilioSmsMetadata {
  accountSid: string;
  phoneNumber?: string;
  messagingServiceSid?: string;

  [key: string]: any;
}

export interface VonageSmsMetadata {
  from?: string;

  [key: string]: any;
}

export interface SpeedSmsMetadata {
  smsType?: number;
  sender?: string;
  apiUrl?: string;

  [key: string]: any;
}

export type Metadata =
  | FacebookPageMetadata
  | FacebookPersonalMetadata
  | WebsiteMetadata
  | EmailServerMetadata
  | PaymentGatewayMetadata
  | FptSmsMetadata
  | TwilioSmsMetadata
  | VonageSmsMetadata
  | SpeedSmsMetadata;
