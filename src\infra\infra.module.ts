import { Global, Module } from '@nestjs/common';
import { RedisService } from './redis';
import { CdnService } from './s3';
import { S3Service } from './s3/s3.service';
import { RagEngineService } from './embedding';
import { HttpModule } from '@nestjs/axios';

@Global()
@Module({
  imports: [HttpModule],
  providers: [RedisService, CdnService, S3Service, RagEngineService],
  exports: [RedisService, CdnService, S3Service, RagEngineService],
})
export class InfraModule {}
