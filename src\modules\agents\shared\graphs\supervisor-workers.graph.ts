import { Injectable, Logger } from '@nestjs/common';
import { tool, ToolRunnableConfig } from '@langchain/core/tools';
import { z } from 'zod';
import { HumanMessage } from '@langchain/core/messages';
import { GraphState, ReactAgentGraph } from './react-agent.graph';
import {
  CompiledStateGraph,
  END,
  START,
  StateGraph,
} from '@langchain/langgraph';
import { checkpointer } from './checkpointer';
import { BaseCallbackHandler } from '@langchain/core/callbacks/base';
import { IterableReadableStream } from '@langchain/core/utils/stream';
import { StreamEvent } from '@langchain/core/dist/tracers/event_stream';
import { AgentConfig, GraphConfigType } from '../interfaces';

export interface SupervisorWorkersGraphConfigType extends GraphConfigType {
  workerAgents?: Record<string, AgentConfig>;
}

@Injectable()
export class SupervisorWorkersGraph {
  private readonly logger = new Logger(SupervisorWorkersGraph.name);
  private readonly workflow: CompiledStateGraph<any, any, any>;

  constructor(private readonly reactAgentGraph: ReactAgentGraph) {
    this.workflow = new StateGraph(GraphState)
      .addNode('agent', this.reactAgentGraph.getWorkflow())
      .addEdge(START, 'agent')
      .addEdge('agent', END)
      .compile({
        checkpointer: checkpointer,
      });
  }

  getWorkflow() {
    return this.workflow;
  }

  /**
   * Prepares the supervisor's config and executes it using the generic ReactAgentGraph.
   */
  executeStream(param: {
    input: any;
    config: SupervisorWorkersGraphConfigType;
    tags: string[]; // inject tags from every platform, not graph dependent
    callbacks: BaseCallbackHandler[]; // callbacks can use external tags for logic, not graph dependent
    signal: AbortSignal;
  }): IterableReadableStream<StreamEvent> {
    const { input, config, tags, callbacks, signal } =
      param;
    const tools: any[] = [];
    if (config.workerAgents) {
      const delegateTool = this.createWorkerTool(config);
      tools.push(delegateTool);
    }
    config.executorAgent.tools = (config.executorAgent.tools || []).concat(tools);
    return this.workflow.streamEvents(input, {
      configurable: config,
      version: 'v2' as const,
      streamMode: ['messages', 'updates'],
      tags,
      callbacks,
      signal,
    });
  }

  private createWorkerTool(
    config: SupervisorWorkersGraphConfigType,
  ): ReturnType<typeof tool> {
    const agentConfigMap = config.workerAgents;
    const toolInfo = {
      name: 'delegate_to_worker',
      description: `Delegate a task to a worker. Available workers: [${Object.keys(agentConfigMap || []).join(', ')}]`,
      schema: z.object({
        workerId: z.string().describe("The worker's ID."),
        taskDescription: z
          .string()
          .describe('The detailed task description for the worker.'),
      }),
    };
    const handler = async (
      { workerId, taskDescription }: z.infer<typeof toolInfo.schema>,
      toolRunnableConfig: ToolRunnableConfig<SupervisorWorkersGraphConfigType>,
    ) => {
      const config =
        toolRunnableConfig.configurable as SupervisorWorkersGraphConfigType;
      const parentThreadId = config.thread_id || 'main';
      const scopedThreadId = `worker:${parentThreadId}:${workerId}`;

      if (!agentConfigMap?.[workerId]) {
        return `Error: Worker with ID '${workerId}' not found.`;
      }
      const workerConfig: GraphConfigType = {
        executorAgent: agentConfigMap?.[workerId] as AgentConfig,
        thread_id: scopedThreadId,
        checkpoint_id: config.checkpoint_id,
        alwaysApproveToolCall: config.alwaysApproveToolCall,
        platform: config.platform,
        currentUser: config.currentUser,
      };

      const agentType = agentConfigMap?.[workerId]?.type;
      const tags = [`agent:${workerId}:${agentType}`, 'subprocess'];
      const workerFinalState = await this.reactAgentGraph.executeInvoke(
        { messages: [new HumanMessage(taskDescription)] },
        workerConfig,
        tags,
      );
      const lastMessage =
        workerFinalState.messages[workerFinalState.messages.length - 1];
      return typeof lastMessage.content === 'string'
        ? lastMessage.content
        : lastMessage.content.join('\n');
    };
    return tool(handler, toolInfo);
  }
}
