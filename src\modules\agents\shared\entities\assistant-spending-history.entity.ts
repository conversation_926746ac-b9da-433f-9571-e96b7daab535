import {
  Column,
  <PERSON>ti<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
  <PERSON>To<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Agent } from './agent.entity';
import { AssistantSpendingType } from '../enums';

/**
 * Entity đại diện cho bảng assistant_spending_history trong cơ sở dữ liệu
 * <PERSON><PERSON><PERSON> lịch sử chi tiêu điểm của trợ lý AI
 */
@Entity('assistant_spending_history')
export class AssistantSpendingHistory {
  /**
   * ID định danh duy nhất dạng UUID cho mỗi bản ghi chi tiêu
   */
  @PrimaryGeneratedColumn('uuid') id: string;

  /**
   * UUID của AI agent đã thực hiện chi tiêu
   */
  @Column({
    name: 'agent_id',
    type: 'uuid',
    nullable: false,
    comment: 'UUID của AI agent đã thực hiện chi tiêu',
  })
  agentId: string;
  /**
   * Số điểm đã chi tiêu trong lần sử dụng
   */
  @Column({
    name: 'point',
    type: 'bigint',
    nullable: false,
    comment: 'Số điểm đã chi tiêu trong lần sử dụng',
  })
  point: number;

  /**
   * Thời điểm chi tiêu, lưu dưới dạng Unix timestamp (miligiây)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '((EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000)::BIGINT)',
    comment: 'Thời điểm chi tiêu, lưu dưới dạng Unix timestamp (miligiây)',
  })
  createdAt: number;

  /**
   * Loại chi tiêu assistant
   */
  @Column({
    name: 'type',
    type: 'enum',
    enum: AssistantSpendingType,
    nullable: true,
    comment: 'Loại chi tiêu assistant',
  })
  type: AssistantSpendingType | null;

  /**
   * ID của model được sử dụng
   */
  @Column({
    name: 'model_id',
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'ID của model được sử dụng',
  })
  modelId: string | null;

  /**
   * ID của user thực hiện chi tiêu
   */
  @Column({
    name: 'user_id',
    type: 'integer',
    nullable: true,
    comment: 'ID của user thực hiện chi tiêu',
  })
  userId: number | null;
}
