import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { InternalConversationMessage } from '../entities';
import { MessageRole } from '../../../shared/enums';
import {
  AttachmentType,
  KnowledgeFileWithMessageId,
  MediaDataWithMessageId,
  ReplyToMessageData,
  ReplyToMessagesContext,
} from '../../../shared/interfaces';

/**
 * Message Operations Service
 *
 * Handles database operations for InternalConversationMessage entities.
 * Provides clean abstraction for message CRUD operations and message processing workflows.
 *
 * Key Features:
 * - Message text updates (for partial text finalization)
 * - Message retrieval and validation
 * - Unprocessed message fetching and processing
 * - Reply-to message context building
 * - Proper error handling and logging
 * - Type-safe repository operations
 *
 * Status: ✅ Extended for Phase 2.1
 * - Message text update functionality
 * - Repository-based operations
 * - Comprehensive error handling
 * - Phase 1.1 message management methods integrated
 */
@Injectable()
export class InternalMessageOperationsService {
  private readonly logger = new Logger(InternalMessageOperationsService.name);

  constructor(
    @InjectRepository(InternalConversationMessage)
    private readonly messageRepository: Repository<InternalConversationMessage>,
  ) {}

  /**
   * Update message text content
   * Used for finalizing partially accumulated text from token collectors
   *
   * @param messageId - Message ID to update
   * @param text - New text content
   * @param context - Additional context for logging
   * @returns True if update was successful, false otherwise
   */
  async updateMessageText(
    messageId: string,
    text: string,
    context?: {
      runId?: string;
      agentId?: string;
      operation?: string;
    },
  ): Promise<boolean> {
    try {
      if (!text.trim()) {
        this.logger.debug(
          `Cannot update message ${messageId} with empty text`,
          {
            messageId,
            textLength: text.length,
            ...context,
          },
        );
        return false;
      }

      const result = await this.messageRepository.update(
        { id: messageId },
        { text: text.trim() },
      );

      if (result.affected === 0) {
        this.logger.warn(`No message found with ID ${messageId} to update`, {
          messageId,
          textLength: text.length,
          ...context,
        });
        return false;
      }

      this.logger.debug(`Successfully updated message ${messageId} text`, {
        messageId,
        textLength: text.length,
        textPreview: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
        ...context,
      });

      return true;
    } catch (error) {
      this.logger.error(`Failed to update message ${messageId} text:`, {
        messageId,
        textLength: text.length,
        error: error.message,
        stack: error.stack,
        ...context,
      });
      return false;
    }
  }

  /**
   * Get message by ID
   * Utility method for message retrieval and validation
   *
   * @param messageId - Message ID to retrieve
   * @returns Message entity or null if not found
   */
  async getMessageById(
    messageId: string,
  ): Promise<InternalConversationMessage | null> {
    try {
      const message = await this.messageRepository.findOne({
        where: { id: messageId },
      });

      if (!message) {
        this.logger.debug(`Message not found: ${messageId}`);
        return null;
      }

      return message;
    } catch (error) {
      this.logger.error(`Failed to get message ${messageId}:`, {
        messageId,
        error: error.message,
        stack: error.stack,
      });
      return null;
    }
  }

  /**
   * Check if message exists
   * Quick validation method
   *
   * @param messageId - Message ID to check
   * @returns True if message exists, false otherwise
   */
  async messageExists(messageId: string): Promise<boolean> {
    try {
      const count = await this.messageRepository.count({
        where: { id: messageId },
      });

      return count > 0;
    } catch (error) {
      this.logger.error(`Failed to check if message ${messageId} exists:`, {
        messageId,
        error: error.message,
      });
      return false;
    }
  }

  /**
   * Update message with validation
   * Updates message text only if message exists
   *
   * @param messageId - Message ID to update
   * @param text - New text content
   * @param context - Additional context for logging
   * @returns True if update was successful, false if message not found or update failed
   */
  async updateMessageTextWithValidation(
    messageId: string,
    text: string,
    context?: {
      runId?: string;
      agentId?: string;
      operation?: string;
    },
  ): Promise<boolean> {
    // First check if message exists
    const exists = await this.messageExists(messageId);
    if (!exists) {
      this.logger.warn(`Cannot update non-existent message: ${messageId}`, {
        messageId,
        textLength: text.length,
        ...context,
      });
      return false;
    }

    // Update the message
    return await this.updateMessageText(messageId, text, context);
  }

  /**
   * Append text to existing message content
   * Useful for incremental text updates
   *
   * @param messageId - Message ID to update
   * @param additionalText - Text to append
   * @param separator - Separator between existing and new text (default: '')
   * @param context - Additional context for logging
   * @returns True if update was successful, false otherwise
   */
  async appendToMessageText(
    messageId: string,
    additionalText: string,
    separator: string = '',
    context?: {
      runId?: string;
      agentId?: string;
      operation?: string;
    },
  ): Promise<boolean> {
    try {
      const message = await this.getMessageById(messageId);
      if (!message) {
        return false;
      }

      const currentText = message.text || '';
      const newText = currentText + separator + additionalText;

      return await this.updateMessageText(messageId, newText, {
        ...context,
        operation: 'append',
      });
    } catch (error) {
      this.logger.error(`Failed to append to message ${messageId}:`, {
        messageId,
        additionalTextLength: additionalText.length,
        error: error.message,
        ...context,
      });
      return false;
    }
  }

  // ============================================================================
  // MESSAGE PROCESSING METHODS (Phase 1.1 Integration)
  // ============================================================================

  /**
   * Fetch unprocessed user messages for a thread
   * Phase 1.1.1: Extracted from InAppAiProcessor and moved to service layer
   *
   * @param threadId - Thread ID to fetch messages for
   * @returns Array of unprocessed user messages ordered by creation date
   */
  async fetchUnprocessedUserMessages(
    threadId: string,
  ): Promise<InternalConversationMessage[]> {
    try {
      const messages = await this.messageRepository.find({
        where: {
          threadId: threadId,
          role: MessageRole.USER,
          processed: false, // Only get unprocessed messages
        },
        order: {
          createdAt: 'ASC',
        },
      });

      this.logger.debug(
        `Fetched ${messages.length} unprocessed user messages for thread ${threadId}`,
        {
          threadId,
          messageCount: messages.length,
          messageIds: messages.map((m) => m.id),
        },
      );

      return messages;
    } catch (error) {
      this.logger.error(
        `Failed to fetch unprocessed user messages for thread ${threadId}:`,
        {
          threadId,
          error: error.message,
          stack: error.stack,
        },
      );
      return [];
    }
  }

  /**
   * Fetch reply-to messages by their IDs
   * Phase 1.1.2: Extracted from InAppAiProcessor and moved to service layer
   *
   * @param replyToMessageIds - Array of message IDs to fetch
   * @returns Array of messages that are being replied to
   */
  async fetchReplyToMessages(
    replyToMessageIds: string[],
  ): Promise<InternalConversationMessage[]> {
    try {
      if (!replyToMessageIds?.length) {
        this.logger.debug('No reply-to message IDs provided');
        return [];
      }

      const messages = await this.messageRepository.find({
        where: {
          id: In(replyToMessageIds),
        },
      });

      this.logger.debug(`Fetched ${messages.length} reply-to messages`, {
        requestedIds: replyToMessageIds,
        foundCount: messages.length,
        foundIds: messages.map((m) => m.id),
      });

      return messages;
    } catch (error) {
      this.logger.error(`Failed to fetch reply-to messages:`, {
        replyToMessageIds,
        error: error.message,
        stack: error.stack,
      });
      return [];
    }
  }

  /**
   * Mark messages as processed
   * Phase 1.1.3: Extracted from InAppAiProcessor and moved to service layer
   *
   * @param messageIds - Array of message IDs to mark as processed
   * @param threadId - Thread ID for logging context
   * @returns True if update was successful, false otherwise
   */
  async markMessagesAsProcessed(
    messageIds: string[],
    threadId: string,
  ): Promise<boolean> {
    try {
      if (!messageIds?.length) {
        this.logger.debug('No message IDs provided to mark as processed', {
          threadId,
        });
        return true;
      }

      const result = await this.messageRepository.update(
        { id: In(messageIds) },
        { processed: true },
      );

      this.logger.debug('Marked messages as processed', {
        threadId,
        messageIds,
        messageCount: messageIds.length,
        affectedRows: result.affected,
      });

      return (result.affected ?? 0) > 0;
    } catch (error) {
      this.logger.error(`Failed to mark messages as processed:`, {
        threadId,
        messageIds,
        error: error.message,
        stack: error.stack,
      });
      return false;
    }
  }

  /**
   * Build reply-to messages context with their attachments
   * Phase 1.1.4: Extracted from InAppAiProcessor and moved to service layer
   *
   * @param replyToMessages - Array of reply-to message entities
   * @param replyToSpecificImages - Array of images attached to reply-to messages
   * @param replyToSpecificKnowledgeFiles - Array of knowledge files attached to reply-to messages
   * @returns Structured context for reply-to messages with attachments
   */
  async buildReplyToMessagesContext(
    replyToMessages: InternalConversationMessage[],
    replyToSpecificImages: MediaDataWithMessageId[],
    replyToSpecificKnowledgeFiles: KnowledgeFileWithMessageId[],
  ): Promise<ReplyToMessagesContext> {
    try {
      const replyToMessagesContext: ReplyToMessagesContext = [];

      for (const replyToMessage of replyToMessages) {
        const replyToMessageData: ReplyToMessageData = {
          text: replyToMessage.text,
          attachments: {
            images: replyToSpecificImages.map((media) => {
              return {
                id: media.id,
                name: media.name,
                description: media.description,
                tags: media.tags,
                storageKey: media.storageKey,
                type: AttachmentType.IMAGE,
              };
            }),
            knowledgeFiles: replyToSpecificKnowledgeFiles.map((kf) => {
              return {
                id: kf.id,
                name: kf.name,
                fileId: kf.fileId,
                type: AttachmentType.KNOWLEDGE_FILE,
              };
            }),
          },
        };
        replyToMessagesContext.push(replyToMessageData);
      }

      this.logger.debug(`Built reply-to messages context`, {
        messageCount: replyToMessages.length,
        contextCount: replyToMessagesContext.length,
        imageCount: replyToSpecificImages.length,
        knowledgeFileCount: replyToSpecificKnowledgeFiles.length,
      });

      return replyToMessagesContext;
    } catch (error) {
      this.logger.error(`Failed to build reply-to messages context:`, {
        messageCount: replyToMessages.length,
        imageCount: replyToSpecificImages.length,
        knowledgeFileCount: replyToSpecificKnowledgeFiles.length,
        error: error.message,
        stack: error.stack,
      });
      return [];
    }
  }
}
