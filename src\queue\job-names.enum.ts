/**
 * Enum định nghĩa các tên job trong queue Zalo ZNS
 */
export enum ZaloZnsJobName {
  /**
   * Job gửi ZNS đơn lẻ
   */
  SEND_ZNS = 'send-zns',

  /**
   * Job gửi ZNS theo chiến dịch
   */
  SEND_ZNS_CAMPAIGN = 'send-zns-campaign',

  /**
   * Job gửi batch ZNS (multiple messages)
   */
  SEND_BATCH_ZNS = 'send-batch-zns',
}

/**
 * Enum định nghĩa các tên job trong queue Email Marketing
 */
export enum EmailMarketingJobName {
  /**
   * Job gửi email marketing (single email)
   */
  SEND_EMAIL = 'send-email',

  /**
   * Job gửi batch email marketing (multiple emails)
   */
  SEND_BATCH_EMAIL = 'send-batch-email',
}

/**
 * Enum định nghĩa các tên job trong queue SMS
 */
export enum SmsJobName {
  /**
   * Job gửi SMS thông thường
   */
  SEND_SMS = 'send-sms',

  /**
   * Job gửi SMS theo mẫu
   */
  SEND_TEMPLATE_SMS = 'send-template-sms',

  /**
   * Job gửi SMS hệ thống
   */
  SMS_SYSTEM = 'sms-system',

  /**
   * Job gửi SMS marketing
   */
  SMS_MARKETING = 'sms-marketing',
}

/**
 * Enum định nghĩa các tên job trong queue Affiliate
 */
export enum AffiliateJobName {
  /**
   * Job xử lý batch click affiliate
   */
  AFFILIATE_CLICK_BATCH = 'affiliate-click-batch',
}

/**
 * Enum định nghĩa các tên job trong queue Webhook
 */
export enum WebhookJobName {
  /**
   * Job trừ dung lượng user addon usage
   */
  DEDUCT_USER_ADDON_USAGE = 'deduct-user-addon-usage',

  /**
   * Job xử lý analytics
   */
  PROCESS_ANALYTICS = 'process-analytics',

  /**
   * Job xử lý Zalo webhook event (Legacy)
   */
  PROCESS_ZALO_WEBHOOK = 'process-zalo-webhook',

  // =============================================================================
  // NEW ZALO QUEUE SYSTEM JOB NAMES
  // =============================================================================

  // Real-time jobs (High Priority)
  /**
   * Job xử lý user messages (text, image, audio, video, file, etc.)
   */
  PROCESS_USER_MESSAGE = 'process-user-message',

  /**
   * Job xử lý user interactions (reactions, click chatnow, etc.)
   */
  PROCESS_USER_INTERACTION = 'process-user-interaction',

  /**
   * Job xử lý follow/unfollow events
   */
  PROCESS_FOLLOW_EVENT = 'process-follow-event',

  // Business logic jobs (Medium Priority)
  /**
   * Job xử lý shop orders
   */
  PROCESS_ORDER = 'process-order',

  /**
   * Job xử lý user info submissions/updates
   */
  PROCESS_USER_INFO = 'process-user-info',

  /**
   * Job xử lý user feedback
   */
  PROCESS_FEEDBACK = 'process-feedback',

  /**
   * Job xử lý call events (OA call user, user call OA)
   */
  PROCESS_CALL_EVENT = 'process-call-event',

  /**
   * Job xử lý consent events
   */
  PROCESS_CONSENT = 'process-consent',

  // Analytics jobs (Low Priority)
  /**
   * Job tracking message status (received, seen)
   */
  TRACK_MESSAGE_STATUS = 'track-message-status',

  /**
   * Job tracking OA messages
   */
  TRACK_OA_MESSAGE = 'track-oa-message',

  /**
   * Job tracking interactions
   */
  TRACK_INTERACTION = 'track-interaction',

  // Background jobs (Lowest Priority)
  /**
   * Job xử lý template events (quota, status changes)
   */
  PROCESS_TEMPLATE_EVENT = 'process-template-event',

  /**
   * Job xử lý system events (widgets, permissions, extensions)
   */
  PROCESS_SYSTEM_EVENT = 'process-system-event',

  /**
   * Job xử lý group management events
   */
  PROCESS_GROUP_MANAGEMENT = 'process-group-management',
}

export enum GraphJobName {
  SUPERVISOR_WORKERS = 'supervisor-workers',
  PLANNER_EXECUTOR = 'planner-executor',
}