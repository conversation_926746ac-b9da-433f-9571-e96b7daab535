import { Entity, PrimaryColumn } from 'typeorm';

/**
 * Entity cho bảng type_agent_models - <PERSON><PERSON><PERSON><PERSON> lý mối quan hệ many-to-many giữa type agent và model registry
 */
@Entity('type_agent_models')
export class TypeAgentModels {
  /**
   * ID của type agent
   */
  @PrimaryColumn({ name: 'type_agent_id', type: 'integer' })
  typeAgentId: number;

  /**
   * ID của model registry
   */
  @PrimaryColumn({ name: 'model_registry_id', type: 'uuid' })
  modelRegistryId: string;
}
