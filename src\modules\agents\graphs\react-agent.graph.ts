import { Injectable, Logger } from '@nestjs/common';
import {
  Annotation,
  BinaryOperatorAggregate,
  Command,
  CompiledStateGraph,
  END,
  interrupt,
  messagesStateReducer,
  START,
  StateDefinition,
  StateGraph,
  StateType,
  UpdateType,
} from '@langchain/langgraph';
import {
  BaseMessage,
  isAIMessage,
  RemoveMessage,
  SystemMessage,
  ToolMessage,
} from '@langchain/core/messages';
import { GraphConfigType } from '../runtime';
import { RunnableConfig } from '@langchain/core/runnables';
import { env } from '../../../config';

import { ModelService } from './services/model.service';
import { backOff } from 'exponential-backoff';
import { TrimService } from './services/trim.service';
import { AgentConfig } from '../interfaces';
import { ToolNode } from '@langchain/langgraph/prebuilt';
import { IterableReadableStream } from '@langchain/core/dist/utils/stream';
import { StreamEvent } from '@langchain/core/dist/tracers/event_stream';
import { EncryptionService } from 'src/shared/services/encryption/encryption.service';

export const GraphState = Annotation.Root({
  messages: Annotation<BaseMessage[], BaseMessage[]>({
    reducer: messagesStateReducer,
    default: () => [],
  }),
});

export type ReactAgentStateType = (typeof GraphState)['State'];

export type ReactAgentConfig = RunnableConfig<GraphConfigType>;

@Injectable()
export class ReactAgentGraph {
  private readonly logger = new Logger(ReactAgentGraph.name);
  private readonly workflow: CompiledStateGraph<
    StateType<{
      messages: BinaryOperatorAggregate<BaseMessage[], BaseMessage[]>;
    }>,
    UpdateType<{
      messages: BinaryOperatorAggregate<BaseMessage[], BaseMessage[]>;
    }>,
    '__start__' | 'agent' | 'humanReview' | 'tools' | 'trim' | 'removeImage',
    {
      messages: BinaryOperatorAggregate<BaseMessage[], BaseMessage[]>;
    },
    {
      messages: BinaryOperatorAggregate<BaseMessage[], BaseMessage[]>;
    },
    StateDefinition
  >;

  constructor(
    private readonly encryptionService: EncryptionService,
    private readonly modelService: ModelService,
    private readonly trimService: TrimService,
  ) {
    this.workflow = new StateGraph(GraphState)
      .addNode('agent', this.callModel.bind(this))
      .addNode('humanReview', this.humanReview.bind(this))
      .addNode('tools', this.callTool.bind(this))
      .addNode('trim', this.trimMessages.bind(this))
      .addNode('removeImage', this.removeImage.bind(this))
      .addEdge(START, 'trim')
      .addEdge('trim', 'agent')
      .addConditionalEdges('agent', this.router, {
        humanReview: 'humanReview',
        tools: 'tools',
        removeImage: 'removeImage',
      })
      .addEdge('removeImage', END)
      .addEdge('tools', 'agent')
      .compile();
  }

  executeStream(
    input: any,
    configurable: GraphConfigType,
    tags?: string[],
  ): IterableReadableStream<StreamEvent> {
    return this.workflow.streamEvents(input, {
      configurable,
      version: 'v2',
      streamMode: ['messages', 'updates'],
      tags,
    });
  }

  async executeInvoke(
    input: any,
    configurable: GraphConfigType,
    tags?: string[],
  ): Promise<ReactAgentStateType> {
    return await this.workflow.invoke(input, {
      configurable,
      tags,
    });
  }

  getWorkflow() {
    return this.workflow;
  }

  private async removeImage(
    state: ReactAgentStateType,
    _: ReactAgentConfig,
  ): Promise<Partial<ReactAgentStateType>> {
    const { messages } = state;
    const imageMessages = messages
      .filter((message) => {
        return Array.isArray(message.content)
          ? message.content.some((content) => content.type === 'image_url')
          : false;
      })
      .map((message) => new RemoveMessage({ id: message.id as string }));
    return {
      messages: imageMessages,
    };
  }

  private async trimMessages(
    state: ReactAgentStateType,
    config: ReactAgentConfig,
  ): Promise<Partial<ReactAgentStateType>> {
    return this.trimService.trim(state, config);
  }

  private async callModel(
    state: ReactAgentStateType,
    config: ReactAgentConfig,
  ): Promise<Partial<ReactAgentStateType>> {
    this.logger.debug(`calling model`);

    const agentConfig = config?.configurable?.executorAgent as AgentConfig;
    const { model } = agentConfig;
    try {
      const systemPrompt = await agentConfig.toPrompt();

      const existingMessages = state.messages || [];
      const newMessages = [
        new SystemMessage(systemPrompt),
        ...existingMessages,
      ];

      const { encryptedApiKeyPairs } = model;
      let currentKeyIndex = 0;
      const apiCall = async () => {
        this.logger.debug(`API call attempt`, {
          currentKeyIndex,
          encryptedPairExists: !!encryptedApiKeyPairs[currentKeyIndex],
          publicKey: encryptedApiKeyPairs[currentKeyIndex]?.publicKey,
          encryptedContentLength:
            encryptedApiKeyPairs[currentKeyIndex]?.encryptedContent?.length,
          encryptedContentFormat:
            encryptedApiKeyPairs[currentKeyIndex]?.encryptedContent,
          privateKeyExists: !!env.keyPairEncryption.KEY_PAIR_PRIVATE_KEY,
          agentId: agentConfig.id,
          modelName: model.name,
        });
        const encryptedPair = encryptedApiKeyPairs[currentKeyIndex];
        const { apiKey } = this.encryptionService.decrypt<{ apiKey: string }>(
          encryptedPair.publicKey,
          env.keyPairEncryption.KEY_PAIR_PRIVATE_KEY,
          encryptedPair.encryptedContent,
        );
        if (!apiKey) {
          throw new Error(`no api key found`);
        }
        await agentConfig.mcpClient?.initializeConnections();
        const tools = [
          ...((await agentConfig.mcpClient?.getTools()) || []),
          ...(agentConfig.tools || []),
        ];
        const llm = await this.modelService.createModelWithTools({
          modelName: model.name,
          modelProvider: model.provider,
          modelParameters: model.parameters,
          apiKey,
          tools,
          toolChoices: config?.configurable?.toolChoices,
        });
        let totalTokens = 0;
        // Use the new message array
        const result = await llm.invoke(newMessages, config);
        this.logger.debug(`API call successful`, {
          currentKeyIndex,
          totalTokens,
          agentId: agentConfig.id,
          modelName: model.name,
          result,
        });
        return {
          messages: [result],
        };
      };
      return await backOff(apiCall, {
        numOfAttempts: encryptedApiKeyPairs.length,
        startingDelay: 200,
        timeMultiple: 1.5,
        retry: (error: any) => {
          this.logger.error(`API call failed: ${error.message}`);
          if (currentKeyIndex >= encryptedApiKeyPairs.length) {
            this.logger.error('All API keys have been tried and failed.');
            return false;
          }
          ++currentKeyIndex;
          this.logger.log(`Retrying with key #${currentKeyIndex}`);
          return true;
        },
      });
    } catch (e) {
      this.logger.error(`Error calling model: ${e.message}`, e.stack);
      throw e;
    } finally {
      await agentConfig.mcpClient?.close();
    }
  }

  private async humanReview(
    state: ReactAgentStateType,
    config: ReactAgentConfig,
  ): Promise<Command | Partial<ReactAgentStateType>> {
    const { messages } = state;
    const lastMessage = messages[messages.length - 1];

    if (!lastMessage || !isAIMessage(lastMessage)) {
      throw new Error('Last message is not an AI message');
    }
    const aiMessage = lastMessage;

    const erroredToolCalls =
      aiMessage.tool_calls?.filter(
        (toolCall) =>
          !toolCall.name ||
          toolCall.name.trim() === '' ||
          !toolCall.id ||
          toolCall.id.trim() === '',
      ) || [];

    if (erroredToolCalls.length > 0) {
      throw new Error(
        `Found ${erroredToolCalls.length} errored tool calls: ${JSON.stringify(erroredToolCalls)}. Tool calls must have both name and id.`,
      );
    }

    if (config.configurable?.alwaysApproveToolCall) {
      return new Command({
        goto: 'tools',
      });
    }

    const toolCalls =
      aiMessage.tool_calls
        ?.map((toolCall, index) => {
          return `Tool ${index + 1}: ${toolCall.name}\nArguments: ${JSON.stringify(toolCall.args, null, 2)}`;
        })
        .join('\n\n') || '';
    const { choice } = interrupt(
      `The AI wants to use the following tools:\n\n${toolCalls}\n\nDo you approve these tool calls? (yes/no/always)`,
    );
    if (choice === 'always' || choice === 'yes') {
      return new Command({
        goto: 'tools',
      });
    } else {
      const rejectionMessages =
        aiMessage.tool_calls?.map((toolCall) => {
          return new ToolMessage({
            content: 'Tool call was rejected by the user.',
            tool_call_id: toolCall.id ?? '',
            name: toolCall.name,
          });
        }) || [];
      return new Command({
        update: {
          messages: rejectionMessages,
        },
        goto: 'agent',
      });
    }
  }

  private async callTool(
    state: ReactAgentStateType,
    config: ReactAgentConfig,
  ): Promise<Partial<ReactAgentStateType> | any> {
    const { messages } = state;
    const lastMessage = messages[messages.length - 1];
    if (!lastMessage) {
      throw new Error('No messages found');
    }
    if (isAIMessage(lastMessage)) {
      const aiMessage = lastMessage;
      if (aiMessage.tool_calls && aiMessage.tool_calls.length > 0) {
        const erroredToolCalls = aiMessage.tool_calls.filter(
          (toolCall) =>
            !toolCall.name ||
            toolCall.name.trim() === '' ||
            !toolCall.id ||
            toolCall.id.trim() === '',
        );
        if (erroredToolCalls.length > 0) {
          throw new Error(
            `Found ${erroredToolCalls.length} errored tool calls: ${JSON.stringify(erroredToolCalls)}. Tool calls must have both name and id.`,
          );
        }
      }
    }
    const agentConfig = config.configurable?.executorAgent as AgentConfig;
    if (!agentConfig) {
      throw new Error('No agent config found');
    }
    try {
      await agentConfig.mcpClient?.initializeConnections();
      const tools = [
        ...((await agentConfig.mcpClient?.getTools()) || []),
        ...(agentConfig.tools || []),
      ];
      const dynamicToolNode = new ToolNode(tools, {
        handleToolErrors: true,
      });
      const raw = await dynamicToolNode.invoke(state, config);

      const output: any[] = [];
      if (Array.isArray(raw)) {
        output.push(...raw);
      } else if (raw instanceof Command) {
        output.push(raw);
      } else if (Array.isArray(raw.messages)) {
        output.push(...raw.messages);
      } else {
        throw new Error('wrappedToolNode: unexpected return shape');
      }
      const hasCommand = output.some((item) => item instanceof Command);
      if (hasCommand) {
        return output;
      }
      const messages: BaseMessage[] = [];
      for (const item of output) {
        if (item instanceof BaseMessage) {
          messages.push(item);
        }
      }
      return { messages };
    } catch (e) {
      this.logger.error(`Error calling tool: ${e.message}`, e.stack);
      throw e;
    } finally {
      await agentConfig.mcpClient?.close();
    }
  }

  private router(state: ReactAgentStateType, config: ReactAgentConfig) {
    const { messages } = state;
    const lastMessage = messages[messages.length - 1];
    if (isAIMessage(lastMessage)) {
      const aiMessage = lastMessage;
      if (!aiMessage.tool_calls || aiMessage.tool_calls.length === 0) {
        return 'removeImage';
      }
      const alwaysApproveToolCall =
        config?.configurable?.alwaysApproveToolCall || false;
      return alwaysApproveToolCall ? 'tools' : 'humanReview';
    }
    return END;
  }
}
