/**
 * @file Conditional Connection Service
 * 
 * Service để xử lý conditional routing logic cho workflow engine.
 * Hỗ trợ IF nodes, SWITCH nodes và conditional dependencies.
 * 
 * @version 1.0.0
 * <AUTHOR> Assistant
 */

import { Injectable, Logger } from '@nestjs/common';
import { Connection } from '../../../entities/connection.entity';
import { Node } from '../../../entities/node.entity';
import { DetailedNodeExecutionResult } from '../types';
import {
  IEnhancedConnection,
  IConditionalDependency,
  IConditionalDependencyGraph,
  IConnectionActivationResult,
  INodeReadinessResult,
  IConditionalConnectionMetadata,
  EConditionalNodeType,
  EConnectionActivationRule,
  NODE_TYPE_HANDLE_MAPPING
} from '../../../interfaces/conditional-connection.interface';

/**
 * Service để xử lý conditional connections và routing logic
 */
@Injectable()
export class ConditionalConnectionService {
  private readonly logger = new Logger(ConditionalConnectionService.name);

  /**
   * Analyze connections và tạo enhanced connections với conditional metadata
   */
  analyzeConnections(
    nodes: Node[], 
    connections: Connection[]
  ): IEnhancedConnection[] {
    const nodeTypeMap = new Map<string, EConditionalNodeType>();
    
    // Map node types
    nodes.forEach(node => {
      nodeTypeMap.set(node.id, this.getNodeConditionalType(node));
    });

    return connections.map(connection => {
      const sourceNodeType = nodeTypeMap.get(connection.sourceNodeId) || EConditionalNodeType.NORMAL;
      const isConditional = sourceNodeType !== EConditionalNodeType.NORMAL;

      const enhancedConnection: IEnhancedConnection = {
        ...connection,
        isConditional,
        conditionalMetadata: isConditional ? 
          this.createConditionalMetadata(connection, sourceNodeType) : 
          undefined,
        isActive: !isConditional // Non-conditional connections are always active
      };

      return enhancedConnection;
    });
  }

  /**
   * Build conditional dependency graph
   */
  buildConditionalDependencyGraph(
    nodes: Node[],
    enhancedConnections: IEnhancedConnection[]
  ): IConditionalDependencyGraph {
    const basicDependencies = new Map<string, string[]>();
    const conditionalDependencies = new Map<string, IConditionalDependency[]>();
    const connectionsBySource = new Map<string, IEnhancedConnection[]>();
    const connectionsByTarget = new Map<string, IEnhancedConnection[]>();

    // Initialize maps
    nodes.forEach(node => {
      basicDependencies.set(node.id, []);
      conditionalDependencies.set(node.id, []);
      connectionsBySource.set(node.id, []);
      connectionsByTarget.set(node.id, []);
    });

    // Process connections
    enhancedConnections.forEach(connection => {
      // Add to source/target maps
      connectionsBySource.get(connection.sourceNodeId)?.push(connection);
      connectionsByTarget.get(connection.targetNodeId)?.push(connection);

      if (connection.isConditional && connection.conditionalMetadata) {
        // Add conditional dependency
        const conditionalDep: IConditionalDependency = {
          sourceNodeId: connection.sourceNodeId,
          targetNodeId: connection.targetNodeId,
          connection,
          activationCondition: {
            sourceHandle: connection.sourceHandle,
            rule: connection.conditionalMetadata.activationRule,
            expectedValue: connection.conditionalMetadata.expectedValue
          }
        };

        conditionalDependencies.get(connection.targetNodeId)?.push(conditionalDep);
      } else {
        // Add basic dependency
        const targetDeps = basicDependencies.get(connection.targetNodeId) || [];
        if (!targetDeps.includes(connection.sourceNodeId)) {
          targetDeps.push(connection.sourceNodeId);
          basicDependencies.set(connection.targetNodeId, targetDeps);
        }
      }
    });

    return {
      basicDependencies,
      conditionalDependencies,
      connectionsBySource,
      connectionsByTarget
    };
  }

  /**
   * Check if connection should be activated based on source node result
   */
  shouldActivateConnection(
    connection: IEnhancedConnection,
    sourceNodeResult: DetailedNodeExecutionResult
  ): IConnectionActivationResult {
    if (!connection.isConditional || !connection.conditionalMetadata) {
      return {
        shouldActivate: true,
        reason: 'Non-conditional connection always active'
      };
    }

    const metadata = connection.conditionalMetadata;
    const result = this.evaluateActivationRule(
      metadata.activationRule,
      connection.sourceHandle,
      sourceNodeResult,
      metadata.expectedValue
    );

    return {
      shouldActivate: result.shouldActivate,
      reason: result.reason,
      sourceNodeResult,
      matchedCondition: result.matchedCondition
    };
  }

  /**
   * Check if node is ready for execution considering conditional dependencies
   */
  checkNodeReadiness(
    nodeId: string,
    dependencyGraph: IConditionalDependencyGraph,
    completedNodes: Map<string, DetailedNodeExecutionResult>
  ): INodeReadinessResult {
    const basicDeps = dependencyGraph.basicDependencies.get(nodeId) || [];
    const conditionalDeps = dependencyGraph.conditionalDependencies.get(nodeId) || [];

    const satisfiedDependencies: string[] = [];
    const unsatisfiedDependencies: string[] = [];
    const conditionalDependenciesStatus: any[] = [];

    // Check basic dependencies
    basicDeps.forEach(depId => {
      if (completedNodes.has(depId)) {
        satisfiedDependencies.push(depId);
      } else {
        unsatisfiedDependencies.push(depId);
      }
    });

    // Check conditional dependencies
    conditionalDeps.forEach(conditionalDep => {
      const sourceResult = completedNodes.get(conditionalDep.sourceNodeId);
      
      if (!sourceResult) {
        unsatisfiedDependencies.push(conditionalDep.sourceNodeId);
        conditionalDependenciesStatus.push({
          nodeId: conditionalDep.sourceNodeId,
          isActive: false,
          reason: 'Source node not completed'
        });
        return;
      }

      const activationResult = this.shouldActivateConnection(
        conditionalDep.connection,
        sourceResult
      );

      if (activationResult.shouldActivate) {
        satisfiedDependencies.push(conditionalDep.sourceNodeId);
        conditionalDependenciesStatus.push({
          nodeId: conditionalDep.sourceNodeId,
          isActive: true,
          reason: activationResult.reason
        });
      } else {
        // Conditional dependency not active - this is OK, not unsatisfied
        conditionalDependenciesStatus.push({
          nodeId: conditionalDep.sourceNodeId,
          isActive: false,
          reason: activationResult.reason
        });
      }
    });

    const isReady = unsatisfiedDependencies.length === 0;

    return {
      isReady,
      satisfiedDependencies,
      unsatisfiedDependencies,
      conditionalDependenciesStatus
    };
  }

  /**
   * Get active connections from a source node based on its execution result
   */
  getActiveConnections(
    sourceNodeId: string,
    sourceNodeResult: DetailedNodeExecutionResult,
    dependencyGraph: IConditionalDependencyGraph
  ): IEnhancedConnection[] {
    const connections = dependencyGraph.connectionsBySource.get(sourceNodeId) || [];
    
    return connections.filter(connection => {
      const activationResult = this.shouldActivateConnection(connection, sourceNodeResult);
      return activationResult.shouldActivate;
    });
  }

  // Private helper methods

  private getNodeConditionalType(node: Node): EConditionalNodeType {
    // Determine node type from parameters or other properties
    // Since Node entity doesn't have direct type property, we need to infer it
    const params = node.parameters as any;

    // Check for IF_CONDITION node characteristics
    if (params && (params.condition || params.conditionType || params.ifCondition)) {
      return EConditionalNodeType.IF_CONDITION;
    }

    // Check for SWITCH node characteristics
    if (params && (params.cases || params.switchValue || params.switchCondition)) {
      return EConditionalNodeType.SWITCH;
    }

    // Check for LOOP node characteristics
    if (params && (params.loopType || params.iterationCondition || params.breakCondition)) {
      return EConditionalNodeType.LOOP;
    }

    // Check for WAIT node characteristics
    if (params && (params.waitType || params.duration || params.waitUntil)) {
      return EConditionalNodeType.WAIT;
    }

    // Check for HTTP_REQUEST node characteristics
    if (params && (params.method || params.url || params.httpMethod)) {
      return EConditionalNodeType.HTTP_REQUEST;
    }

    return EConditionalNodeType.NORMAL;
  }

  private createConditionalMetadata(
    connection: Connection,
    nodeType: EConditionalNodeType
  ): IConditionalConnectionMetadata {
    const activationRule = this.getActivationRuleForHandle(
      nodeType,
      connection.sourceHandle
    );

    return {
      nodeType,
      activationRule,
      expectedValue: this.getExpectedValueForHandle(nodeType, connection.sourceHandle),
      isDefaultPath: connection.sourceHandle === 'default',
      sourceHandleMapping: {
        handle: connection.sourceHandle,
        condition: this.getConditionForHandle(nodeType, connection.sourceHandle)
      }
    };
  }

  private getActivationRuleForHandle(
    nodeType: EConditionalNodeType,
    sourceHandle: string
  ): EConnectionActivationRule {
    switch (nodeType) {
      case EConditionalNodeType.IF_CONDITION:
        return sourceHandle === 'true' ? 
          EConnectionActivationRule.ON_TRUE : 
          EConnectionActivationRule.ON_FALSE;
      
      case EConditionalNodeType.SWITCH:
        return sourceHandle === 'default' ?
          EConnectionActivationRule.ON_DEFAULT :
          EConnectionActivationRule.ON_VALUE_MATCH;
      
      default:
        return EConnectionActivationRule.ALWAYS;
    }
  }

  private getExpectedValueForHandle(nodeType: EConditionalNodeType, sourceHandle: string): any {
    switch (nodeType) {
      case EConditionalNodeType.IF_CONDITION:
        return sourceHandle === 'true' ? true : false;
      case EConditionalNodeType.SWITCH:
        return sourceHandle === 'default' ? null : sourceHandle;
      default:
        return null;
    }
  }

  private getConditionForHandle(nodeType: EConditionalNodeType, sourceHandle: string): any {
    return this.getExpectedValueForHandle(nodeType, sourceHandle);
  }

  private evaluateActivationRule(
    rule: EConnectionActivationRule,
    sourceHandle: string,
    sourceNodeResult: DetailedNodeExecutionResult,
    expectedValue?: any
  ): { shouldActivate: boolean; reason: string; matchedCondition?: any } {
    switch (rule) {
      case EConnectionActivationRule.ALWAYS:
        return { shouldActivate: true, reason: 'Always active connection' };

      case EConnectionActivationRule.ON_TRUE:
        const isTrue = this.extractBooleanResult(sourceNodeResult);
        return {
          shouldActivate: isTrue === true,
          reason: `IF condition result: ${isTrue}`,
          matchedCondition: isTrue
        };

      case EConnectionActivationRule.ON_FALSE:
        const isFalse = this.extractBooleanResult(sourceNodeResult);
        return {
          shouldActivate: isFalse === false,
          reason: `IF condition result: ${isFalse}`,
          matchedCondition: isFalse
        };

      case EConnectionActivationRule.ON_VALUE_MATCH:
        const switchValue = this.extractSwitchResult(sourceNodeResult);
        const matches = switchValue === expectedValue;
        return {
          shouldActivate: matches,
          reason: `Switch value ${switchValue} ${matches ? 'matches' : 'does not match'} expected ${expectedValue}`,
          matchedCondition: switchValue
        };

      case EConnectionActivationRule.ON_DEFAULT:
        // Default path activates when no other paths match
        return {
          shouldActivate: true, // This logic should be handled at higher level
          reason: 'Default path activation'
        };

      case EConnectionActivationRule.ON_CONTINUE:
        const loopContinue = this.extractLoopResult(sourceNodeResult);
        return {
          shouldActivate: loopContinue.control === 'continue',
          reason: `Loop control: ${loopContinue.control}`,
          matchedCondition: loopContinue.control
        };

      case EConnectionActivationRule.ON_BREAK:
        const loopBreak = this.extractLoopResult(sourceNodeResult);
        return {
          shouldActivate: loopBreak.control === 'break',
          reason: `Loop control: ${loopBreak.control}`,
          matchedCondition: loopBreak.control
        };

      case EConnectionActivationRule.ON_COMPLETE:
        const loopComplete = this.extractLoopResult(sourceNodeResult);
        return {
          shouldActivate: loopComplete.control === 'complete',
          reason: `Loop control: ${loopComplete.control}`,
          matchedCondition: loopComplete.control
        };

      case EConnectionActivationRule.ON_TIMEOUT:
        const waitTimeout = this.extractWaitResult(sourceNodeResult);
        return {
          shouldActivate: waitTimeout.status === 'timeout' || waitTimeout.status === 'truncated',
          reason: `Wait status: ${waitTimeout.status}`,
          matchedCondition: waitTimeout.status
        };

      case EConnectionActivationRule.ON_SUCCESS:
        // Handle both WAIT and HTTP_REQUEST nodes
        if (sourceNodeResult.nodeType === 'WAIT') {
          const waitSuccess = this.extractWaitResult(sourceNodeResult);
          return {
            shouldActivate: waitSuccess.status === 'completed',
            reason: `Wait status: ${waitSuccess.status}`,
            matchedCondition: waitSuccess.status
          };
        } else if (sourceNodeResult.nodeType === 'HTTP_REQUEST') {
          const httpSuccess = this.extractHttpResult(sourceNodeResult);
          return {
            shouldActivate: httpSuccess.success && httpSuccess.statusCode >= 200 && httpSuccess.statusCode < 400,
            reason: `HTTP status: ${httpSuccess.statusCode}, success: ${httpSuccess.success}`,
            matchedCondition: httpSuccess.statusCode
          };
        }
        return { shouldActivate: false, reason: 'Unsupported node type for ON_SUCCESS' };

      case EConnectionActivationRule.ON_SKIP:
        const waitSkip = this.extractWaitResult(sourceNodeResult);
        return {
          shouldActivate: waitSkip.status === 'skipped',
          reason: `Wait status: ${waitSkip.status}`,
          matchedCondition: waitSkip.status
        };

      case EConnectionActivationRule.ON_ERROR:
        const httpError = this.extractHttpResult(sourceNodeResult);
        return {
          shouldActivate: !httpError.success || httpError.statusCode >= 400,
          reason: `HTTP status: ${httpError.statusCode}, success: ${httpError.success}`,
          matchedCondition: httpError.statusCode
        };

      case EConnectionActivationRule.ON_STATUS_CODE:
        const httpStatus = this.extractHttpResult(sourceNodeResult);
        const expectedStatusCode = parseInt(expectedValue as string) || 200;
        return {
          shouldActivate: httpStatus.statusCode === expectedStatusCode,
          reason: `HTTP status: ${httpStatus.statusCode}, expected: ${expectedStatusCode}`,
          matchedCondition: httpStatus.statusCode
        };

      default:
        return { shouldActivate: false, reason: 'Unknown activation rule' };
    }
  }

  private extractBooleanResult(nodeResult: DetailedNodeExecutionResult): boolean {
    // For IF_CONDITION nodes
    if (nodeResult.nodeType === 'IF_CONDITION' && nodeResult.outputData) {
      return (nodeResult.outputData as any).result === true;
    }
    return false;
  }

  private extractSwitchResult(nodeResult: DetailedNodeExecutionResult): any {
    // For SWITCH nodes
    if (nodeResult.nodeType === 'SWITCH' && nodeResult.outputData) {
      return (nodeResult.outputData as any).selectedValue;
    }
    return null;
  }

  private extractLoopResult(nodeResult: DetailedNodeExecutionResult): {
    control: 'continue' | 'break' | 'complete';
    iterations: number;
    breakConditionMet: boolean;
    maxIterationsReached: boolean;
  } {
    // For LOOP nodes
    if (nodeResult.nodeType === 'LOOP' && nodeResult.outputData) {
      const outputData = nodeResult.outputData as any;

      // Determine loop control based on output
      let control: 'continue' | 'break' | 'complete' = 'complete';

      if (outputData.breakConditionMet) {
        control = 'break';
      } else if (outputData.maxIterationsReached) {
        control = 'break';
      } else if (outputData.iterations > 0) {
        control = 'complete';
      }

      return {
        control,
        iterations: outputData.iterations || 0,
        breakConditionMet: outputData.breakConditionMet || false,
        maxIterationsReached: outputData.maxIterationsReached || false
      };
    }

    return {
      control: 'complete',
      iterations: 0,
      breakConditionMet: false,
      maxIterationsReached: false
    };
  }

  private extractWaitResult(nodeResult: DetailedNodeExecutionResult): {
    status: 'completed' | 'skipped' | 'timeout' | 'truncated' | 'cancelled' | 'error';
    actualWaitTime: number;
    plannedWaitTime: number;
    timeoutReached: boolean;
  } {
    // For WAIT nodes
    if (nodeResult.nodeType === 'WAIT' && nodeResult.outputData) {
      const outputData = nodeResult.outputData as any;
      const metadata = nodeResult.metadata as any;

      return {
        status: outputData.wait_metadata?.status || 'completed',
        actualWaitTime: outputData.wait_metadata?.actual_wait_time || metadata?.actualWaitTime || 0,
        plannedWaitTime: outputData.wait_metadata?.planned_wait_time || metadata?.plannedWaitTime || 0,
        timeoutReached: outputData.wait_metadata?.timed_out || metadata?.timeoutReached || false
      };
    }

    return {
      status: 'completed',
      actualWaitTime: 0,
      plannedWaitTime: 0,
      timeoutReached: false
    };
  }

  private extractHttpResult(nodeResult: DetailedNodeExecutionResult): {
    success: boolean;
    statusCode: number;
    statusText: string;
    responseTime: number;
    method: string;
    url: string;
  } {
    // For HTTP_REQUEST nodes
    if (nodeResult.nodeType === 'HTTP_REQUEST' && nodeResult.outputData) {
      const outputData = nodeResult.outputData as any;
      const metadata = nodeResult.metadata as any;

      return {
        success: outputData.success || false,
        statusCode: outputData.status_code || metadata?.statusCode || 0,
        statusText: outputData.status_text || 'Unknown',
        responseTime: outputData.response_time || metadata?.responseTime || 0,
        method: metadata?.httpMethod || 'UNKNOWN',
        url: outputData.final_url || metadata?.finalUrl || 'unknown'
      };
    }

    return {
      success: false,
      statusCode: 0,
      statusText: 'Unknown',
      responseTime: 0,
      method: 'UNKNOWN',
      url: 'unknown'
    };
  }
}
