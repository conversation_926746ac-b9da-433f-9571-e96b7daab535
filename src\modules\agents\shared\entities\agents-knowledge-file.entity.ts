import { Entity, PrimaryColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng agents_knowledge_file trong cơ sở dữ liệu
 * Bảng trung gian lưu quan hệ many-to-many giữa agents và knowledge_files
 */
@Entity('agents_knowledge_file')
export class AgentsKnowledgeFile {
  /**
   * ID của agent
   * L<PERSON> một phần của khóa chính kết hợp
   * Tham chiếu đến bảng agents
   */
  @PrimaryColumn({ name: 'agent_id', type: 'uuid' }) agentId: string;

  /**
   * ID của knowledge file
   * Là một phần của khóa chính kết hợp
   * Tham chiếu đến bảng knowledge_files
   */
  @PrimaryColumn({ name: 'file_id', type: 'uuid' }) fileId: string;
}
